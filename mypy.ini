[mypy]
strict = True
files = .
plugins = pydantic.mypy
allow_subclassing_any = True
allow_untyped_decorators = True


[mypy-aredis_om.*]
ignore_missing_imports = True

[mypy-json_logic.*]
ignore_missing_imports = True

[mypy-fastapi_controllers.*]
ignore_missing_imports = True

[mypy-firebase_admin.*]
ignore_missing_imports = True

[mypy-sklearn.neural_network.*]
ignore_missing_imports = True

[mypy-geojson.*]
ignore_missing_imports = True

[mypy-redlock]
ignore_missing_imports = True

[mypy-apscheduler.*]
ignore_missing_imports = True

[mypy-google.cloud.*]
ignore_missing_imports = True

[mypy-aioredlock.*]
ignore_missing_imports = True

[mypy-polyline.*]
ignore_missing_imports = True

[mypy-dash-proto.*]
ignore_missing_imports = True