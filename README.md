# Dash Hailing Backend

This backend handles driver location tracking, hail requests, and the geo-spatial querying and matching of drivers with riders.

## How to prepare the local environment

1. Install python with homebrew if not already installed
   ```bash
   brew install python@3.12
   ```
2. Set up virtual environment
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   ```
3. Install pipx if not already installed
   ```bash
   brew install pipx
   pipx ensurepath
   ```
4. Install [Poetry](https://python-poetry.org/docs/) if you haven't already.
   ```bash
   pipx install poetry
   ```
   Then set up such that it will use the virtual env
   ```bash
   poetry config virtualenvs.in-project true
   ```
5. Add private registry requirements for the dash_proto package.
   ```bash
   export CLOUDSDK_PYTHON=/usr/local/bin/python3 (on ubuntu/linux, try use 'export CLOUDSDK_PYTHON=/usr/bin/python3')
   gcloud config set project dash-dev-81bb1
   poetry config http-basic.dash oauth2accesstoken $(gcloud auth print-access-token)
   ```
6. Install dependencies.
   ```bash
   poetry install --no-root
   ```

## How to run locally in development

1. [Prepare the local environment.](#how-to-prepare-the-local-environment)
2. Install the [gcloud cli](https://cloud.google.com/sdk/docs/install) if not already installed and then authenticate and set default project
   ```bash
   gcloud auth login
   gcloud config set project dash-dev2-edcb3
   ```
3. Authenticate with gcloud cli and start a redis-forwarder, mapping the GCP Redis instance to your local redis port.
   ```bash
   gcloud compute ssh redis-forwarder --zone=asia-east2-a -- -N -L 6379:10.116.113.12:6379
   ```
4. Optionally install redis cli in order to test connection. After running monitor, you should see live redis command activity.
   ```bash
   brew install redis
   redis-cli
   monitor
   ```
5. Run the backend project in dev mode.
   ```bash
   poetry run uvicorn app.main:app --reload
   ```

## how to build and run the docker image locally

1. Build the docker image
   ```bash
   docker build --platform linux/x86_64 --no-cache -t dash-hailing-backend .
   ```
2. Authenticate with gcloud cli and start a redis-forwarder, mapping the GCP Redis instance to your local redis port.
   ```bash
   gcloud compute ssh redis-forwarder -- -N -L 6379:************:6379
   ```
3. Run the docker image
   ```bash
   # Read the firebase config from a file into an environment variable
   json=`cat ./firebase_local.json`
   docker run -p 8000:8080 \
      -e ENV=local \
      -e REDIS_OM_URL="redis://host.docker.internal:6379" \
      -e FIREBASE_CONFIG=$json \
      -e DASH_API_URL="https://api.dev2.dash-hk.com" \
      -e PROJECT_ID="dash-dev2-edcb3" \
   dash-hailing-backend
   ```

## Load testing

The load tests are written using the Locust load testing tool and are integrated into the CD pipeline to run post-deployment in the development environment. It can also be run locally with a web UI component in order to easily see the statistics, charts, and exceptions.

### How to run load tests locally

1. [Prepare the local environment.](#how-to-prepare-the-local-environment)
2. Run a load test using the cli tool by referencing one of the [test files](tests/load/) and one of the [load shapes](tests/load/load_shapes/) comma separated.
   ```bash
   locust -f tests/load/flow_test_combined.py,tests/load/load_shapes/smoke_load.py --host=https://api.dev.dash-hk.com --ws-host=wss://api.dev.dash-hk.com
   ```
3. Open the UI in the browser at the provided url (This is usually http://0.0.0.0:8089) and press "START".

## Considerations

- This project is deployed as a horizontally distributed stateless server in GCP cloud run. Because each instance is maintaining it's own group of socket connections, REDIS and its PubSub feature is being used to synchronize data between them.
- Redis' PubSub should be used to publish changes that require checking all server instances for ownership of the socket, and emitting the required messages
- Redis in GCP MemoryStore is missing some features that may not be immediately apparent. JSON model types are not supported so only flattened hash tables and stringified complex objects can be used with redis-om. Boolean types are also not supported, so converting of boolean types to int between dto layers is required.

## Note

protobuf issue, forced protobuf to 3.20.3 to fix the issue for now

```bash
  File "/Users/<USER>/Documents/vismo/repo/tmp/hail-backend/.venv/lib/python3.12/site-packages/google/protobuf/descriptor.py", line 621, in __new__
    _message.Message._CheckCalledFromGeneratedFile()
TypeError: Descriptors cannot be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).
```
