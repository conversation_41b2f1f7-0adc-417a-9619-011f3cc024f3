# HTTP Client Global Timeout Configuration

This document explains how to configure longer timeouts for `httpx.AsyncClient` globally in your FastAPI application.

## Configuration Added

### 1. Settings Configuration
Added `http_timeout` setting to `app/infrastructure/settings.py`:
```python
http_timeout: float = 30.0  # Default 30 seconds timeout
```

### 2. Environment Variable
Added to `.env` file:
```bash
HTTP_TIMEOUT=30.0
```

### 3. HTTP Client Utility
Created `app/utils/http_client.py` with helper functions:
- `create_http_client(timeout=None)` - Creates configured AsyncClient
- `create_http_timeout(timeout=None)` - Creates configured Timeout object

## Usage Examples

### Basic Usage (Recommended)
```python
from app.utils.http_client import create_http_client

# Uses global timeout setting (30 seconds)
async with create_http_client() as client:
    response = await client.get("https://api.example.com")
```

### Custom Timeout
```python
from app.utils.http_client import create_http_client

# Override with custom timeout (60 seconds)
async with create_http_client(timeout=60.0) as client:
    response = await client.post("https://slow-api.example.com")
```

### Manual Configuration
```python
import httpx
from app.infrastructure.settings import settings

# Manual approach using settings
async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=settings.http_timeout)) as client:
    response = await client.get("https://api.example.com")
```

## Services Updated

The following services have been updated to use the new HTTP client configuration:

1. **TxService** (`app/services/tx_service.py`)
   - Changed from `httpx.AsyncClient()` to `create_http_client()`

2. **FleetQuoteService** (`app/services/fleet_quote_service.py`)
   - Changed from `httpx.AsyncClient(timeout=httpx.Timeout(timeout=15.0))` to `create_http_client(timeout=15.0)`

## Benefits

1. **Centralized Configuration**: All HTTP timeouts controlled from one place
2. **Environment-Specific**: Different timeouts for dev/staging/prod environments
3. **Backwards Compatible**: Existing services can still specify custom timeouts
4. **Consistent**: Ensures all HTTP clients have reasonable default timeouts
5. **Configurable**: Easy to adjust without code changes via environment variables

## Advanced Configuration

You can extend `create_http_client()` to include other global configurations:

```python
def create_http_client(timeout: float | None = None) -> httpx.AsyncClient:
    effective_timeout = timeout or settings.http_timeout
    
    return httpx.AsyncClient(
        timeout=httpx.Timeout(timeout=effective_timeout),
        follow_redirects=True,
        limits=httpx.Limits(
            max_keepalive_connections=5, 
            max_connections=10
        ),
        headers={
            "User-Agent": "YourApp/1.0",
        }
    )
```

## Environment Variables

Add these to your environment files as needed:

```bash
# Development
HTTP_TIMEOUT=30.0

# Production (longer timeout for external APIs)
HTTP_TIMEOUT=60.0

# Local development (shorter timeout for faster debugging)
HTTP_TIMEOUT=10.0
```
