name: hail-ci

on:
  push:
    branches-ignore:
      - qa
      - dev
      - main
      - dev2

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  PYTHON_VERSION: 3.12
  GCP_SA_DEV_KEY_JSON: gcp_key.json

jobs:
  mypy:
    name: My<PERSON>y
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up virtualenv
        run: python3 -m venv .venv

      - name: Set up Poetry
        run: curl -sSL https://install.python-poetry.org | python3 -

      - name: Copy GCP credentials
        run: echo '${{ secrets.GCP_SA_DEV_KEY }}' | base64 --decode > ${{ env.GCP_SA_DEV_KEY_JSON }}

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          poetry config virtualenvs.in-project true
          poetry config http-basic.dash oauth2accesstoken $(gcloud auth print-access-token)
          poetry install --no-root
        env:
          GCP_SA_DEV_KEY: ${{ secrets.GCP_SA_DEV_KEY }}
          CLOUDSDK_AUTH_CREDENTIAL_FILE_OVERRIDE: ${{ env.GCP_SA_DEV_KEY_JSON }}

      - name: Run MyPy
        run: |
          source .venv/bin/activate
          mypy .
