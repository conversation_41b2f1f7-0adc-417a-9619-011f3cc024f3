name: hail-cd

on:
  push:
    branches: [dev, dev2, qa, main]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  PYTHON_VERSION: 3.12
  ARTIFACT_IMAGE: asia-east2-docker.pkg.dev/${{ vars.PROJECT_ID }}/gcf-artifacts/hail-backend:latest

jobs:
  mypy:
    environment: ${{ github.ref_name }}
    name: MyPy Test
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up virtualenv
        run: python3 -m venv .venv

      - name: Set up Poetry
        run: curl -sSL https://install.python-poetry.org | python3 -

      - name: Copy GCP credentials
        run: echo '${{ secrets.GCP_SA_KEY }}' | base64 --decode > gcp_key.json

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          poetry config virtualenvs.in-project true
          poetry config http-basic.dash oauth2accesstoken $(gcloud auth print-access-token)
          poetry install --no-root
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          CLOUDSDK_AUTH_CREDENTIAL_FILE_OVERRIDE: gcp_key.json

      - name: Run MyPy
        run: |
          source .venv/bin/activate
          mypy .

  deploy:
    environment: ${{ github.ref_name }}
    name: Deploy
    needs: [mypy]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up kubectl
        uses: azure/setup-kubectl@v1
        with:
          version: "v1.29.2"

      - name: Copy GCP credentials
        run: echo '${{ secrets.GCP_SA_KEY }}' | base64 --decode > gcp_key.json

      - name: Set up gcloud with gke-gcloud-auth-plugin
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          install_components: "gke-gcloud-auth-plugin"

      - name: Build image
        run: |
          ACCESS_TOKEN=$(gcloud auth print-access-token)
          docker build --build-arg ACCESS_TOKEN=$ACCESS_TOKEN --platform linux/x86_64 --progress=plain --no-cache -t hailing-backend .
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          CLOUDSDK_AUTH_CREDENTIAL_FILE_OVERRIDE: gcp_key.json

      - name: Push image to GCP artifact registry
        run: |
          gcloud auth configure-docker asia-east2-docker.pkg.dev
          docker tag hailing-backend ${{ env.ARTIFACT_IMAGE }}
          docker push ${{ env.ARTIFACT_IMAGE }}
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          CLOUDSDK_AUTH_CREDENTIAL_FILE_OVERRIDE: gcp_key.json

      - name: Deploy update to GKE
        run: |
          gcloud auth activate-service-account --key-file=gcp_key.json
          gcloud container clusters get-credentials hailing-cluster --region=asia-east2 --project=${{ vars.PROJECT_ID }}
          kubectl get deployments -n hailing-namespace
          kubectl set env deployment hailing-deployment ENV=${{ vars.ENV }} REDIS_OM_URL=${{ vars.REDIS_OM_URL }} DASH_API_URL=${{ vars.DASH_API_URL }} PROJECT_ID=${{ vars.PROJECT_ID }} SENTRY_DSN=${{ vars.SENTRY_DSN }} VALHALLA_URL=${{vars.VALHALLA_URL}} X_API_KEY=${{ secrets.X_API_KEY }} -n hailing-namespace
          kubectl set image deployment hailing-deployment hailing-container=${{ env.ARTIFACT_IMAGE }} -n hailing-namespace
          kubectl rollout restart deployment/hailing-deployment -n hailing-namespace
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          CLOUDSDK_AUTH_CREDENTIAL_FILE_OVERRIDE: gcp_key.json

  # load-test:
  #   if: github.ref == 'refs/heads/dev2'
  #   environment: ${{ github.ref_name }}
  #   name: Load Test
  #   needs: [deploy]
  #   runs-on: ubuntu-latest

  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Set up Python
  #       uses: actions/setup-python@v5
  #       with:
  #         python-version: ${{ env.PYTHON_VERSION }}

  #     - name: Set up virtualenv
  #       run: python3 -m venv .venv

  #     - name: Set up Poetry
  #       run: curl -sSL https://install.python-poetry.org | python3 -

  #     - name: Install dependencies
  #       run: |
  #         source .venv/bin/activate
  #         poetry config virtualenvs.in-project true
  #         poetry install --no-root

  #     - name: Smoke test - Combined flow
  #       run: |
  #         source .venv/bin/activate
  #         locust -f ./tests/load/flow_test_combined.py,tests/load/load_shapes/smoke_load.py --headless --host=${{ vars.DASH_API_URL }} --ws-host=${{ vars.DASH_WS_URL }}
