from decimal import Decimal
from pydantic import Field
from app.contracts.base_contract import BaseContract


class MerchantCampaign(BaseContract):
    campaign_id: str | None = None
    bonus_amount: Decimal = Field(default=Decimal(0), ge=0, examples=[5.00])

    @staticmethod
    def create_example() -> "MerchantCampaign":
        return MerchantCampaign(
            campaign_id="example_campaign_id",
            bonus_amount=10.0,
        )
