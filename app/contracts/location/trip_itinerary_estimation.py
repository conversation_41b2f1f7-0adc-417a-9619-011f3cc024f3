from pydantic import Field

from app.contracts.base_contract import BaseContract
from app.contracts.common.pin import Pin


class PlaceDetails(Pin):
    display_name: str = Field(examples=["Lee Garden 3"], max_length=255)
    place_id: str = Field(examples=["h9238fh9eyf298"])
    formatted_address: str = Field(examples=["1 Hau Fook St, Tsim Sha Tsui, Hong Kong"])


class TripItineraryEstimation(BaseContract):
    distance_meters: int = Field(ge=0, examples=[1000])
    duration_seconds: int = Field(ge=0, examples=[600])
    encoded_polyline: str | None = Field(
        None,
        examples=[
            "so~fCml|wTo@Pr@lCl@dCrAzEnBnIz@|DUFaBkI[?}@\\qA\\KF{JdDK?sCx@Us@Kq@@]OeAU}@?WJQvAi@LMFUO{CFq@Py@Pg@F_@Ti@B{@SeA_AuDY{@[m@mE_GWc@Yw@Mm@e@{DWsA_@aBK_@[m@m@w@y@q@c@Uo@Qo@IuDUqB_@uAe@iCmAc@McCiAm@]i@_@cAaAwEsFg@o@qCeEeAiBw@kBSq@c@cB[wBM_AWeCw@gGEyB?eBG{Aq@cGcAwHM}ACoCF{BNc@lCwMvAmHXiAPi@v@aBbGqJlBuDp@uAlAsD`@s@h@k@XKd@Ml@Cp@Np@ZPL\\^TLTf@FTDx@A|@In@Wr@o@l@YPm@Pu@D]Em@Q_@W]c@w@eBsr@ubBg@mA{B}EiAuCw@yA_AkAcA}Ao@yAwA{Dg@kAYk@[Mi@g@eAo@YBi@@QFYVOZC~@Hp@ZrBHJJ`BDbCCfBBnAHn@Rd@\\^TF^@XEPIT_@Da@OoBSmAYcAcHcOuA}C_AmCe@_C]_DKoCEwBHwCZuDXaBvB}GpU{j@lCsEbHqHr@q@nAuAfB{BR[@Qn@cBBi@Ki@M[SU_@WuD_AaAKo@EKCkXGoO@gCAMDw@uDw@{CIc@Wy@aA}DE]wCiMMe@VKx@QvAO"
        ],
    )
    origin_place_details: PlaceDetails
    destination_place_details: PlaceDetails
