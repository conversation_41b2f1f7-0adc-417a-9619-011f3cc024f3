from decimal import Decimal
from app.contracts.base_contract import BaseContract
from app.contracts.matrix.hail_option_matrix_v2 import HailVehicleClassOptionV2
from app.models.vehicle_class import VehicleClass
from pydantic import Field

class FleetVehicleClassOption(BaseContract):
    vehicle_class: str
    vehicle_class_name: str
    vehicle_class_name_tc: str
    vehicle_class_description: str
    vehicle_class_description_tc: str
    fleet_vehicle_id: str
    fleet_vehicle_type: str
    fleet_partner_key: str
    seats_count: int 
    base_fare: Decimal
    luggage_count: int
    fleet_icon_url: str | None = None
    vehicle_icon_url: str | None = None
    is_include_wheel_chair: bool = False

    @staticmethod
    def create_example() -> "FleetVehicleClassOption":
        return FleetVehicleClassOption(
            vehicle_class=str(VehicleClass.FOUR_SEATER),
            fleet_vehicle_id="123456789",
            fleet_vehicle_type="E-MPT",
            fleet_partner_key="SYNCAB",
            vehicle_class_name="Four Seater",
            vehicle_class_name_tc="四座車",
            vehicle_class_description="Four Seats in a Car",
            vehicle_class_description_tc="四座車",
            seats_count=4,
            luggage_count=2,
            base_fare=Decimal(100),
            is_include_wheel_chair=False,
        )


class FleetQuoteResponse(BaseContract):
    vehicle_class_options: list[FleetVehicleClassOption] = Field(alias="vehicleClassOptions")