from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.contracts.common.heart_beat import HeartBeat


class UpdateHeartBeatRequest(BaseContract):
    hail_id: str
    phone_number: str = Field(examples=["+85212345678"])
    heart_beat: HeartBeat

    @staticmethod
    def create_example() -> "UpdateHeartBeatRequest":
        return UpdateHeartBeatRequest(
            phone_number="+85212345678",
            heart_beat=HeartBeat.create_example(),
        )
