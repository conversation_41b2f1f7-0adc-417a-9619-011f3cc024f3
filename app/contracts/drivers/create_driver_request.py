from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.contracts.common.heart_beat import HeartBeat
from app.models.service_filters import ServiceFilters


class CreateDriverSessionRequest(BaseContract):
    name: str = <PERSON>(examples=["<PERSON>"], max_length=255)
    name_tc: str = Field(examples=["老夫子"], max_length=20)
    phone_number: str = Field(examples=["+85212345678"])
    license_plate: str = Field(examples=["DASH02T"])
    filter_matrix: ServiceFilters
    heart_beat: HeartBeat | None = None

    @staticmethod
    def create_example() -> "CreateDriverSessionRequest":
        return CreateDriverSessionRequest(
            name="<PERSON>",
            name_tc="老夫子",
            license_plate="AAAA",
            phone_number="+85298775424",
            filter_matrix=ServiceFilters.create_example(),
            heart_beat=HeartBeat.create_example(),
        )
