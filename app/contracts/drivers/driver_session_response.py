from datetime import datetime
from uuid import uuid4

from app.models.meter_operating_area import MeterOperatingArea
from pydantic import Field

from app.contracts.base_contract import BaseContract
from app.models.driver_status import DriverStatus
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class import VehicleClass


class DriverSessionResponse(BaseContract):
    status: DriverStatus = Field(examples=[DriverStatus.AVAILABLE])
    driver_id: str = Field(examples=["+85291234567"])
    name: str = Field(examples=["<PERSON> Se<PERSON>feld"])
    name_tc: str = Field(examples=["老夫子"])
    phone_number: str = Field(examples=["+85291234567"])
    license_plate: str = Field(examples=["ABC123"])
    is_dash_meter: bool = Field(examples=[False])
    fleet_id: str | None = Field(examples=[str(uuid4())])
    vehicle_id: str = Field(examples=[str(uuid4())])
    vehicle_make: str = Field(examples=["Toyota"])
    vehicle_model: str = Field(examples=["Corolla"])
    vehicle_class: VehicleClass = Field(examples=[VehicleClass.COMFORT])
    seats: int = Field(examples=[4])
    filter_matrix: ServiceFilters | None
    created_at: datetime
    updated_at: datetime
    operating_area: MeterOperatingArea | None
