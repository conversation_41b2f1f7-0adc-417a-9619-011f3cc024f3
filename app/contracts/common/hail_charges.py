from pydantic import Field
from typing import Optional

from app.contracts.base_contract import BaseContract


class CancellationFeeBreakdown(BaseContract):
    total: float = Field(examples=[20.0])
    driver_payout: float = Field(examples=[15.0])
    dash_fee: float = Field(examples=[5.0])


class HailCharges(BaseContract):
    cancellation_fee: float = Field(default=0, examples=[10.0])
    cancellation_fee_breakdown: Optional[CancellationFeeBreakdown] = Field(default=None)
