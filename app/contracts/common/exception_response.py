from typing import Any
from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.utils.exceptions.exception_code import ExceptionCode


class ExceptionResponse(BaseContract):
    code: str = Field(examples=[ExceptionCode.AUTH__FORBIDDEN])
    description: str = Field(
        examples=["You are not authorized to access this resource"]
    )
    status: int | None = Field(None)
    timestamp: str = Field(examples=["2021-10-01T12:00:00Z"])
    details: dict[Any, Any] | None = Field(None)

    def to_response(self) -> dict[str, Any]:
        return {
            "code": self.code,
            "description": self.description,
            "status": self.status,
            "timestamp": self.timestamp,
            **({"details": self.details} if self.details else {}),
        }
