from typing import Any
from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.contracts.common.pin import Pin
from app.models.localization_language import LocalizationLanguage
from app.utils.str_utils import StrUtils
from dash_proto.beam import hail_pb2  # type: ignore


class TripItineraryPlaceDetails(BaseContract):
    display_name: str = Field(examples=["Lee Garden 3"], max_length=255)
    formatted_address: str = Field(
        examples=["1 Hau Fook St, Tsim Sha Tsui, Hong Kong"])


class TripItinerary(Pin):
    index: int = Field(examples=[0], ge=0)
    place_id: str = Field(examples=["ChIJ0ZRG7K3iAzQRQkyUkOQOKcc"])
    i18n: dict[LocalizationLanguage, TripItineraryPlaceDetails] = Field(
        examples=[
            {
                LocalizationLanguage.EN: {
                    "display_name": "Lee Garden 3",
                    "formatted_address": "1 Hau Fook St, Tsim S<PERSON> Tsui, Hong Kong",
                }
            },
        ],
        alias="i18n",
    )

    @staticmethod
    def create_example() -> "TripItinerary":
        return TripItinerary(
            index=0,
            place_id="ChIJ0ZRG7K3iAzQRQkyUkOQOKcc",
            lat=22.27803660363929,
            lng=114.17739521440791,
            i18n={
                LocalizationLanguage.EN: TripItineraryPlaceDetails(
                    display_name="Tung Chung",
                    formatted_address="1 Hau Fook St, Tung Chung, Hong Kong",
                )
            },
        )

    def convert_for_firestore(self) -> dict[str, Any]:
        trip_itinerary_dict = self.model_dump()
        i18n_items = trip_itinerary_dict["i18n"].items()
        trip_itinerary_dict["i18n"] = {}

        for lang, details in i18n_items:
            trip_itinerary_dict["i18n"][StrUtils.locale_to_snake_case(
                lang)] = details

        return trip_itinerary_dict

    def to_itinerary_proto(self) -> hail_pb2.Itinerary:
        return hail_pb2.Itinerary(
            index=self.index,
            place_id=self.place_id,
            lat=self.lat,
            lng=self.lng,
        )
