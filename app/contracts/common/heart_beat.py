from pydantic import Field
from app.contracts.common.pin import Pin


class HeartBeat(Pin):
    heading: float | None = Field(None, examples=[180.0], ge=0, le=360)
    speed: float | None = Field(None, examples=[60.0], ge=0)

    @staticmethod
    def create_example() -> "HeartBeat":
        return HeartBeat(
            lat=22.27803660363929,
            lng=114.17739521440791,
            heading=180,
            speed=60,
        )
