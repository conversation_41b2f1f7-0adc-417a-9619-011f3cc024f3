from app.contracts.base_contract import BaseContract
from pydantic import Field


class FleetVehicleType(BaseContract):
    fleet_vehicle_key: str = Field(..., examples=["MPT"])
    fleet_class: str = Field(..., examples=["COMFORT"])
    name: str = Field(..., examples=["Comfort Sedan"])
    name_tc: str = Field(..., examples=["舒適轎車"])
    description: str = Field(..., examples=["Comfortable 4-seater sedan"])
    description_tc: str = Field(..., examples=["舒適的4座轎車"])
    number_of_seat: int = Field(..., examples=[4], ge=1)
    number_of_luggage: int = Field(..., examples=[2], ge=0)
    is_include_wheel_chair: bool = Field(default=False, examples=[False])
    vehicle_icon_url: str = Field(..., examples=["https://example.com/vehicle.png"])
    fleet_icon_url: str = Field(..., examples=["https://example.com/fleet.png"])

    @staticmethod
    def create_example() -> "FleetVehicleType":
        return FleetVehicleType(
            fleet_vehicle_key="MPT",
            fleet_class="COMFORT",
            name="Comfort Sedan",
            name_tc="舒適轎車",
            description="Comfortable 4-seater sedan",
            description_tc="舒適的4座轎車",
            number_of_seat=4,
            number_of_luggage=2,
            is_include_wheel_chair=False,
            vehicle_icon_url="https://example.com/vehicle.png",
            fleet_icon_url="https://example.com/fleet.png",
        )
