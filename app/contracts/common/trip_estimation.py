from pydantic import Field

from app.contracts.base_contract import BaseContract


class TripEstimation(BaseContract):
    distance_meters: int = Field(ge=0, examples=[1000])
    duration_seconds: int = Field(ge=0, examples=[600])
    encoded_polyline: str | None = Field(None, examples=["encoded_polyline"])

    @staticmethod
    def create_example() -> "TripEstimation":
        return TripEstimation(distance_meters=1000, duration_seconds=600)
