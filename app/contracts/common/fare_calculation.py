from decimal import Decimal
from enum import StrEnum
from pydantic import Field

from app.contracts.base_contract import BaseContract
from app.contracts.common.dash_transaction_fees import DashTransactionFees
from dash_proto.beam import hail_pb2  # type: ignore

class FareType(StrEnum):
    METER_FARE = "METER_FARE"
    FIXED_PRICE = "FIXED_PRICE"

class FareCalculation(BaseContract):
    estimated_fare_fee: Decimal = Field(ge=0, examples=[100.00])
    estimated_tunnel_fee: Decimal = Field(
        default=Decimal(0), ge=0, examples=[0.00])
    fixed_price: Decimal | None = Field(default=None, ge=0, examples=[100.00])
    fare_type: FareType | None = Field(default=None, examples=[FareType.METER_FARE])
    fleet_booking_fee: Decimal = Field(ge=0, examples=[24.00])
    additional_booking_fee: Decimal = Field(ge=0, examples=[24.00])
    dash_booking_fee: Decimal = Field(ge=0, examples=[3.00])
    boost_amount: Decimal = Field(default=Decimal(0), ge=0, examples=[10.00])
    sub_total: Decimal = Field(ge=0, examples=[127.00])
    dash_transaction_fee: Decimal = Field(ge=0, examples=[0.00])
    total: Decimal = Field(ge=0, examples=[127.00])
    discount: Decimal = Field(default=Decimal(0), ge=0, examples=[0.00])
    discounted_total: Decimal = Field(
        default=Decimal(0), ge=0, examples=[100.00])
    transaction_fees: DashTransactionFees

    def __eq__(self, other: object) -> bool:
        return isinstance(other, FareCalculation) and self.total == other.total

    def __ge__(self, other: object) -> bool:
        return isinstance(other, FareCalculation) and self.total >= other.total

    def __gt__(self, other: object) -> bool:
        return isinstance(other, FareCalculation) and self.total > other.total

    def __le__(self, other: object) -> bool:
        return isinstance(other, FareCalculation) and self.total <= other.total

    def __lt__(self, other: object) -> bool:
        return isinstance(other, FareCalculation) and self.total < other.total

    def to_fare_calculation_proto(self) -> hail_pb2.FareCalculation:
        proto = hail_pb2.FareCalculation(
            estimated_fare_fee=float(self.estimated_fare_fee),
            fleet_booking_fee=float(self.fleet_booking_fee),
            additional_booking_fee=float(self.additional_booking_fee),
            dash_booking_fee=float(self.dash_booking_fee),
            sub_total=float(self.sub_total),
            dash_transaction_fee=float(self.dash_transaction_fee),
            total=float(self.total),
            transaction_fees__dash_fee_constant=float(
                self.transaction_fees.dash_fee_constant
            ),
            transaction_fees__dash_fee_rate=float(
                self.transaction_fees.dash_fee_rate),
            estimated_tunnel_fee=float(self.estimated_tunnel_fee),
        )

        # Only set boost_amount if the field exists in the protobuf message
        if hasattr(proto, 'boost_amount'):
            proto.boost_amount = float(self.boost_amount)

        return proto
    @staticmethod
    def create_example() -> "FareCalculation":
        return FareCalculation(
            estimated_fare_fee=Decimal(100.00),
            estimated_tunnel_fee=Decimal(0.00),
            fleet_booking_fee=Decimal(24.00),
            additional_booking_fee=Decimal(24.00),
            dash_booking_fee=Decimal(3.00),
            boost_amount=Decimal(10.00),
            sub_total=Decimal(157.00),
            dash_transaction_fee=Decimal(3.00),
            total=Decimal(160.00),
            discount=Decimal(0.00),
            discounted_total=Decimal(160.00),
            transaction_fees=DashTransactionFees(
                dash_fee_constant=Decimal(3.00),
                dash_fee_rate=Decimal(0.03),
            ),
        )
