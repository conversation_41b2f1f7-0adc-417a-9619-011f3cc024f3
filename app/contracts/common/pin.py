from pydantic import Field

from app.contracts.base_contract import BaseContract


class Pin(BaseContract):
    lat: float = Field(examples=[22.27803660363929], ge=-90, le=90)
    lng: float = Field(examples=[114.17739521440791], ge=-180, le=180)

class PinWithOptional(BaseContract):
    lat: float | None = Field(examples=[22.27803660363929], ge=-90, le=90) 
    lng: float | None = Field(examples=[114.17739521440791], ge=-180, le=180)
