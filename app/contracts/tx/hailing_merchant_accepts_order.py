from app.contracts.base_contract import BaseContract
from app.contracts.common.heart_beat import HeartBeat
from app.models.tx_event_type import TxEventType


class HailingMerchantAcceptsOrderContent(BaseContract):
    phone_number: str
    heart_beat_on_accepted: HeartBeat | None = None
    meter: str
    distance: float | None = None
    eta: float | None = None
    boost_amount: float | None = None
    bonus_amount: float | None = None


class HailingMerchantAcceptsOrder(BaseContract):
    type: TxEventType = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER
    content: HailingMerchantAcceptsOrderContent
