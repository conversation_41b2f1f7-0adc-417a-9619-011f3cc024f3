from app.contracts.base_contract import BaseContract
from app.contracts.tx.tx_event_base_response import TxEventBaseResponse
from app.models.tx_event_type import TxEventType


class HailingMerchantAcceptsOrderResponseContent(BaseContract):
    phoneNumber: str


class HailingMerchantAcceptsOrderResponse(TxEventBaseResponse):
    type: TxEventType = TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER
    content: HailingMerchantAcceptsOrderResponseContent
