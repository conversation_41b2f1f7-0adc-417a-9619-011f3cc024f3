from app.contracts.base_contract import BaseContract
from app.contracts.tx.tx_event_base_response import TxEventBaseResponse
from app.contracts.common.hail_charges import HailCharges
from app.models.tx_event_type import TxEventType


class HailingUserCancelsOrderContent(BaseContract):
    charges: HailCharges


class HailingUserCancelsOrderResponse(TxEventBaseResponse):
    type: TxEventType = TxEventType.HAILING_USER_CANCELS_ORDER
    content: HailingUserCancelsOrderContent
