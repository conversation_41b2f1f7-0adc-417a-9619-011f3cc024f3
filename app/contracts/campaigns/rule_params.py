from typing import Tuple
from app.contracts.base_contract import BaseContract
from app.contracts.common.pin import Pin
from app.contracts.hails.create_hail_request import C<PERSON><PERSON><PERSON>Request
from app.contracts.matrix.create_hail_option_matrix_request import CreateHailOptionMatrixRequest
from app.utils.datetime_utils import DateTimeUtils


class RuleParams(BaseContract):
    user_id: str | None = None
    transaction_type: str | None = None
    transaction_subtype: str | None = None
    payment_instrument_type: str | None = None
    payment_channel: str | None = None
    origin: Tuple[float, float] | None = None
    destination: Tuple[float, float] | None = None
    origin_place_id: str | None = None
    destination_place_id: str | None = None
    time_of_day: str | None = None
    day_of_week: int | None = None
    # TODO: These fields are not handled in the current implementation but are included for future use.
    fare: float | None = None
    estimated_fare: float | None = None
    vehicle_class: str | None = None
    platform_type: str | None = None

    @staticmethod
    def from_create_hail_request(request: CreateHailRequest) -> "RuleParams":
        hk_datetime = DateTimeUtils.set_to_hk_tz(
            request.time) if request.time else None

        return RuleParams(
            user_id=None,
            transaction_type="TRIP",
            transaction_subtype="HAILING",
            payment_instrument_type=request.payment_details.card_type,
            payment_channel="APP",
            origin=[request.itinerary[0].lat,
                    request.itinerary[0].lng] if request.itinerary else None,
            destination=[
                request.itinerary[-1].lat, request.itinerary[-1].lng] if request.itinerary else None,
            time_of_day=hk_datetime.strftime(
                "%H:%M") if hk_datetime else None,
            day_of_week=(hk_datetime.weekday() +
                         1) if hk_datetime else None,
            fare=None,
            estimated_fare=None,
            vehicle_class=None,
            platform_type=request.platform_type,
        )

    @staticmethod
    def from_create_hail_option_matrix_request(request: CreateHailOptionMatrixRequest) -> "RuleParams":
        hk_datetime = DateTimeUtils.set_to_hk_tz(
            request.time) if request.time else None

        return RuleParams(
            user_id=None,
            transaction_type="TRIP",
            transaction_subtype="HAILING",
            payment_instrument_type=request.payment_instrument_type,
            payment_channel="APP",
            origin_place_id=request.place_ids[0] if request.place_ids else None,
            destination_place_id=request.place_ids[-1] if request.place_ids else None,
            time_of_day=hk_datetime.strftime(
                "%H:%M") if hk_datetime else None,
            day_of_week=(hk_datetime.weekday() +
                         1) if hk_datetime else None,
            fare=None,
            estimated_fare=None,
            vehicle_class=None,
            platform_type=request.platform_type,
        )
