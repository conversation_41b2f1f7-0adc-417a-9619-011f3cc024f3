from uuid import uuid4
from app.contracts.base_contract import BaseContract
from app.models.discount_rules import DiscountRules


class ApplicableCampaigns(BaseContract):
    campaign_id_third_party: str | None = None
    campaign_rules_third_party: str | None = None
    campaign_id_dash: str | None = None
    campaign_rules_dash: str | None = None

    @staticmethod
    def create_example() -> "ApplicableCampaigns":
        return ApplicableCampaigns(
            campaign_id_third_party=str(uuid4()),
            campaign_rules_third_party="",
            campaign_id_dash=str(uuid4()),
            campaign_rules_dash="",
        )

    def to_discount_rules(self) -> DiscountRules:
        return DiscountRules(
            discount_rules_dash=self.campaign_rules_dash,
            discount_rules_third_party=self.campaign_rules_third_party,
        )
