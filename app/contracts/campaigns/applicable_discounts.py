from uuid import uuid4
from app.contracts.base_contract import BaseContract
from dash_proto.beam import hail_pb2  # type: ignore
from app.models.discount_rules import DiscountRules


class ApplicableDiscounts(BaseContract):
    discount_id_third_party: str | None = None
    discount_rules_third_party: str | None = None
    discount_id_dash: str | None = None
    discount_rules_dash: str | None = None

    @staticmethod
    def create_example() -> "ApplicableDiscounts":
        return ApplicableDiscounts(
            discount_id_third_party=str(uuid4()),
            discount_rules_third_party="",
            discount_id_dash=str(uuid4()),
            discount_rules_dash="",
        )

    def to_discount_rules(self) -> DiscountRules:
        return DiscountRules(
            discount_rules_dash=self.discount_rules_dash,
            discount_rules_third_party=self.discount_rules_third_party,
        )

    def to_applicable_discounts_proto(self) -> hail_pb2.ApplicableDiscounts:
        return hail_pb2.ApplicableDiscounts(
            discount_id_dash=self.discount_id_dash,
            discount_rules_dash=self.discount_rules_dash,
            discount_id_third_party=self.discount_id_third_party,
            discount_rules_third_party=self.discount_rules_third_party,
        )
