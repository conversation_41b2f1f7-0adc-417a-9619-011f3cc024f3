from datetime import datetime
from uuid import uuid4

from pydantic import Field

from app.contracts.base_contract import BaseContract
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.common.fleet_vehicle import FleetVehicleType
from app.contracts.common.payment_details import PaymentDetails
from app.contracts.common.trip_itinerary import TripItinerary
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.common.hail_charges import HailCharges
from app.models.client_type import ClientType
from app.models.hail_platform_type import HailPlatformType
from app.models.hail_status import HailStatus
from app.models.hail_type import HailType
from app.contracts.hails.matched_driver import MatchedDriver
from app.models.localization_language import LocalizationLanguage
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class import VehicleClass
from app.contracts.hails.create_hail_request import PreferenceMatrix


class HailRiderResponse(BaseContract):
    id: str = Field(..., examples=[str(uuid4())])
    created_at: datetime
    updated_at: datetime
    user_id: str = Field(..., examples=[str(uuid4())])
    user_phone_number: str = Field(..., examples=["+85291234567"])
    language: str = Field(..., examples=[LocalizationLanguage.EN])
    trip_estimation: TripEstimation
    itinerary: list[TripItinerary]
    trip_id: str | None = Field(None, examples=[str(uuid4())])
    time: datetime
    platform_type: HailPlatformType = Field(
        examples=[HailPlatformType.DASH], default=HailPlatformType.DASH)
    payment_details: PaymentDetails
    min_max_fare_calculations: tuple[FareCalculation, ...]
    preference_matrix: PreferenceMatrix = Field(
        ...,
        examples=[
            {
                VehicleClass.FOUR_SEATER: [],
                VehicleClass.FIVE_SEATER: [],
                VehicleClass.COMFORT: [str(uuid4())],
            }
        ],
    )
    filters: ServiceFilters | None = None
    type: HailType
    status: HailStatus
    matched_driver: MatchedDriver | None = None
    charges: HailCharges | None = None
    prioritize_favorite_drivers: bool = False
    double_tunnel_fee: bool = False
    num_favorite_drivers_online: int = 0
    operating_areas: list[str] | None = None
    applicable_discounts: ApplicableDiscounts | None = None
    driver_count_by_radius: dict[float, int] | None = Field(
        default=None,
        examples=[{1.0: 5, 3.0: 12, 5.0: 20}],
        description="Map of radius in kilometers to number of drivers within that radius"
    )
    boost_amount: float | None = Field(
        default=None,
        examples=[10.0, 25.5],
        description="Boost amount to prioritize this hail request",
        ge=0
    )
    fleet_vehicle: FleetVehicleType | None = None
    fleet_partner_key: str | None = None
    client_type: ClientType = Field(default=ClientType.DASH, examples=[
                                    ClientType.DASH, ClientType.B2B_APP])
