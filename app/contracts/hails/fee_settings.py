from pydantic import Field
from app.contracts.base_contract import BaseContract


class FeeSettings(BaseContract):
    additional_booking_fee_rule: str | float | None = Field(
        ...,
        examples=[
            '{ "if": [ { "or": [ { "==": [ { "var" : "dayOfWeek" }, 5 ] }, { "==": [ { "var" : "dayOfWeek" }, 6 ] }, { "in": [ { "var" : "date" }, { "var" : "publicHolidays" } ] } ] }, 100, 0 ] }'
        ],
    )
    fleet_booking_fee_rule: str | float | None = Field(
        ...,
        examples=[
            '{ "if": [ { "or": [ { "==": [ { "var" : "dayOfWeek" }, 5 ] }, { "==": [ { "var" : "dayOfWeek" }, 6 ] }, { "in": [ { "var" : "date" }, { "var" : "publicHolidays" } ] } ] }, 100, 0 ] }'
        ],
    )
