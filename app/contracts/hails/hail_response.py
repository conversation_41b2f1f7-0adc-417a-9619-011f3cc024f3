
from pydantic import Field

from app.contracts.common.heart_beat import HeartBeat
from app.contracts.hails.hail_rider_response import HailRiderResponse


class HailResponse(HailRiderResponse):
    bonus_amount: float | None = Field(
        default=None,
        examples=[10.0, 25.5],
        description="Bonus amount to incentivize drivers to accept this hail request",
        ge=0
    )
    eta: float | None = Field(
        default=None,
        examples=[300],
        description="Estimated time of arrival in seconds"
    )
    heart_beat: HeartBeat | None = Field(
        default=None,
        examples=[HeartBeat.create_example()],
        description="Heartbeat of the matched driver"
    )
