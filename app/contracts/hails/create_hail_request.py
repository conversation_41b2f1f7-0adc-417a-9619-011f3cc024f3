from uuid import uuid4
from pydantic import Field, field_validator
from datetime import datetime
from app.contracts.base_contract import BaseContract
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.common.fleet_vehicle import FleetVehicleType
from app.contracts.common.payment_details import PaymentDetails
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.common.trip_itinerary import (
    TripItinerary,
    TripItineraryPlaceDetails,
)
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class import VehicleClass
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.validation_exception_builder import ValidationExceptionBuilder

PreferenceMatrix = dict[VehicleClass, list[str]]


class CreateHailRequest(BaseContract):
    platform_type: str = Field(examples=["DASH", "FLEET"])
    fleet_vehicle_type_key: str | None = Field(default=None, examples=["MPT"])
    fleet_partner_key: str | None = Field(default=None, examples=["SYNCAB"])
    tx_id: str = Field(examples=[str(uuid4())])
    language: LocalizationLanguage = Field(examples=[LocalizationLanguage.EN])
    trip_estimation: TripEstimation
    itinerary: list[TripItinerary] = Field(
        examples=[
            [
                TripItinerary(
                    index=0,
                    place_id="ChIJwcg1V1EABDQRFqycdeEoLdQ",
                    lat=22.27803660363929,
                    lng=114.17739521440791,
                    i18n={
                        LocalizationLanguage.EN: TripItineraryPlaceDetails(
                            display_name="Lee Garden 3",
                            formatted_address="1 Hau Fook St, Tsim Sha Tsui, Hong Kong",
                        )
                    },
                ),
                TripItinerary(
                    index=1,
                    place_id="ChIJEULc_GIABDQRfY_ZL9npKAM",
                    lat=22.27803660363929,
                    lng=114.17739521440791,
                    i18n={
                        LocalizationLanguage.EN: TripItineraryPlaceDetails(
                            display_name="The Peninsula Hong Kong",
                            formatted_address="Salisbury Rd, Tsim Sha Tsui, Hong Kong",
                        )
                    },
                ),
            ]
        ]
    )
    payment_details: PaymentDetails
    fleet_vehicle_type: FleetVehicleType | None = Field(default=None)
    time: datetime = Field(
        examples=[DateTimeUtils.utc_now_with_tz().isoformat()],
    )
    vehicle_preferences: PreferenceMatrix = Field(
        examples=[
            {
                VehicleClass.COMFORT: ["fg28fhewihf8932r23"],
                VehicleClass.LUXURY: ["fg28fhewihf8932r23", "hy28fhewihf8932r56"],
            }
        ]
    )
    """
    Vehicle class-fleet matrix where an empty list of fleet IDs means all fleets are viable.
    """
    operating_areas: list[MeterOperatingArea] | None = Field(
        None, examples=[[MeterOperatingArea.LANTAU]]
    )
    filters: ServiceFilters | None = None
    prioritize_favorite_drivers: bool = Field(default=False, examples=[False])
    double_tunnel_fee: bool = Field(default=False, examples=[False])
    applicable_discounts: ApplicableDiscounts | None = Field(default=None)

    @field_validator("time")
    def validate_datetime_with_timezone(cls, time: datetime) -> datetime:
        if DateTimeUtils.datetime_has_timezone(time):
            return DateTimeUtils.datetime_to_utc(time)
        else:
            raise ValidationExceptionBuilder().invalid_datetime_timezone()

    @staticmethod
    def create_example() -> "CreateHailRequest":
        return CreateHailRequest(
            tx_id=str(uuid4()),
            language=LocalizationLanguage.EN,
            trip_estimation=TripEstimation.create_example(),
            itinerary=[
                TripItinerary(
                    index=0,
                    place_id="ChIJ-dx8dXUBBDQRJXQvN7zqOvM",
                    lng=114.177004,
                    lat=22.321759,
                    i18n={
                        LocalizationLanguage.EN: TripItineraryPlaceDetails(
                            display_name="Lee Garden 3",
                            formatted_address="1 Hau Fook St, Tsim Sha Tsui, Hong Kong",
                        )
                    },
                ),
                TripItinerary(
                    index=1,
                    place_id="ChIJuwh9w1YABDQRaRbZ1DgateI",
                    lng=114.17739521440791,
                    lat=22.27803660363929,
                    i18n={
                        LocalizationLanguage.EN: TripItineraryPlaceDetails(
                            display_name="The Peninsula Hong Kong",
                            formatted_address="Salisbury Rd, Tsim Sha Tsui, Hong Kong",
                        )
                    },
                ),
            ],
            payment_details=PaymentDetails.create_example(),
            time=DateTimeUtils.utc_now_with_tz(),
            vehicle_preferences={
                VehicleClass.FOUR_SEATER: [],
                VehicleClass.FIVE_SEATER: [],
                VehicleClass.COMFORT: [],
                VehicleClass.LUXURY: [],
            },
            operating_areas=None,
            filters=None,
        )
