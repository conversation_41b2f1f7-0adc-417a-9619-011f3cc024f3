from pydantic import Field
from app.contracts.base_contract import BaseContract


class RiderHailBoostRequest(BaseContract):
    boost_amount: float | None = Field(
        None, 
        description="Amount to add to the current boost (cumulative). Must be greater than 0. Will be capped at the maximum boost limit configured in Firestore hail configuration or 500 (default).", 
        examples=[10.0, 25.5],
        ge=0,
    )