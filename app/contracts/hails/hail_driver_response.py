from datetime import datetime
from decimal import Decimal
from uuid import uuid4

from pydantic import Field

from app.contracts.base_contract import BaseContract
from app.contracts.common.trip_itinerary import TripItinerary
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.models.hail_status import HailStatus
from app.models.hail_type import HailType
from app.contracts.hails.matched_driver import MatchedDriver
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class import VehicleClass


class HailDriverResponse(BaseContract):
    id: str = Field(..., examples=["+85291234567"])
    created_at: datetime
    updated_at: datetime
    user_id: str = Field(..., examples=[str(uuid4())])
    user_phone_number: str = Field(..., examples=["+85291234567"])
    trip_estimation: TripEstimation
    itinerary: list[TripItinerary]
    trip_id: str | None = Field(None, examples=[str(uuid4())])
    time: datetime | None = None
    markup: Decimal = Field(
        ..., description="Markup price amount", examples=[24.00], ge=0
    )
    vehicle_class: VehicleClass
    filters: ServiceFilters | None = None
    type: HailType
    status: HailStatus
    tunnel_fees: list[TunnelFee] = Field(default=[])
    matched_driver: MatchedDriver | None = None
    is_favorite_driver: bool = Field(False, examples=[True, False])
    double_tunnel_fee: bool = Field(default=False, examples=[False])
    fixed_price: Decimal | None = Field(default=None, examples=[100.00], ge=0)
