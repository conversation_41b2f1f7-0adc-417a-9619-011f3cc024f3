from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.common.heart_beat import HeartBeat
from app.contracts.hails.fee_settings import FeeSettings
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from dash_proto.beam import hail_pb2  # type: ignore


class MatchedDriver(BaseContract):
    driver_id: str = Field(..., examples=["+85291234567"])
    name: str = Field(examples=["<PERSON>"])
    name_tc: str = Field(examples=["老夫子"])
    phone_number: str = Field(..., examples=["+85291234567"])
    license_plate: str = Field(..., examples=["AB1234"])
    vehicle_make: str = Field(..., examples=["Toyota"])
    vehicle_model: str = Field(..., examples=["Comfort"])
    vehicle_class: VehicleClass = Field(..., examples=["COMFORT"])
    operating_area: MeterOperatingArea | None = Field(
        default=None,
        examples=[MeterOperatingArea.URBAN],
    )
    is_dash_meter: bool | None = Field(default=False, examples=[True])
    fare_calculation: FareCalculation
    fee_settings: FeeSettings | None = None
    heart_beat_on_accepted: HeartBeat | None = Field(
        default=None, examples=[HeartBeat.create_example()]
    )
    vehicle_icon_url: str | None = Field(default=None, examples=["https://example.com/icon.png"])

    def to_matched_driver_proto(self) -> hail_pb2.MatchedDriver:
        return hail_pb2.MatchedDriver(
            driver_id=self.driver_id,
            license_plate=self.license_plate,
            vehicle_class=self.vehicle_class,
            is_dash_meter=self.is_dash_meter or False,
            fare_calculation=self.fare_calculation.to_fare_calculation_proto(),
        )
