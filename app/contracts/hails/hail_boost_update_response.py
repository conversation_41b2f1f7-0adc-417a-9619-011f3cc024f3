from datetime import datetime

from pydantic import Field
from app.contracts.base_contract import BaseContract


class BoostUpdateResponse(BaseContract):
    success: bool = Field(
        default=True,
        description="Whether the boost update was successful"
    )
    total_boost_amount: float = Field(
        description="Total boost amount after this update",
        examples=[25.0, 50.0, 100.0]
    )
    added_boost_amount: float = Field(
        description="Amount of boost added in this request",
        examples=[10.0, 25.0, 50.0]
    )
    message: str = Field(
        default="Boost added successfully",
        description="Human-readable message about the boost update",
        examples=[
            "Added $10.00 boost. Total boost: $25.00",
            "Added $50.00 boost. Total boost: $100.00",
            "Boost limit reached. Added $25.00 boost. Total boost: $500.00"
        ]
    )
    updated_at: datetime = Field(
        description="Timestamp when the hail was last updated"
    )