from pydantic import Field
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.common.fleet_vehicle import FleetVehicleType
from app.contracts.common.payment_details import PaymentDetails
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.common.trip_itinerary import TripItinerary
from app.contracts.matrix.create_estimate_total_range_price_request import CreateEstimateTotalRangePriceRequest
from app.models.client_type import ClientType
from app.models.service_filters import ServiceFilters


class CreateHailRequestV3(CreateEstimateTotalRangePriceRequest):
    payment_details: PaymentDetails
    applicable_discounts: ApplicableDiscounts
    tx_id: str
    itinerary: list[TripItinerary]
    prioritize_favorite_drivers: bool = Field(False, examples=[False])
    filters: ServiceFilters | None = None
    fleet_vehicle_type: FleetVehicleType | None = Field(default=None)
    trip_estimation: TripEstimation
    place_ids: list[str] = Field(default=[], exclude=True)
    client_type: ClientType = Field(default=ClientType.DASH, examples=[
                                    ClientType.DASH, ClientType.B2B_APP])
