from pydantic import Field
from app.contracts.hails.create_hail_request import PreferenceMatrix
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from app.models.vehicle_class import VehicleClass


class CreateEstimatedFeeRangeRequest(CreateHailOptionMatrixRequest):
    vehicle_preferences: PreferenceMatrix = Field(
        examples=[
            {
                VehicleClass.COMFORT: ["fg28fhewihf8932r23"],
                VehicleClass.LUXURY: ["fg28fhewihf8932r23", "hy28fhewihf8932r56"],
            }
        ],
        description="The vehicle class and corresponding fleet ids preferences for the hail request",
    )
    fleet_vehicle_type: str | None = Field(
        default=None,
        examples=["E-MPT"],
        description="The vehicle type for fleet hail",
    )
