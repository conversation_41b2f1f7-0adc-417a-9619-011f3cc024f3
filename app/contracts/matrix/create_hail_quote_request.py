from datetime import datetime

from pydantic import Field, field_validator
from app.contracts.base_contract import BaseContract
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.models.hail_platform_type import HailPlatformType
from app.models.localization_language import LocalizationLanguage
from app.models.price_rule import PriceRule
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.validation_exception_builder import ValidationExceptionBuilder


class CreateHailQuoteRequest(BaseContract):
    place_ids: list[str] = Field(
        min_length=2,
        max_length=2,
        examples=['["ChIJwXMVrLriAzQRcMQVFWJ-Hsg", "ChIJPZXTUy_8AzQRBVRH5GyvszU"]'],
        description="The place ids of the trip itinerary",
    )
    platform_type: HailPlatformType = Field(
        default=HailPlatformType.DASH,
        examples=[HailPlatformType.DASH, HailPlatformType.FLEET],
        description="The type of hail booking. DASH or FLEET",
    )
    time: datetime = Field(
        examples=[DateTimeUtils.utc_now_with_tz().isoformat()],
        description="The time for the hail booking",
    )
    language: LocalizationLanguage = Field(
        examples=[LocalizationLanguage.EN, LocalizationLanguage.ZH_HK],
        description="The language of the information to be returned",
    )
    is_double_tunnel_fee: bool = Field(default=True, examples=[False])
    boost_amount: float | None = Field(
        default=None,
        description="Boost amount to prioritize this hail request",
        examples=[10.0, 25.5],
        ge=0
    )
    fleet_partner_key: str | None = Field(default=None, examples=["SYNCAB"])
    applicable_discounts: ApplicableDiscounts
    price_rules: list[PriceRule]

    @field_validator("time")
    def validate_datetime_with_timezone(cls, time: datetime) -> datetime:
        if DateTimeUtils.datetime_has_timezone(time):
            return DateTimeUtils.datetime_to_utc(time)
        else:
            raise ValidationExceptionBuilder().invalid_datetime_timezone()

    @property
    def is_english(self) -> bool:
        return self.language == LocalizationLanguage.EN

    @staticmethod
    def create_example() -> "CreateHailQuoteRequest":
        return CreateHailQuoteRequest(
            place_ids=["ChIJwXMVrLriAzQRcMQVFWJ-Hsg",
                       "ChIJPZXTUy_8AzQRBVRH5GyvszU"],
            time=DateTimeUtils.utc_now_with_tz(),
            language=LocalizationLanguage.EN,
            is_double_tunnel_fee=False,
            boost_amount=10.0,
            applicable_discounts=ApplicableDiscounts.create_example(),
            price_rules=[PriceRule(json_rule='{"*": [{"var": "fare"}, 1]}')]
        )
