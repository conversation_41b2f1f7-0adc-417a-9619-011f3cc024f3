from decimal import Decimal
from pydantic import Field

from app.contracts.campaigns.applicable_campaigns import ApplicableCampaigns
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.hails.create_hail_request import PreferenceMatrix
from app.contracts.location.trip_itinerary_estimation import TripItineraryEstimation
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.matrix.hail_option_matrix import HailSubOption
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.models.hail_platform_type import HailPlatformType
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.contracts.base_contract import BaseContract


class HailFleetVehicleClassOptionV2(BaseContract):
    vehicle_class_name: str
    vehicle_class_description: str
    fleet_vehicle_type: str | None = None
    fleet_vehicle_id: str | None = None
    fleet_partner_key: str | None = None
    vehicle_icon_url: str | None = None
    fleet_icon_url: str | None = None
    seats_count: int
    luggage_count: int
    sub_options: list[HailSubOption]
    is_include_wheel_chair: bool = False

    @staticmethod
    def create_example() -> "HailFleetVehicleClassOptionV2":
        return HailFleetVehicleClassOptionV2(
            vehicle_class_name="Four Seater",
            vehicle_class_description="Four Seats in a Car",
            seats_count=4,
            luggage_count=2,
            sub_options=[HailSubOption.create_example()],
            is_include_wheel_chair=False,
        )


class HailFleetOptionMatrixV2(TripItineraryEstimation):
    tunnel_fees: list[TunnelFee]
    applicable_campaigns: ApplicableCampaigns | None = Field(default=None)
    applicable_discounts: ApplicableDiscounts | None = Field(default=None)
    fleet_vehicle_class_options: list[HailFleetVehicleClassOptionV2] = Field(
        default=[],
        examples=[[HailFleetVehicleClassOptionV2.create_example()]],
    )
    platform_type: HailPlatformType = Field(default=HailPlatformType.FLEET, examples=[HailPlatformType.DASH, HailPlatformType.FLEET])

    def min_max_fare_calculations(
        self, fleet_vehicle_type_key: str
    ) -> tuple[FareCalculation, FareCalculation] | None:
        if len(self.fleet_vehicle_class_options) < 1:
            return None
        min_fare: FareCalculation | None = None
        max_fare: FareCalculation | None = None

        if self.fleet_vehicle_class_options:
            for vehicle_class_option in self.fleet_vehicle_class_options:
                if fleet_vehicle_type_key == vehicle_class_option.fleet_vehicle_type:
                    min_fare = vehicle_class_option.sub_options[0]
                    max_fare = vehicle_class_option.sub_options[0]
                    break
        return (
            (min_fare, max_fare)
            if min_fare is not None and max_fare is not None
            else None
        )

    def base_fare_estimations_by_area_str(self) -> dict[MeterOperatingArea, Decimal]:
        return {}
