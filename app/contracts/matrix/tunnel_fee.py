from decimal import Decimal
from typing import Any

from pydantic import Field, model_validator
from app.contracts.base_contract import BaseContract


class TunnelFee(BaseContract):
    tunnel: str = Field(examples=["WESTERN_TUNNEL"])
    """The tunnel name."""
    fee: Decimal = Field(examples=[25])
    """The tunnel fee."""
    is_double_fee_applicable: bool = False
    """Whether double fee is applicable for the tunnel."""

    @model_validator(mode="before")
    def set_double_fee_applicable(cls, values: dict[str, Any]) -> dict[str, Any]:
        tunnel = values.get("tunnel")
        values["is_double_fee_applicable"] = tunnel in [
            "WESTERN_TUNNEL",
            "CROSS_HARBOUR_TUNNEL",
            "EASTERN_TUNNEL",
        ]
        return values
