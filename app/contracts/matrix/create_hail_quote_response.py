from uuid import uuid4
from pydantic import Field
from app.contracts.base_contract import BaseContract

from pydantic import Field
from app.contracts.base_contract import BaseContract
from app.contracts.common.fare_calculation import FareCalculation
from app.models.meter_operating_area import MeterOperatingArea

class HailQuoteMetadata(BaseContract):
    seats_count: int
    luggage_count: int
    fleet_vehicle_type: str | None = None
    fleet_vehicle_id: str | None = None
    fleet_partner_key: str | None = None
    fleet_icon_url: str | None = None
    vehicle_icon_url: str | None = None

class HailQuote(BaseContract):
    price_matrix: FareCalculation
    vehicle_class: str
    vehicle_class_name: str
    vehicle_class_description: str
    metadata: HailQuoteMetadata
    operating_area: list[MeterOperatingArea] = list()
    
    @staticmethod
    def create_example() -> "HailQuote":
        return HailQuote(
            price_matrix=FareCalculation.create_example(),
            vehicle_class="FOUR_SEATER",
            vehicle_class_name="Four Seater",
            vehicle_class_description="Four Seater",
            vehicle_icon_url="https://example.com/icon.png",
            fleet_icon_url="https://example.com/icon.png",
            metadata=HailQuoteMetadata(seats_count=4, luggage_count=2),
        )

class CreateHailQuoteResponse(BaseContract):
    quote_id: str = Field(..., examples=[str(uuid4())])
    quotes: list[HailQuote] = Field(
        ...,
        examples=[
            [
                HailQuote.create_example(),
                HailQuote.create_example(),
            ]
        ],
        description="The quotes for the hail request",
    )