from decimal import Decimal
from pydantic import Field

from app.contracts.common.dash_transaction_fees import DashTransactionFees
from app.contracts.hails.create_hail_request import PreferenceMatrix
from app.contracts.location.trip_itinerary_estimation import TripItineraryEstimation
from app.contracts.common.fare_calculation import FareCalculation
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.contracts.base_contract import BaseContract
from app.models.vehicle_image import VehicleImage


class HailSubOption(FareCalculation):
    fleet_id: str | None = None
    fleet_name: str | None = None

    @staticmethod
    def create_example() -> "HailSubOption":
        return HailSubOption(
            fleet_id="QUHpqngI2Jav4KymJVFa",
            fleet_name="D-ASH team",
            dash_booking_fee=Decimal(10),
            additional_booking_fee=Decimal(10),
            dash_transaction_fee=Decimal(3),
            estimated_fare_fee=Decimal(100),
            estimated_tunnel_fee=Decimal(30),
            fleet_booking_fee=Decimal(10),
            sub_total=Decimal(150),
            total=Decimal(153),
            discount=Decimal(0),
            discounted_total=Decimal(100),
            transaction_fees=DashTransactionFees(
                dash_fee_constant=Decimal(3),
                dash_fee_rate=Decimal(0.03),
            ),
        )


class HailVehicleClassOption(BaseContract):
    vehicle_class: VehicleClass
    vehicle_class_name: str
    vehicle_class_description: str
    image_name: VehicleImage | None = Field(
        examples=[VehicleImage.COMFORT_URBAN, VehicleImage.STANDARD_LANTAU]
    )
    seats_count: int = Field(ge=4)
    luggage_count: int
    sub_options: list[HailSubOption]


class HailOptionMatrix(TripItineraryEstimation):
    estimated_base_fare: Decimal = Field(
        max_digits=10, decimal_places=2, default=Decimal(0)
    )
    operating_area: MeterOperatingArea | None = None
    matrix: list[HailVehicleClassOption]

    @property
    def sub_options(self) -> list[HailSubOption]:
        sub_options: list[HailSubOption] = []
        for vehicle_class_option in self.matrix:
            sub_options.extend(vehicle_class_option.sub_options)
        return sub_options

    def min_max_fare_calculations(
        self, preference_matrix: PreferenceMatrix
    ) -> tuple[FareCalculation, FareCalculation] | None:
        if len(self.sub_options) < 1:
            return None
        min_fare: FareCalculation | None = None
        max_fare: FareCalculation | None = None

        for vehicle_class_option in self.matrix:
            if vehicle_class_option.vehicle_class not in preference_matrix:
                continue
            for sub_option in vehicle_class_option.sub_options:
                if (
                    sub_option.fleet_id
                    not in preference_matrix[vehicle_class_option.vehicle_class]
                    and len(preference_matrix[vehicle_class_option.vehicle_class]) > 0
                ):
                    continue

                if min_fare is None or sub_option < min_fare:
                    min_fare = sub_option
                if max_fare is None or sub_option > max_fare:
                    max_fare = sub_option

        return (
            (min_fare, max_fare)
            if min_fare is not None and max_fare is not None
            else None
        )
