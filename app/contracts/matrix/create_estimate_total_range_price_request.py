from pydantic import Field
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.matrix.create_hail_quote_request import CreateHailQuoteRequest
from app.models.vehicle_class import VehicleClass


class CreateEstimateTotalRangePriceRequest(CreateHailQuoteRequest):
    prefer_vehicle_classes: list[VehicleClass] = Field(
        default=list(VehicleClass),
        examples=[list(VehicleClass)],
        description="the list of vehicle classes to estimate the total range price for",
    )
    applicable_discounts: ApplicableDiscounts
    fleet_vehicle_type_key: str | None = Field(default=None, examples=["E-MPT"])
