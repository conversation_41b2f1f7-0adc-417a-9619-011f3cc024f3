from decimal import Decimal
from pydantic import Field

from app.contracts.campaigns.applicable_campaigns import ApplicableCampaigns
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.hails.create_hail_request import PreferenceMatrix
from app.contracts.location.trip_itinerary_estimation import TripItineraryEstimation
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.matrix.hail_option_matrix import HailSubOption
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.models.hail_platform_type import HailPlatformType
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.contracts.base_contract import BaseContract


class HailVehicleClassOptionV2(BaseContract):
    vehicle_class: VehicleClass
    vehicle_class_name: str
    vehicle_class_description: str
    seats_count: int
    luggage_count: int
    sub_options: list[HailSubOption]

    @staticmethod
    def create_example() -> "HailVehicleClassOptionV2":
        return HailVehicleClassOptionV2(
            vehicle_class=VehicleClass.FOUR_SEATER,
            vehicle_class_name="Four Seater",
            vehicle_class_description="Four Seats in a Car",
            seats_count=4,
            luggage_count=2,
            sub_options=[HailSubOption.create_example()],
        )


class HailOptionMatrixV2(TripItineraryEstimation):
    tunnel_fees: list[TunnelFee]
    operating_area_options: dict[MeterOperatingArea, list[HailVehicleClassOptionV2]] | None = Field(
        default=None,
        examples=[
            {
                MeterOperatingArea.LANTAU: [
                    HailVehicleClassOptionV2.create_example()
                ],
                MeterOperatingArea.URBAN: [
                    HailVehicleClassOptionV2.create_example()
                ],
            }
        ]
    )
    applicable_campaigns: ApplicableCampaigns | None = Field(default=None)
    applicable_discounts: ApplicableDiscounts | None = Field(default=None)
    fleet_vehicle_class_options: list[HailVehicleClassOptionV2] = Field(
        default=[],
        examples=[[HailVehicleClassOptionV2.create_example()]],
    )
    operating_area: MeterOperatingArea | None = None
    platform_type: HailPlatformType = Field(default=HailPlatformType.DASH, examples=[HailPlatformType.DASH, HailPlatformType.FLEET])

    @property
    def sub_options(self) -> list[HailSubOption]:
        sub_options: list[HailSubOption] = []
        if self.operating_area_options:
            for operating_area_option in self.operating_area_options.values():
                for vehicle_class_option in operating_area_option:
                    sub_options.extend(vehicle_class_option.sub_options)
        return sub_options

    def min_max_fare_calculations(
        self, preference_matrix: PreferenceMatrix
    ) -> tuple[FareCalculation, FareCalculation] | None:
        if len(self.sub_options) < 1:
            return None
        min_fare: FareCalculation | None = None
        max_fare: FareCalculation | None = None

        if self.operating_area_options:
            for operating_area_option in self.operating_area_options.values():
                for vehicle_class_option in operating_area_option:
                    if vehicle_class_option.vehicle_class not in preference_matrix:
                        continue
                    for sub_option in vehicle_class_option.sub_options:
                        if (
                            sub_option.fleet_id
                            not in preference_matrix[vehicle_class_option.vehicle_class]
                            and len(preference_matrix[vehicle_class_option.vehicle_class])
                            > 0
                        ):
                            continue
                        if min_fare is None or sub_option < min_fare:
                            min_fare = sub_option
                        if max_fare is None or sub_option > max_fare:
                            max_fare = sub_option
        return (
            (min_fare, max_fare)
            if min_fare is not None and max_fare is not None
            else None
        )

    def base_fare_estimations_by_area(self) -> dict[MeterOperatingArea, Decimal]:
        base_fare_estimations: dict[MeterOperatingArea, Decimal] = {}
        if self.operating_area_options:
            for operating_area_option in self.operating_area_options:
                if (
                    len(self.operating_area_options[operating_area_option]) > 0
                    and len(
                        self.operating_area_options[operating_area_option][0].sub_options
                    )
                    > 0
                ):
                    base_fare_estimations[operating_area_option] = (
                        self.operating_area_options[operating_area_option][0]
                        .sub_options[0]
                        .estimated_fare_fee
                    )
        return base_fare_estimations
