from datetime import datetime

from pydantic import Field, field_validator
from app.contracts.base_contract import BaseContract
from app.contracts.campaigns.applicable_campaigns import ApplicableCampaigns
from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.models.discount_rules import DiscountRules
from app.models.hail_platform_type import HailPlatformType
from app.models.localization_language import LocalizationLanguage
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.validation_exception_builder import ValidationExceptionBuilder


class CreateHailOptionMatrixRequest(BaseContract):
    place_ids: list[str] = Field(
        min_length=2,
        max_length=2,
        examples=['["ChIJwXMVrLriAzQRcMQVFWJ-Hsg", "ChIJPZXTUy_8AzQRBVRH5GyvszU"]'],
        description="The place ids of the trip itinerary",
    )
    platform_type: HailPlatformType = Field(
        default=HailPlatformType.DASH,
        examples=[HailPlatformType.DASH, HailPlatformType.FLEET],
        description="The type of hail booking. DASH or FLEET",
    )
    time: datetime = Field(
        examples=[DateTimeUtils.utc_now_with_tz().isoformat()],
        description="The time for the hail booking",
    )
    language: LocalizationLanguage = Field(
        examples=[LocalizationLanguage.EN, LocalizationLanguage.ZH_HK],
        description="The language of the information to be returned",
    )
    session_token: str = Field(
        default="placeholder",
        examples=["eyJhbGciOi"],
        description="A google map api created session token to optionally be passed for billing purposes",
    )
    double_tunnel_fee: bool | None = Field(default=False, examples=[False])
    payment_instrument_type: str | None = Field(
        None,
        examples=["VISA"],
        description="The payment instrument type needed to calculate discounts",
    )
    applicable_campaigns: ApplicableCampaigns | None = Field(
        default=None,
        description="The applicable campaigns for the hail booking",
    )
    applicable_discounts: ApplicableDiscounts | None = Field(
        default=None,
        description="The applicable discounts for the hail booking",
    )
    boost_amount: float | None = Field(
        default=None,
        description="Boost amount to prioritize this hail request",
        examples=[10.0, 25.5],
        ge=0
    )

    @field_validator("time")
    def validate_datetime_with_timezone(cls, time: datetime) -> datetime:
        if DateTimeUtils.datetime_has_timezone(time):
            return DateTimeUtils.datetime_to_utc(time)
        else:
            raise ValidationExceptionBuilder().invalid_datetime_timezone()

    @property
    def is_english(self) -> bool:
        return self.language == LocalizationLanguage.EN

    @property
    def discount_rules_to_apply(self) -> DiscountRules:
        if self.applicable_discounts:
            return self.applicable_discounts.to_discount_rules()
        elif self.applicable_campaigns:
            return self.applicable_campaigns.to_discount_rules()
        else:
            return DiscountRules.model_validate({})

    @staticmethod
    def create_example() -> "CreateHailOptionMatrixRequest":
        return CreateHailOptionMatrixRequest(
            place_ids=["ChIJwcg1V1EABDQRFqycdeEoLdQ", "ChIJITOSyn4ABDQRAnXUwVYtUPo"],
            time=DateTimeUtils.utc_now_with_tz(),
            language=LocalizationLanguage.EN,
            payment_instrument_type="VISA",
            session_token="eyJhbGciOi",
            hail_type="DASH",
            platform_type=HailPlatformType.DASH,
        )
