from typing import AsyncIterator
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from app.controllers.v1.v1 import v1_router
from app.controllers.v2.v2 import v2_router
from app.controllers.v3.v3 import v3_router
from contextlib import asynccontextmanager
from app.infrastructure.app_configuration import AppConfiguration
from app.infrastructure.container import Container

from app.services.reporting_service import ReportingService
from app.controllers.v1 import (
    hails_controller,
    drivers_controller,
    pricing_controller,
    reports_controller,
    admin_controller,
)
from app.controllers.v2 import pricing_controller_v2
from app.controllers.v3 import hails_controller_v3, pricing_controller_v3
from app.infrastructure import lifecycle, app_configuration
from app.infrastructure.settings import settings
from app.infrastructure.lifecycle import LifeCycle


class App:
    container: Container
    app: FastAPI

    def __init__(self) -> None:
        self.container = Container()
        self.container.wire(
            modules=[
                lifecycle.__name__,
                app_configuration.__name__,
                hails_controller.__name__,
                drivers_controller.__name__,
                pricing_controller.__name__,
                reports_controller.__name__,
                admin_controller.__name__,
                pricing_controller_v2.__name__,
                pricing_controller_v3.__name__,
                hails_controller_v3.__name__,
            ]
        )
        self.app = FastAPI(
            title="D-ASH HAIL BACKEND",
            lifespan=self.lifespan,
            root_path="/hailing",
        )

        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=(
                ["*"]
                if settings.allow_testing
                else ["https://web-admin-72527.web.app", "https://admin.qa.dash-hk.com"]
            ),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self.app.include_router(v1_router)
        self.app.include_router(v2_router)
        self.app.include_router(v3_router)

        app_configurator = AppConfiguration()
        app_configurator.configure_app(self.app)

        ReportingService().initialize()

    @asynccontextmanager
    async def lifespan(
        self,
        _: FastAPI,
    ) -> AsyncIterator[None]:
        initializer = LifeCycle()
        await initializer.start_services()
        yield
        await initializer.stop_services()


app_instance: App = App()
app = app_instance.app

# For running with debugger
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, access_log=False)
