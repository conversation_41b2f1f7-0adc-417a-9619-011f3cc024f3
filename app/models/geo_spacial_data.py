from typing import List, Optional, Tuple
from pydantic import BaseModel, Field, ConfigDict
from rtree import index
from shapely.geometry import Point

from app.models.polygon_feature import PolygonFeature
from app.services.logger_service import LoggerService


class GeoSpatialData(BaseModel):
    """
    Holds a list of polygon features and an R-tree index for spatial queries.
    """

    logger_service: LoggerService
    features: List[PolygonFeature] = Field(default_factory=list)
    # We exclude the index from serialization as it's not JSON-serializable
    _rtree_index: index.Index | None = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def build_rtree_index(self) -> index.Index:
        """
        Build an R-tree index from the features in this data structure.
        """
        if self._rtree_index is None:
            new_index = index.Index()
            for i, feature in enumerate(self.features):
                new_index.insert(i, feature.geometry.bounds)
            return new_index
        else:
            return self._rtree_index

    def query(self, bounds: Tuple[float, float, float, float]) -> List[int]:
        """
        Query the R-tree for features that intersect the given bounding box.
        Returns a list of indices to the features in `self.features`.
        """
        if self._rtree_index is None:
            self._rtree_index = self.build_rtree_index()
        return list(self._rtree_index.intersection(bounds))

    def find_polygon_containing_point(self, point: Point) -> Optional[PolygonFeature]:
        """
        Find the polygon that contains the given point.
        Returns the PolygonFeature, or None if not found.
        """
        if self._rtree_index is None:
            self._rtree_index = self.build_rtree_index()
        candidate_indices = list(
            self._rtree_index.intersection((point.x, point.y, point.x, point.y))
        )

        self.logger_service.debug(
            f"Found {len(candidate_indices)} candidate polygons for point: {point}"
        )

        for i in candidate_indices:
            if self.features[i].geometry.contains(point):
                return self.features[i]
            else:
                self.logger_service.debug(
                    f"Polygon {i} does not contain point: {point.wkt}",
                    extra={
                        "polygon_index": i,
                        "polygon_geometry": self.features[i].geometry.wkt,
                        "point": point.wkt,
                    }
                )
        return None
