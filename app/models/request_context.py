from pydantic import BaseModel
from typing import Dict, Optional
import time


class RequestContext(BaseModel):
    correlation_id: str
    function_start_times: Dict[str, float] = {}
    
    def start_function_timer(self, function_name: str) -> None:
        """Record the start time for a function execution"""
        self.function_start_times[function_name] = time.time()
    
    def get_function_execution_time_ms(self, function_name: str) -> Optional[int]:
        """Get the execution time in milliseconds for a function"""
        start_time = self.function_start_times.get(function_name)
        if start_time is not None:
            execution_time_ms = int((time.time() - start_time) * 1000)
            # Clean up the stored start time
            del self.function_start_times[function_name]
            return execution_time_ms
        return None
