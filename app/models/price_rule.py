from enum import StrEnum
from app.contracts.base_contract import BaseContract
from app.models.client_type import ClientType
from app.models.hail_platform_type import HailPlatformType
from app.models.hail_type import HailType

class FeeType(StrEnum):
    FARE_ADJUSTMENT = "FARE_ADJUSTMENT"
    ADDITIONAL_BOOKING_FEE = "ADDITIONAL_BOOKING_FEE"
    DASH_TRANSACTION_FEE = "DASH_TRANSACTION_FEE"
    DASH_BOOKING_FEE = "DASH_BOOKING_FEE"

class PriceRule (BaseContract):
    platform_type: HailPlatformType = HailPlatformType.DASH
    client_type: ClientType = ClientType.DASH
    order_type: HailType = HailType.LIVE
    fee_type: FeeType
    json_rule: str
