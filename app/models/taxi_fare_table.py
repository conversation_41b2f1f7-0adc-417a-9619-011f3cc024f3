class TaxiFareTable:
    """
    Calculation of taxi fare based on formula from government fare table.
    More info: https://www.td.gov.hk/en/transport_in_hong_kong/public_transport/taxi/taxi_fare_of_hong_kong/index.html
    """

    distance_unit: float = 200
    """The distance unit in meters for each fare adjustment."""

    base_distance: float = 2000
    """The base distance in meters for the initial fare."""

    flagfall_rate: float
    """The initial fare for the taxi ride."""

    first_tier_rate: float
    """The fare per distance unit for the first tier of the hk taxi two-tier fare system."""

    second_tier_rate: float
    """The fare per distance unit for the second tier of the hk taxi two-tier fare system."""

    threshold_fare: float
    """The fare at which the second tier fare starts."""

    threshold_distance: float
    """The distance at which the fare reaches the threshold fare."""

    def __init__(
        self,
        flagfall_rate: float,
        first_tier_rate: float,
        second_tier_rate: float,
        threshold_fare: float,
    ):
        self.flagfall_rate = flagfall_rate
        self.first_tier_rate = first_tier_rate
        self.second_tier_rate = second_tier_rate
        self.threshold_fare = threshold_fare

        self.threshold_distance = self.get_threshold_distance(
            threshold_fare, flagfall_rate, first_tier_rate
        )

    def get_threshold_distance(
        self, threshold_fare: float, flagfall_rate: float, first_tier_fare: float
    ) -> float:
        """Calculate the distance at which the fare reaches the threshold fare."""

        return (
            threshold_fare - flagfall_rate
        ) / first_tier_fare * self.distance_unit + self.base_distance

    def get_fare_from_distance(self, distance: float) -> float:
        """Calculate the total fare based on the given distance."""

        first_tier_distance = max(
            0,
            min(
                self.threshold_distance - self.base_distance,
                distance - self.base_distance,
            ),
        )
        second_tier_distance = max(0, distance - self.threshold_distance)
        return (
            self.flagfall_rate
            + first_tier_distance / self.distance_unit * self.first_tier_rate
            + second_tier_distance / self.distance_unit * self.second_tier_rate
        )
