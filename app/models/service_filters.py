from typing import Any
from pydantic import Field

from app.contracts.base_contract import BaseContract


class ServiceFilters(BaseContract):
    is_assistant: bool = Field(default=False, examples=[False])
    is_pet_friendly: bool = Field(default=False, examples=[False])

    def __getitem__(self, item: str) -> Any:
        return getattr(self, item)

    @staticmethod
    def create_example() -> "ServiceFilters":
        return ServiceFilters(
            is_assistant=True,
            is_pet_friendly=True,
        )
