from enum import StrEnum


class TxEventType(StrEnum):
    HAILING_MERCHANT_ACCEPTS_ORDER = "HAILING_MERCHANT_ACCEPTS_ORDER"
    HAILING_MERCHANT_CANCELS_ORDER = "HAILING_MERCHANT_CANCELS_ORDER"
    HAILING_USER_CANCELS_ORDER = "HAILING_USER_CANCELS_ORDER"
    HAILING_MERCHANT_PICK_UP_CONFIRMED = "HAILING_MERCHANT_PICK_UP_CONFIRMED"
    HAILING_SCHEDULED_ORDER_TIMEOUT = "HAILING_SCHEDULED_ORDER_TIMEOUT"
    HAILING_USER_UPDATES_ORDER = "HAILING_USER_UPDATES_ORDER"
    HAILING_MERCHANT_ARRIVED_DESTINATION = "HAILING_MERCHANT_ARRIVED_DESTINATION",
    HAILING_ORDER_COMPLETED = "HAILING_ORDER_COMPLETED"
    HAILING_MERCHANT_APPROACHING_DESTINATION = "HAILING_MERCHANT_APPROACHING_DESTINATION"
