from pydantic import BaseModel
from app.models.role import Role
from firebase_admin import auth

from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder


class Auth(BaseModel):
    role: Role | str = Role.DEFAULT
    user_id: str
    phone_number: str = "temp"
    token: str

    @property
    def is_rider(self) -> bool:
        return self.role == Role.DEFAULT

    @property
    def is_driver(self) -> bool:
        return self.role == Role.DRIVER

    @property
    def driver_id(self) -> str:
        return self.phone_number

    @staticmethod
    def from_jwt_token(jwt_token: str) -> "Auth":
        try:
            payload = auth.verify_id_token(jwt_token)
        except:
            raise AuthExceptionBuilder().auth_invalid_token()

        auth_dict = {
            **payload,
            "token": jwt_token,
        }
        return Auth.model_validate(auth_dict)
