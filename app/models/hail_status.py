from enum import StrEnum


class HailStatus(StrEnum):
    PENDING = "PENDING"
    """Hail is pending for a driver to accept"""

    ACCEPTED = "ACCEPTED"
    """Hail has been accepted by a driver"""

    APPROACHING = "APPROACHING"
    """Hail driver is approaching the rider"""
    
    ARRIVED = "ARRIVED"
    """Hail driver has arrived at the rider's location"""

    ON_GOING = "ON_GOING"
    """Hail trip is currently on going"""

    CANCELLED = "CANCELLED"
    """Hail has been cancelled and is no longer needed"""

    TIMED_OUT = "TIMED_OUT"
    """Hail has timed out and is no longer needed"""

    COMPLETED = "COMPLETED"
    """Hail trip has been fully completed"""
