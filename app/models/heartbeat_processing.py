from pydantic import BaseModel, Field


class GeoPoint(BaseModel):
    latitude: float = Field(serialization_alias="_latitude")
    longitude: float = Field(serialization_alias="_longitude")


class MeterTimestamp(BaseModel):
    seconds: int = Field(serialization_alias="_seconds")
    nanoseconds: int = Field(serialization_alias="_nanoseconds")


class HeartbeatProcessingPayload(BaseModel):
    id: str
    server_time: MeterTimestamp
    location: GeoPoint
    speed: float | None = None
    device_time: MeterTimestamp


class HeartbeatProcessing(BaseModel):
    document: str
    after: HeartbeatProcessingPayload

    @staticmethod
    def create(
        meter_id: str,
        heartbeat_id: str,
        heartbeat_processing_payload: HeartbeatProcessingPayload,
    ) -> "HeartbeatProcessing":
        return HeartbeatProcessing(
            document=f"meters/{meter_id}/heartbeat/{heartbeat_id}",
            after=heartbeat_processing_payload,
        )
