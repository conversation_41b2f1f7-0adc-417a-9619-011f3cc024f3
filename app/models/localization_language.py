from enum import Enum
from typing import Dict, List, Optional


class LocalizationLanguage(str, Enum):
    EN = "en"
    ZH_HK = "zhHK"

    __value_aliases: Dict[str, List[str]] = {
        "zhHK": ["zh-hk", "zhHK", "zh-hk", "zh_hk"],
        "en": ["en", "EN", "en-us", "en_US"]
    }

    @classmethod
    def _missing_(cls, value: object) -> Optional["LocalizationLanguage"]:
        if not isinstance(value, str):
            return None
            
        value = value.lower()
        for enum_value in cls.__value_aliases:
            if value in cls.__value_aliases[enum_value]:
                return cls(enum_value)
        return None
