from typing import Dict, Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict
from shapely.geometry import Polygon
from shapely.geometry.base import BaseGeometry


class PolygonFeature(BaseModel):
    """
    Represents a single polygon feature in the KML (Placemark).
    We assume all geometry is a Shapely Polygon.
    """

    name: Optional[str] = None
    description: Optional[str] = None
    geometry: Polygon = Field(..., description="A shapely Polygon geometry.")
    extra_data: Dict[str, str] = Field(default_factory=dict)

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @field_validator("geometry")
    def validate_polygon(cls, value: BaseGeometry) -> BaseGeometry:
        """
        Ensure that the geometry is actually a Polygon.
        """
        if not isinstance(value, Polygon):
            raise ValueError(f"Expected Polygon, got: {value.geom_type}")
        return value
