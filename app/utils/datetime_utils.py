from datetime import datetime, timezone

import pytz


class DateTimeUtils:
    @staticmethod
    def utc_now_with_tz() -> datetime:
        return datetime.now(tz=timezone.utc)

    @staticmethod
    def set_to_hk_tz(date: datetime) -> datetime:
        hk_tz = pytz.timezone("Asia/Hong_Kong")
        return date.astimezone(hk_tz)

    @staticmethod
    def datetime_has_timezone(date: datetime) -> bool:
        return date.tzinfo is not None and date.tzinfo.utcoffset(date) is not None

    @staticmethod
    def datetime_to_utc(date: datetime) -> datetime:
        return date.astimezone(timezone.utc)
