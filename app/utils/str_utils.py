import re


class StrUtils:
    @staticmethod
    def is_locale_camel_case(string: str) -> bool:
        return re.match(r"/^[a-z]{2}[A-Z]{2}$/", string) is not None

    @staticmethod
    def to_snake_case(string: str) -> str:
        return re.sub(r"(?<!^)(?=[A-Z])", "_", string).lower()

    @staticmethod
    def locale_to_snake_case(locale: str) -> str:
        return re.sub(r"([a-z]{2})([A-Z]{2})", r"\1_\2", locale).lower()
