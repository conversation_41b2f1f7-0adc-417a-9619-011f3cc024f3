import copy
from typing import Any, Type

from app.models.socket_doc_type import SocketDocType
from app.sockets.messages.base_message import BaseMessage
from app.sockets.messages.driver_ack_message import DriverAckMessage
from app.sockets.messages.driver_eta_message import DriverEtaMessage
from app.sockets.messages.driver_heartbeat_message import DriverHeartbeatMessage
from app.sockets.messages.driver_pending_hails_message import DriverPendingHailsMessage
from app.sockets.messages.driver_session_created_message import (
    DriverSessionCreatedMessage,
)
from app.sockets.messages.driver_session_start_message import DriverSessionStartMessage
from app.sockets.messages.exception_message import ExceptionMessage
from app.sockets.messages.hail_accepted_message import HailAcceptedMessage
from app.sockets.messages.hail_approaching_driver_message import (
    HailApproachingDriverMessage,
)
from app.sockets.messages.hail_approaching_rider_message import (
    HailApproachingRiderMessage,
)
from app.sockets.messages.hail_boost_success_message import HailBoostSuccessMessage
from app.sockets.messages.hail_cancelled_message import HailCancelledMessage
from app.sockets.messages.hail_drivers_notified_message import (
    HailDriversNotifiedMessage,
)
from app.sockets.messages.hail_expired_message import HailExpiredMessage
from app.sockets.messages.hail_favorite_drivers_offline import (
    HailFavoriteDriversOfflineMessage,
)
from app.sockets.messages.hail_favorite_drivers_unavailable import (
    HailFavoriteDriversUnavailableMessage,
)
from app.sockets.messages.hail_on_going_rider_message import HailOnGoingRiderMessage
from app.sockets.messages.hail_pending_driver_message import HailPendingDriverMessage
from app.sockets.messages.hail_pending_rider_message import HailPendingRiderMessage
from app.sockets.messages.hail_not_available_message import HailNotAvailableMessage
from app.sockets.messages.hail_timed_out_message import HailTimedOutMessage


class AsyncApiUtils:
    __base_spec = {
        "asyncapi": "3.0.0",
        "info": {
            "title": "Hailing WebSocket API",
            "version": "1.0.0",
            "description": "API for managing hailing-related WebSocket communications",
        },
        "servers": {"dev": {"host": "api.dev.dash-hk.com/hailing", "protocol": "wss"}},
        "operations": {},
        "channels": {},
        "components": {
            "messages": {},
            "schemas": {},
            "messageTraits": {
                "commonHeaders": {
                    "headers": {
                        "type": "object",
                        "properties": {
                            "Authorization": {
                                "type": "string",
                                "examples": [
                                    "Bearer 2ey38329f8h3f298fy2489r7842fhihe983"
                                ],
                            }
                        },
                    }
                }
            },
        },
    }

    docs: dict[str, dict[Any, Any]] = {
        str(SocketDocType.HAILS): copy.deepcopy(__base_spec),
        str(SocketDocType.DRIVERS): copy.deepcopy(__base_spec),
    }

    def generate_asyncapi_docs(
        self,
        socket_doc_type: SocketDocType,
        channel_path: str,
        description: str,
        send_messages: list[Type[BaseMessage]],
        receive_messages: list[Type[BaseMessage]],
    ) -> None:
        message_refs: dict[str, Any] = {}

        socket_doc = str(socket_doc_type)
        channel_name = str(socket_doc_type).lower()

        def generate_message_docs(message_class: Type[BaseMessage]) -> None:

            def replace_refs(schema_properties: dict[Any, Any]) -> dict[Any, Any]:
                for key, value in schema_properties.items():
                    if key == "$ref":
                        schema_properties[key] = (
                            f"#/components/schemas/{value.split('/')[-1]}"
                        )
                    elif isinstance(value, dict):
                        schema_properties[key] = replace_refs(schema_properties[key])
                    elif isinstance(value, list):
                        schema_properties[key] = [
                            replace_refs(item) if isinstance(item, dict) else item
                            for item in schema_properties[key]
                        ]
                return schema_properties

            message_refs[message_class.__name__] = {
                "$ref": f"#/components/messages/{message_class.__name__}"
            }

            if (
                message_class.__name__
                not in async_api_utils.docs[socket_doc]["components"]["messages"]
            ):
                async_api_utils.docs[socket_doc]["components"]["messages"][
                    message_class.__name__
                ] = {
                    "name": message_class.__name__,
                    "title": message_class.__name__,
                    "contentType": "application/json",
                    "traits": [{"$ref": "#/components/messageTraits/commonHeaders"}],
                    "payload": {
                        "$ref": f"#/components/schemas/{message_class.__name__}"
                    },
                }

            # Add the message schema to the components
            schema_data = message_class.model_json_schema()
            nested_schemas = schema_data.get("$defs", {})
            for schema_name, schema_properties in nested_schemas.items():
                if "properties" in schema_properties:
                    async_api_utils.docs[socket_doc]["components"]["schemas"][
                        schema_name
                    ] = {
                        "type": "object",
                        "properties": (
                            replace_refs(schema_properties["properties"])
                            if (
                                schema_properties["properties"]
                                and isinstance(schema_properties["properties"], dict)
                            )
                            else {}
                        ),
                        "required": schema_properties.get("required", []),
                    }
                if "enum" in schema_properties:
                    async_api_utils.docs[socket_doc]["components"]["schemas"][
                        schema_name
                    ] = {
                        "type": "string",
                        "enum": schema_properties["enum"],
                    }

            schema_properties = schema_data["properties"]
            async_api_utils.docs[socket_doc]["components"]["schemas"][
                message_class.__name__
            ] = {
                "type": "object",
                "properties": {
                    **replace_refs(schema_properties),
                    "message": {
                        "type": "string",
                        "const": schema_properties["message"]["default"],
                        "description": "The type of message that has occurred.",
                    },
                },
            }

        for message_class in send_messages:
            generate_message_docs(message_class)

            async_api_utils.docs[socket_doc]["operations"][message_class.__name__] = {
                "action": "send",
                "channel": {"$ref": f"#/channels/{channel_name}"},
                "messages": [
                    {
                        "$ref": f"#/channels/{channel_name}/messages/{message_class.__name__}"
                    }
                ],
            }

        for message_class in receive_messages:
            generate_message_docs(message_class)

            async_api_utils.docs[socket_doc]["operations"][message_class.__name__] = {
                "action": "receive",
                "channel": {"$ref": f"#/channels/{channel_name}"},
                "messages": [
                    {
                        "$ref": f"#/channels/{channel_name}/messages/{message_class.__name__}"
                    }
                ],
            }

        # Add the channel information to the AsyncAPI spec
        if channel_name not in async_api_utils.docs[socket_doc]["channels"]:
            async_api_utils.docs[socket_doc]["channels"][channel_name] = {
                "address": channel_path,
                "messages": message_refs,
                "description": description,
                # "parameters": {
                #     "streetlightId": {"$ref": "#/components/parameters/streetlightId"}
                # },
            }


async_api_utils = AsyncApiUtils()

# Hail channel
async_api_utils.generate_asyncapi_docs(
    socket_doc_type=SocketDocType.HAILS,
    channel_path="v1/hails/{hailId}",
    description="Hail channel for receiving status updates for a hail.",
    send_messages=[],
    receive_messages=[
        HailAcceptedMessage,
        HailPendingRiderMessage,
        HailApproachingRiderMessage,
        HailOnGoingRiderMessage,
        HailTimedOutMessage,
        HailFavoriteDriversOfflineMessage,
        HailFavoriteDriversUnavailableMessage,
        HailDriversNotifiedMessage,
        DriverHeartbeatMessage,
        DriverEtaMessage,
        ExceptionMessage,
        HailBoostSuccessMessage, 
    ],
)

# Driver channel
async_api_utils.generate_asyncapi_docs(
    socket_doc_type=SocketDocType.DRIVERS,
    channel_path="v1/drivers",
    description="Driver channel for starting a session and handling heartbeat and hail message.",
    send_messages=[DriverHeartbeatMessage, DriverSessionStartMessage, DriverAckMessage],
    receive_messages=[
        DriverSessionCreatedMessage,
        DriverPendingHailsMessage,
        HailPendingDriverMessage,
        HailApproachingDriverMessage,
        HailNotAvailableMessage,
        HailCancelledMessage,
        HailExpiredMessage,
        ExceptionMessage,
    ],
)
