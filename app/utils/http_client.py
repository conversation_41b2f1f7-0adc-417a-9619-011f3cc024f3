import httpx
from app.infrastructure.settings import settings


def create_http_client(timeout: float | None = None) -> httpx.AsyncClient:
    """
    Create a configured httpx.AsyncClient with global timeout settings.
    
    Args:
        timeout: Custom timeout in seconds. If None, uses settings.http_timeout
        
    Returns:
        Configured httpx.AsyncClient instance
    """
    effective_timeout = timeout or settings.http_timeout
    
    return httpx.AsyncClient(
        timeout=httpx.Timeout(timeout=effective_timeout),
        # You can add other global client configurations here
        # follow_redirects=True,
        # limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
    )


def create_http_timeout(timeout: float | None = None) -> httpx.Timeout:
    """
    Create a configured httpx.Timeout object.
    
    Args:
        timeout: Custom timeout in seconds. If None, uses settings.http_timeout
        
    Returns:
        Configured httpx.Timeout instance
    """
    effective_timeout = timeout or settings.http_timeout
    
    return httpx.Timeout(timeout=effective_timeout)
