from typing import Any
from app.utils.exceptions.base_exception import BaseHailException


class ExceptionDocUtils:
    @staticmethod
    def get_exception_response_docs(
        exceptions: list[BaseHailException],
    ) -> dict[int, dict[str, Any]]:
        docs: dict[int, dict[str, Any]] = {}
        for exception in exceptions:
            status_code = exception.status_code
            if status_code not in docs:
                docs[status_code] = {
                    # "description": "Multiple possible errors",
                    "content": {"application/json": {"examples": {}}},
                }
            docs[status_code]["content"]["application/json"]["examples"][
                exception.code
            ] = {
                "summary": exception.code,
                "value": exception.to_response(),
            }
        return docs
