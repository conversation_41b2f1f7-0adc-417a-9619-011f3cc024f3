from fastapi import status

from app.models.hail_status import HailStatus
from app.models.meter_operating_area import MeterOperatingArea
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class HailExceptionBuilder(BaseExceptionBuilder):
    def hail_already_exists(self, hail_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__ALREADY_EXISTS,
            description="Hail already exists",
            data={"hail_id": hail_id},
        )

    def hail_not_found(self, hail_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__NOT_FOUND,
            description="Hail not found",
            data={"hail_id": hail_id},
        )

    def hail_not_updatable(
        self, hail_status: HailStatus, hail_id: str
    ) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__NOT_UPDATABLE,
            description=f"Hail not updatable with status {hail_status}",
            data={"hail_id": hail_id},
        )

    def hail_config_not_found(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__CONFIG__NOT_FOUND,
            description="Hail config not found",
        )

    def fleets_not_found(self, fleet_id: str | None = None) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__FLEETS_NOT_FOUND,
            description="Hail fleets not found",
            data={"fleet_id": fleet_id} if fleet_id is not None else None,
        )

    def invalid_schedule_time(self, schedule_delta_mins: int) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__INVALID_SCHEDULE_TIME,
            description=f"Invalid schedule time. Hails can only be scheduled at least {schedule_delta_mins} minutes in advance.",
        )

    def invalid_meter_pairing_state(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__INVALID_METER_PAIRING_STATE,
            description="Invalid meter pairing state",
        )

    def no_matched_driver(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__NO_MATCHED_DRIVER,
            description="No matched driver",
        )

    def no_hail_options_available(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__NO_OPTIONS_AVAILABLE,
            description="No hail options available",
        )

    def no_base_fare_estimation_for_area(
        self, area: MeterOperatingArea | None
    ) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.HAIL__NO_BASE_FARE_ESTIMATION_FOR_AREA,
            description="No base fare estimation for area",
            data={"area": area},
        )
    
    def invalid_boost_amount(self, message: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__INVALID_BOOST_AMOUNT,
            description=message,
        )
    
    def boost_limit_exceeded(self, current_boost: float, max_limit: float) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.HAIL__BOOST_LIMIT_EXCEEDED,
            description=f"Boost limit exceeded. Current: ${current_boost:.2f}, Max allowed: ${max_limit:.2f}",
            data={"current_boost": current_boost, "max_limit": max_limit},
        )