from app.utils.exceptions.exception_code import ExceptionCode
from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder


class MerchantCampaignExceptionBuilder(BaseExceptionBuilder):
    def merchant_campaign_service_failure(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.MERCHANT_CAMPAIGN__SERVICE_FAILURE,
            description="Merchant campaign service failure",
        )

    def timeout(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.MERCHANT_CAMPAIGN__TIMEOUT,
            description="Merchant campaign service timeout",
        )
