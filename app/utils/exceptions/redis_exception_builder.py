from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class RedisExceptionBuilder(BaseExceptionBuilder):
    def lock_failed_to_create(self, lock_key: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.LOCATION__ROUTE_NOT_FOUND,
            description="Failed to create lock",
            data={"lock_key": lock_key},
        )
