from fastapi import status
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class DriverExceptionBuilder(BaseExceptionBuilder):
    def driver_not_found(self, driver_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.DRIVER__DRIVER_NOT_FOUND,
            description="Driver not found. Start driver session first.",
            data={"driver_id": driver_id},
        )
