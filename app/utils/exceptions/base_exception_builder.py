from abc import ABC
from typing import Any

from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.exception_code import ExceptionCode


class BaseExceptionBuilder(ABC):
    def build_exception(
        self,
        status: int,
        code: ExceptionCode,
        description: str,
        data: dict[str, Any] | None = None,
    ) -> BaseHailException:
        return BaseHailException(
            status=status,
            code=code,
            description=description,
            data=data,
        )
