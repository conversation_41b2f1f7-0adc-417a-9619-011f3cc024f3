from fastapi import status
from fastapi.exceptions import RequestValidationError
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class ValidationExceptionBuilder(BaseExceptionBuilder):
    def invalid_data(self, exc: RequestValidationError) -> BaseHailException:
        validation_errors = exc.errors()
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.VALIDATION__INVALID_DATA,
            description=", ".join([f"[{".".join(str(loc) for loc in error["loc"])}]: {error["msg"]}" for error in validation_errors]),
            data={"errors": validation_errors},
        )

    def invalid_datetime_timezone(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.VALIDATION__INVALID_DATETIME_TIMEZONE,
            description="Datetime must be timezone-aware",
        )
