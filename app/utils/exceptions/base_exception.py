from datetime import datetime
from typing import Any
from fastapi import HTTPException
from pydantic import ValidationError

from app.contracts.common.exception_response import ExceptionResponse
from app.utils.exceptions.exception_code import ExceptionCode


class BaseHailException(HTTPException):
    code: ExceptionCode
    description: str
    timestamp: str = datetime.now().isoformat()
    detail: Any

    def __init__(
        self,
        status: int,
        code: ExceptionCode,
        description: str,
        data: dict[str, Any] | None = None,
    ):
        super().__init__(
            status_code=status,
        )
        self.code = code
        self.description = description
        self.detail = data

    @staticmethod
    def from_validation_error(error: ValidationError) -> "BaseHailException":
        return BaseHailException(
            status=400,
            code=ExceptionCode.VALIDATION__INVALID_DATA,
            description="Invalid data",
            data={"errors": error.errors()},
        )

    def to_response(self, additional_details: dict[str, Any] = {}) -> dict[str, Any]:
        return ExceptionResponse(
            status=self.status_code,
            code=self.code,
            description=self.description,
            timestamp=self.timestamp,
            details=(
                {**self.detail, **additional_details}
                if self.detail
                else additional_details
            ),
        ).to_response()
