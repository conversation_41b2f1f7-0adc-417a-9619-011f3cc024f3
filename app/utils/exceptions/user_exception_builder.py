from fastapi import status

from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class UserExceptionBuilder(BaseExceptionBuilder):
    def user_not_found(self, user_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.USER__NOT_FOUND,
            description="User not found",
            data={"user_id": user_id},
        )
