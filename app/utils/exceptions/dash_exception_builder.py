from fastapi import status
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class DashExceptionBuilder(BaseExceptionBuilder):
    def unexpected_error(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.DASH__UNEXPECTED_ERROR,
            description="Unexpected error",
        )
