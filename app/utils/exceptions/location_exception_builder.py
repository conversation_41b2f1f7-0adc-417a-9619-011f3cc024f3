from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class LocationExceptionBuilder(BaseExceptionBuilder):
    def location_service_failure(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.LOCATION__SERVICE_FAILURE,
            description="Location service failure",
        )

    def route_not_found(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.LOCATION__ROUTE_NOT_FOUND,
            description="Route not found",
        )

    def location_outside_of_service_area(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.LOCATION__OUTSIDE_OF_SERVICE_AREA,
            description="Location outside of service area",
        )

    def origin_and_destination_same(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.LOCATION__ORIGIN_AND_DESTINATION_SAME,
            description="Origin and destination are the same",
        )
