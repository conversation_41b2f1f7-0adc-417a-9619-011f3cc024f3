from fastapi import status

from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class ReportExceptionBuilder(BaseExceptionBuilder):
    def invalid_date_range(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.REPORT__INVALID_DATE_RANGE,
            description="The end date must be greater than the start date",
        )
