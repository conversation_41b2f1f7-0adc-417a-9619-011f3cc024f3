from enum import StrEnum


class ExceptionCode(StrEnum):
    VALIDATION__INVALID_DATA = "VALIDATION__INVALID_DATA"
    AUTH__FORBIDDEN = "AUTH__FORBIDDEN"
    AUTH__INSUFFICIENT_ROLE = "AUTH__INSUFFICIENT_ROLE"
    AUTH__INVALID_AUTHORIZATION_CODE = "AUTH__INVALID_AUTHORIZATION_CODE"
    AUTH__INVALID_SCHEME = "AUTH__INVALID_SCHEME"
    AUTH__INVALID_TOKEN = "AUTH__INVALID_TOKEN"
    AUTH__INVALID_TOKEN_PAYLOAD = "AUTH__INVALID_TOKEN_PAYLOAD"
    HAIL__ALREADY_EXISTS = "HAIL__ALREADY_EXISTS"
    HAIL__NOT_FOUND = "HAIL__NOT_FOUND"
    HAIL__NOT_UPDATABLE = "HAIL__NOT_UPDATABLE"
    HAIL__CONFIG__NOT_FOUND = "HAIL_CONFIG__NOT_FOUND"
    HAIL__FLEETS_NOT_FOUND = "HAIL__FLEETS_NOT_FOUND"
    HAIL__INVALID_SCHEDULE_TIME = "HAIL__INVALID_SCHEDULE_TIME"
    HAIL__INVALID_METER_PAIRING_STATE = "HAIL__INVALID_METER_PAIRING_STATE"
    HAIL__NO_MATCHED_DRIVER = "HAIL__NO_MATCHED_DRIVER"
    HAIL__NO_OPTIONS_AVAILABLE = "HAIL__NO_OPTIONS_AVAILABLE"
    HAIL__NO_BASE_FARE_ESTIMATION_FOR_AREA = "HAIL__NO_BASE_FARE_ESTIMATION_FOR_AREA"
    HAIL__BOOST_LIMIT_EXCEEDED = "HAIL__BOOST_LIMIT_EXCEEDED"
    HAIL__INVALID_BOOST_AMOUNT = "HAIL__INVALID_BOOST_AMOUNT"
    DRIVER__DRIVER_NOT_FOUND = "DRIVER__DRIVER_NOT_FOUND"
    USER__NOT_FOUND = "USER__NOT_FOUND"
    LOCATION__SERVICE_FAILURE = "LOCATION__SERVICE_FAILURE"
    LOCATION__ROUTE_NOT_FOUND = "LOCATION__ROUTE_NOT_FOUND"
    LOCATION__OUTSIDE_OF_SERVICE_AREA = "LOCATION__OUTSIDE_OF_SERVICE_AREA"
    LOCATION__ORIGIN_AND_DESTINATION_SAME = "LOCATION__ORIGIN_AND_DESTINATION_SAME"
    REDIS__LOCK_FAILED_TO_CREATE = "REDIS__LOCK_FAILED_TO_CREATE"
    VALIDATION__INVALID_DATETIME_TIMEZONE = "VALIDATION__INVALID_DATETIME_TIMEZONE"
    METER__NOT_FOUND = "METER__NOT_FOUND"
    METER__INVALID_DATA = "METER__INVALID_DATA"
    METER__ACTIVE_TRIP_NOT_FOUND = "METER__ACTIVE_TRIP_NOT_FOUND"
    METER__ACTIVE_TRIP_IS_ALREADY_PAIRED = "METER__ACTIVE_TRIP_IS_ALREADY_PAIRED"
    METER__TRIP_NOT_FOUND = "METER__TRIP_NOT_FOUND"
    METER__INSUFFICIENT_HAIL_DATA_FOR_TRIP = "METER__INSUFFICIENT_HAIL_DATA_FOR_TRIP"
    VEHICLE__VEHICLE_NOT_FOUND = "VEHICLE__VEHICLE_NOT_FOUND"
    TX__EVENT_TYPE_NOT_VALID = "TX__EVENT_TYPE_NOT_VALID"
    TX__SERVICE_FAILURE = "TX__SERVICE_FAILURE"
    PRICE__INSUFFICIENT_OPTIONS_SELECTED = "PRICE__INSUFFICIENT_OPTIONS_SELECTED"
    PRICE__FLEET_VEHICLE_TYPE_NOT_PROVIDED = "PRICE__FLEET_VEHICLE_TYPE_NOT_PROVIDED"
    REPORT__INVALID_DATE_RANGE = "REPORT__INVALID_DATE_RANGE"
    DASH__UNEXPECTED_ERROR = "DASH__UNEXPECTED_ERROR"
    CAMPAIGN__SERVICE_FAILURE = "CAMPAIGN__SERVICE_FAILURE"
    CAMPAIGN__TIMEOUT = "CAMPAIGN__TIMEOUT"
    FLEET_QUOTE__SERVICE_FAILURE = "FLEET_QUOTE__SERVICE_FAILURE"
    FLEET_QUOTE__TIMEOUT = "FLEET_QUOTE__TIMEOUT"
    MERCHANT_CAMPAIGN__SERVICE_FAILURE = "MERCHANT_CAMPAIGN__SERVICE_FAILURE"
    MERCHANT_CAMPAIGN__TIMEOUT = "MERCHANT_CAMPAIGN__TIMEOUT"
