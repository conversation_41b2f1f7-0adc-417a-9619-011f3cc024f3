from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class PricingExceptionBuilder(BaseExceptionBuilder):
    def insufficient_options_selected(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.PRICE__INSUFFICIENT_OPTIONS_SELECTED,
            description="At least one pricing matrix option must be selected",
        )
    def fleet_vehicle_type_not_provided(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.PRICE__FLEET_VEHICLE_TYPE_NOT_PROVIDED,
            description="Fleet vehicle type not provided",
        )
