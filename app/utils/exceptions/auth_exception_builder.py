from fastapi import status

from app.models.role import Role
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class AuthExceptionBuilder(BaseExceptionBuilder):
    def auth_forbidden_resource(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_403_FORBIDDEN,
            code=ExceptionCode.AUTH__FORBIDDEN,
            description="Forbidden resource",
            data={"message": "You don't have permission to access this resource"},
        )

    def auth_insufficient_role(self, required_role: Role) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_403_FORBIDDEN,
            code=ExceptionCode.AUTH__INSUFFICIENT_ROLE,
            description="Insufficient role",
            data={"required_role": required_role},
        )

    def auth_invalid_authorization_code(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_401_UNAUTHORIZED,
            code=ExceptionCode.AUTH__INVALID_AUTHORIZATION_CODE,
            description="Invalid authorization code",
        )

    def auth_invalid_scheme(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_401_UNAUTHORIZED,
            code=ExceptionCode.AUTH__INVALID_SCHEME,
            description="Invalid authorization scheme",
        )

    def auth_invalid_token(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_401_UNAUTHORIZED,
            code=ExceptionCode.AUTH__INVALID_TOKEN,
            description="Invalid token",
        )

    def auth_invalid_token_payload(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_401_UNAUTHORIZED,
            code=ExceptionCode.AUTH__INVALID_TOKEN_PAYLOAD,
            description="Invalid token payload",
        )
