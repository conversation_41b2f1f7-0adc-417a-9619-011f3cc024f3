from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class TxExceptionBuilder(BaseExceptionBuilder):
    def event_type_not_valid(self, event_type: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.TX__EVENT_TYPE_NOT_VALID,
            description="Tx event type is not valid or is not yet supported",
            data={"event_type": event_type},
        )

    def tx_service_failure(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.TX__SERVICE_FAILURE,
            description="Tx service failure",
        )
