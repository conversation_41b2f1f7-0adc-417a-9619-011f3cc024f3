from fastapi import status
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class FleetQuoteExceptionBuilder(BaseExceptionBuilder):
    def fleet_service_failure(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.FLEET_QUOTE__SERVICE_FAILURE,
            description="Fleet service failure",
        )

    def timeout(self) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            code=ExceptionCode.FLEET_QUOTE__TIMEOUT,
            description="Fleet service timeout",
        )
