from fastapi import status
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class VehicleExceptionBuilder(BaseExceptionBuilder):
    def vehicle_not_found(self, vehicle_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.VEHICLE__VEHICLE_NOT_FOUND,
            description="Vehicle not found.",
            data={"vehicle_id": vehicle_id},
        )
