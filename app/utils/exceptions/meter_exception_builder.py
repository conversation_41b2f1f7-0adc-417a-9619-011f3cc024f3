from typing import Any
from fastapi import status
from app.utils.exceptions.base_exception import Base<PERSON>ailException
from app.utils.exceptions.base_exception_builder import BaseExceptionBuilder
from app.utils.exceptions.exception_code import ExceptionCode


class MeterExceptionBuilder(BaseExceptionBuilder):
    def meter_not_found(self, meter_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.METER__NOT_FOUND,
            description="Meter not found",
            data={"meter_id": meter_id},
        )

    def invalid_meter_data(
        self, meter_id: str, meter_data: dict[str, Any]
    ) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.METER__INVALID_DATA,
            description="Invalid meter data",
            data={"meter_id": meter_id, "meter_data": meter_data},
        )

    def active_meter_trip_not_found(self, meter_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_404_NOT_FOUND,
            code=ExceptionCode.METER__ACTIVE_TRIP_NOT_FOUND,
            description="Active meter trip not found",
            data={"meter_id": meter_id},
        )

    def active_meter_trip_is_already_paired(
        self, meter_id: str, trip_id: str
    ) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.METER__ACTIVE_TRIP_IS_ALREADY_PAIRED,
            description="Active meter trip is already paired",
            data={"meter_id": meter_id, "trip_id": trip_id},
        )

    def insufficient_hail_data_for_trip(self, hail_id: str) -> BaseHailException:
        return self.build_exception(
            status=status.HTTP_400_BAD_REQUEST,
            code=ExceptionCode.METER__INSUFFICIENT_HAIL_DATA_FOR_TRIP,
            description="Insufficient hail data for trip",
            data={"hail_id": hail_id},
        )
