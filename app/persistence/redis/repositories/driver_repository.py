import asyncio
from datetime import datetime, timedelta
import json
from uuid import uuid4
from app.contracts.common.heart_beat import HeartBeat
from app.contracts.hails.fleet_hail_update_request import FleetHailUpdateRequest
from app.models.heartbeat_processing import (
    GeoPoint,
    HeartbeatProcessing,
    HeartbeatProcessingPayload,
    MeterTimestamp,
)
from app.models.meter_operating_area import MeterOperatingArea
from app.persistence.firestore.entities.meter import Meter
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.redis.entities.driver import Driver
from app.persistence.redis.entities.hail import Hail
from app.services.web_socket_message_service import WebSocketMessageService
from app.services.fare_calculation_service import FareCalculationService
from app.services.geo_service import DriverLocation
from typing import List, Set
from app.persistence.redis.redis import redis
from app.models.id_location import IdLocation
from app.contracts.common.pin import Pin
from app.services.logger_service import LoggerService
from app.infrastructure.settings import settings
from app.services.pub_sub_publisher_service import PubSubPublisherService
from app.sockets.messages.hail_drivers_notified_message import (
    HailDriversNotifiedMessage,
)
from app.sockets.messages.hail_pending_driver_message import HailPendingDriverMessage
from app.utils.datetime_utils import DateTimeUtils

DRIVER_GEO_KEY = "dash:geo:drivers"
URBAN_DRIVERS_KEY = "dash:drivers:urban"
NT_DRIVERS_KEY = "dash:drivers:nt"
LANTAU_DRIVERS_KEY = "dash:drivers:lantau"


class DriverRepository:
    logger_service: LoggerService
    configuration_repository: ConfigurationsRepository
    fare_calculation_service: FareCalculationService
    pub_sub_publisher_service: PubSubPublisherService
    web_socket_message_service: WebSocketMessageService

    def __init__(
        self,
        logger_service: LoggerService,
        configuration_repository: ConfigurationsRepository,
        fare_calculation_service: FareCalculationService,
        pub_sub_publisher_service: PubSubPublisherService,
        web_socket_message_service: WebSocketMessageService,
    ):
        self.logger_service = logger_service
        self.configuration_repository = configuration_repository
        self.fare_calculation_service = fare_calculation_service
        self.pub_sub_publisher_service = pub_sub_publisher_service
        self.web_socket_message_service = web_socket_message_service

    def __hail_drivers_key(self, hail_id: str) -> str:
        return f"dash:drivers:hail:{hail_id}"

    async def get_active_drivers(self) -> list[Driver]:
        """
        Get all active drivers
        """
        driver_ids = await redis.zrange(DRIVER_GEO_KEY, 0, -1)
        drivers = await self.get_drivers_by_ids(set(driver_ids))
        return [driver for driver in drivers if driver.is_available]

    async def get_driver_by_id(self, driver_id: str) -> Driver | None:
        try:
            driver: Driver | None = await Driver.get(driver_id)
            return driver
        except Exception:
            self.logger_service.warning(f"Driver {driver_id} does not exist")
            return None

    async def get_drivers_for_hail(self, hail_id: str) -> Set[str]:
        """
        Get the drivers that were notified of a hail
        """
        drivers = await redis.smembers(self.__hail_drivers_key(hail_id))
        return drivers

    async def upsert_driver_by_id(self, fleet_hail_update_request: FleetHailUpdateRequest, meter: Meter) -> Driver:
        try:
            self.logger_service.log(
                f"Upserting driver {fleet_hail_update_request.driver_phone_number}")
            driver: Driver = await Driver.get(fleet_hail_update_request.driver_phone_number)
            driver.license_plate = fleet_hail_update_request.driver_license_plate if fleet_hail_update_request.driver_license_plate else driver.license_plate
            driver.name = fleet_hail_update_request.driver_name if fleet_hail_update_request.driver_name else driver.name
            await driver.save()
            return driver
        except Exception:
            self.logger_service.log(
                f"Creating driver {fleet_hail_update_request.driver_phone_number}")
            driver = await Driver.from_fleet_hail_update_request(fleet_hail_update_request, meter)
            await driver.save()
            return driver

    async def remove_driver(self, driver_id: str) -> None:
        """
        Remove the driver location from the Redis GeoSet
        """
        await redis.zrem(DRIVER_GEO_KEY, driver_id)
        driver = await self.get_driver_by_id(driver_id)
        if driver is not None:
            await driver.set_to_offline()

    async def update_driver_location(
        self, driver_id: str, driver_heartbeat: HeartBeat
    ) -> None:
        """
        Update the driver location in the Redis GeoSet
        """
        driver = await self.get_driver_by_id(driver_id)
        if driver is None:
            return

        await asyncio.gather(
            driver.set_heart_beat(driver_heartbeat),
            redis.geoadd(
                DRIVER_GEO_KEY,
                IdLocation(
                    id=driver_id, location=driver_heartbeat).to_coords(),
            ),
        )

    async def get_drivers_by_ids(self, driver_ids: Set[str]) -> list[Driver]:
        """
        Get all driver hashes by driver ids
        """
        if len(driver_ids) == 0:
            return []

        get_driver_sessions = redis.register_script(
            """
            local matched_drivers = {}

            for i = 1, #KEYS do
                local driver_id = KEYS[i]
                local driver = redis.call("HGETALL", "dash:driver:" .. driver_id)

                if table.getn(driver) > 0 then
                    local driver_table = {}

                    for j = 1, #driver, 2 do
                        driver_table[driver[j]] = driver[j + 1]
                    end

                    table.insert(matched_drivers, driver_table)
                end
            end

            return cjson.encode(matched_drivers)
        """
        )

        raw_driver_session_data = await get_driver_sessions(keys=list(driver_ids))

        driver_sessions = [
            Driver.model_validate(driver)
            for driver in json.loads(raw_driver_session_data)
        ]

        return driver_sessions

    async def add_driver_to_operating_area(self, driver: Driver) -> None:
        """
        Add the driver to the correct operating area set
        """
        pipeline = redis.pipeline()
        await pipeline.srem(URBAN_DRIVERS_KEY, driver.driver_id)
        await pipeline.srem(NT_DRIVERS_KEY, driver.driver_id)
        await pipeline.srem(LANTAU_DRIVERS_KEY, driver.driver_id)

        match driver.operating_area:
            case MeterOperatingArea.NT:
                await pipeline.sadd(NT_DRIVERS_KEY, driver.driver_id)
            case MeterOperatingArea.LANTAU:
                await pipeline.sadd(LANTAU_DRIVERS_KEY, driver.driver_id)
            case _:
                await pipeline.sadd(URBAN_DRIVERS_KEY, driver.driver_id)

        await pipeline.execute()

    async def set_drivers_for_hail(self, hail: Hail, drivers: list[Driver], timestamp: datetime) -> None:
        """
        Queue drivers for a hail
        """
        hail_drivers_key = self.__hail_drivers_key(hail.id)

        # TODO: In future, get current hail drivers and exclude them from resending socket message
        existing_drivers = await redis.smembers(hail_drivers_key)
        # new_drivers = set(driver.id for driver in drivers) - existing_drivers

        if len(drivers) > 0:
            await redis.sadd(hail_drivers_key, *[driver.id for driver in drivers])
            await redis.expire(
                hail_drivers_key,
                (
                    timedelta(days=1)
                    if hail.is_live
                    else hail.time - DateTimeUtils.utc_now_with_tz()
                ),
            )

            hail_config = await self.configuration_repository.get_hail_configuration()

            self.logger_service.log(
                f"Sending hail {hail.id} request to {len(drivers)} drivers"
            )

            await asyncio.gather(
                *[
                    self.web_socket_message_service.trigger_message(
                        {driver.id},
                        HailPendingDriverMessage(
                            payload=hail.to_hail_driver_response(
                                driver,
                                hail_config,
                                fare_calculation_service=self.fare_calculation_service,
                            ),
                            timestamp=timestamp,
                        ),
                    )
                    for driver in drivers
                ]
            )

            total_drivers_notified = len(
                existing_drivers | {driver.id for driver in drivers}
            )
            self.logger_service.debug(
                f"Hail {hail.id} total drivers notified: {total_drivers_notified}"
            )

            await self.web_socket_message_service.trigger_message(
                {hail.id},
                HailDriversNotifiedMessage(
                    payload=total_drivers_notified,
                ),
            )

    async def remove_drivers_for_hail(self, hail_id: str) -> None:
        """
        Remove all drivers from the hail
        """
        await redis.delete(self.__hail_drivers_key(hail_id))

    async def set_expiry_for_all_drivers_for_hail(self, hail_id: str) -> None:
        """
        Remove all drivers from the hail
        """
        await redis.expire(self.__hail_drivers_key(hail_id), timedelta(minutes=10))

    async def get_driver_count_by_radius(
        self, hail: Hail, *radius: float
    ) -> dict[float, int]:
        """
        Get the count of matched drivers within a given radius of the hail
        """
        driver_count_by_radius: dict[float, int] = {}
        if len(radius) == 0:
            return driver_count_by_radius

        for r in radius:
            driver_count_by_radius[r] = len(
                await self.get_filtered_drivers_within_radius(hail, r)
            )
        return driver_count_by_radius

    async def check_drivers_and_set_for_hail(
        self, hail: Hail, drivers: list[Driver], timestamp: datetime
    ) -> None:
        """
        Checks if list of drivers is different from set already notified and notify if not to be notified of the pending hail
        """
        driver_ids_for_hail = {driver.driver_id for driver in drivers}
        current_driver_ids_for_hail = await self.get_drivers_for_hail(hail.id)
        if driver_ids_for_hail != current_driver_ids_for_hail:
            await self.set_drivers_for_hail(hail, drivers, timestamp)

    async def get_filtered_drivers_within_radius(
        self, hail: Hail, km_radius: float
    ) -> list[Driver]:
        """
        Finds drivers within a given radius, filters them by the hail's preferences
        """
        self.logger_service.debug(
            f"hail: {hail.origin_pin()}, radius: {km_radius}")
        viable_drivers_within_radius = await self.__get_viable_drivers_within_radius(
            hail.origin_pin(), km_radius
        )
        self.logger_service.debug(
            f"Drivers within {km_radius}{settings.default_unit_of_distance} of hail {hail.id}: {", ".join(viable_drivers_within_radius) if len(viable_drivers_within_radius) > 0 else 'None'}"
        )
        filtered_drivers = await self.__filter_drivers_by_hail_filters(
            viable_drivers_within_radius, hail
        )
        self.logger_service.log(
            f"Viable drivers for {hail.type.lower()} hail {hail.id}: {", ".join([d.driver_id for d in filtered_drivers]) if len(filtered_drivers) > 0 else 'None'}"
        )

        return filtered_drivers

    async def get_viable_drivers_for_hail_by_filters(
        self, hail: Hail, driver_ids: set[str]
    ) -> list[Driver]:
        """
        Filters drivers by the hail's preferences, and sets them to be notified of the pending hail
        """

        filtered_drivers = await self.__filter_drivers_by_hail_filters(driver_ids, hail)

        self.logger_service.log(
            f"Viable drivers for {hail.type.lower()} hail {hail.id}: {", ".join([d.driver_id for d in filtered_drivers]) if len(filtered_drivers) > 0 else 'None'}"
        )

        return filtered_drivers

    async def set_viable_drivers_for_hail_by_operating_area(
        self, hail: Hail, operating_areas: set[MeterOperatingArea], timestamp: datetime
    ) -> list[Driver]:
        """
        Finds drivers with a given operating area, filters them by the hail's preferences, and sets them to be notified of the pending hail
        """
        drivers_with_matching_operating_area = (
            await self.__get_drivers_by_operating_areas(operating_areas)
        )

        filtered_drivers = await self.get_viable_drivers_for_hail_by_filters(
            hail, drivers_with_matching_operating_area
        )

        await self.set_drivers_for_hail(hail, filtered_drivers, timestamp)

        return filtered_drivers

    async def __get_viable_drivers_within_radius(
        self, start: Pin, km_radius: float = settings.default_radius
    ) -> Set[str]:
        """
        Get ids of drivers within the intersection of a certain radius of a location as well as a given region
        """
        driver_locations_within_radius: List[DriverLocation] = await redis.georadius(
            DRIVER_GEO_KEY,
            start.lng,
            start.lat,
            km_radius,
            settings.default_unit_of_distance,
            withcoord=True,
        )

        # geo_service = GeoService()
        # drivers_within_region = geo_service.filter_users_by_region(start, drivers_within_radius, km_radius)

        matched_driver_ids = set(
            [driver[0] for driver in driver_locations_within_radius]
            if driver_locations_within_radius
            else []
        )

        return matched_driver_ids

    async def __filter_drivers_by_hail_filters(
        self,
        driver_ids: Set[str],
        hail: Hail,
    ) -> list[Driver]:
        """
        Get all driver hashes and filter by hail preferences and filters
        """

        driver_sessions = await self.get_drivers_by_ids(driver_ids)

        filtered_drivers = [
            driver
            for driver in driver_sessions
            if hail.is_driver_viable_for_hail(driver)
        ]

        return filtered_drivers

    async def __get_drivers_by_operating_areas(
        self, operating_areas: set[MeterOperatingArea]
    ) -> set[str]:
        """
        Get all driver ids by operating area
        """
        drivers_by_operating_area = set()
        for operating_area in operating_areas:
            match operating_area:
                case MeterOperatingArea.NT:
                    drivers_by_operating_area.update(
                        await redis.smembers(NT_DRIVERS_KEY)
                    )
                case MeterOperatingArea.LANTAU:
                    drivers_by_operating_area.update(
                        await redis.smembers(LANTAU_DRIVERS_KEY)
                    )
                case _:
                    drivers_by_operating_area.update(
                        await redis.smembers(URBAN_DRIVERS_KEY)
                    )
        return drivers_by_operating_area

    def __publish_heartbeat_processing(
        self, driver_heartbeat: HeartBeat, driver: Driver
    ) -> None:
        """
        Publish the heartbeat processing to PubSub for analytics
        """
        heartbeat_id = str(uuid4())
        current_time = DateTimeUtils.utc_now_with_tz()
        meter_timestamp = MeterTimestamp(
            seconds=int(current_time.timestamp()), nanoseconds=0
        )
        self.pub_sub_publisher_service.publish_heartbeat(
            HeartbeatProcessing.create(
                meter_id=driver.license_plate,
                heartbeat_id=heartbeat_id,
                heartbeat_processing_payload=HeartbeatProcessingPayload(
                    id=heartbeat_id,
                    server_time=meter_timestamp,
                    device_time=meter_timestamp,
                    speed=driver_heartbeat.speed,
                    location=GeoPoint(
                        latitude=driver_heartbeat.lat, longitude=driver_heartbeat.lng
                    ),
                ),
            )
        )
