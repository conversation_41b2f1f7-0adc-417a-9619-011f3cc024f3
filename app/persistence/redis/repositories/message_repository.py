from app.services.logger_service import LoggerService
from app.persistence.redis.entities.message import Message


class MessageRepository:
    logger_service: LoggerService

    def __init__(
        self,
        logger_service: LoggerService,
    ) -> None:
        self.logger_service = logger_service

    async def get_message_by_id(self, message_id: str) -> Message | None:
        try:
            message: Message | None = await Message.get(message_id)
            return message
        except Exception:
            return None
