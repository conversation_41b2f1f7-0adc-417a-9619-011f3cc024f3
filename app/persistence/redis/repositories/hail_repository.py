from typing import Any
import asyncio
from datetime import datetime
import json
import time

from fastapi import BackgroundTasks

from app.contracts.tx.hailing_merchant_accepts_order import Hailing<PERSON>er<PERSON>tAccepts<PERSON><PERSON><PERSON>, HailingMerchantAcceptsOrderContent
from app.contracts.tx.hailing_merchant_approaching_destination import HailingMerchantApproachingDestination
from app.contracts.tx.hailing_merchant_cancels_order import HailingMerchantCancelsOrder
from app.contracts.tx.hailing_merchant_pick_up_confirmed import HailingMerchantPickUpConfirmed, HailingMerchantPickUpConfirmedContent
from app.contracts.tx.hailing_order_complete import HailingOrderComplete
from app.contracts.tx.hailing_user_cancels_order import HailingUserCancelsOrder
from app.contracts.tx.hailing_user_cancels_order_response import HailingUserCancelsOrderResponse
from app.models import auth
from app.models.auth import Auth
from app.models.client_type import ClientType
from app.services.tx_service import TxService
from app.sockets.messages.hail_cancelled_for_driver_message import HailCancelledForDriverMessage
from app.utils.datetime_utils import DateTimeUtils
from redis.asyncio.client import Pipeline
from app.contracts.common.hail_charges import CancellationFeeBreakdown, HailCharges
from app.contracts.hails.hail_cancelled_response import HailCancelledResponse
from app.contracts.hails.hail_id import HailId
from app.models.hail_status import HailStatus
from app.contracts.common.pin import Pin
from app.models.meter_operating_area import MeterOperatingArea
from app.persistence.firestore.entities.hail_configuration import HailConfiguration
from app.persistence.redis.entities.driver import Driver
from app.persistence.redis.entities.hail import (
    ACTIVE_HAILS_KEY,
    HAIL_GEO_KEY,
    Hail,
)
from app.services.web_socket_message_service import WebSocketMessageService
from app.persistence.redis.redis import redis
from app.services.fare_calculation_service import FareCalculationService
from app.services.logger_service import LoggerService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.services.pub_sub_publisher_service import PubSubPublisherService
from app.services.routing_service import RoutingService
from app.sockets.messages.driver_eta_message import DriverEtaMessage
from app.sockets.messages.driver_heartbeat_message import DriverHeartbeatMessage
from app.infrastructure.settings import settings
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.sockets.messages.hail_accepted_message import HailAcceptedMessage
from app.sockets.messages.hail_approaching_driver_message import (
    HailApproachingDriverMessage,
)
from app.sockets.messages.hail_approaching_rider_message import (
    HailApproachingRiderMessage,
)
from app.sockets.messages.hail_cancelled_message import HailCancelledMessage
from app.sockets.messages.hail_not_available_message import HailNotAvailableMessage
from app.sockets.messages.hail_on_going_rider_message import HailOnGoingRiderMessage
from app.sockets.messages.hail_pending_rider_message import HailPendingRiderMessage
from app.sockets.messages.hail_timed_out_message import HailTimedOutMessage
from app.sockets.socket_service import SocketService
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder


HAIL_KEY_SPACE = "__keyspace@0__:dash:hail:*"

URBAN_SCHEDULED_HAILS_KEY = "dash:scheduled:hails:urban"
NT_SCHEDULED_HAILS_KEY = "dash:scheduled:hails:nt"
LANTAU_SCHEDULED_HAILS_KEY = "dash:scheduled:hails:lantau"


class HailRepository:
    logger_service: LoggerService
    driver_repository: DriverRepository
    configuration_repository: ConfigurationsRepository
    socket_service: SocketService
    fare_calculation_service: FareCalculationService
    pub_sub_publisher_service: PubSubPublisherService
    routing_service: RoutingService
    tx_service: TxService
    web_socket_message_service: WebSocketMessageService

    def __init__(
        self,
        logger_service: LoggerService,
        driver_repository: DriverRepository,
        configuration_repository: ConfigurationsRepository,
        socket_service: SocketService,
        fare_calculation_service: FareCalculationService,
        pub_sub_publisher_service: PubSubPublisherService,
        routing_service: RoutingService,
        tx_service: TxService,
        web_socket_message_service: WebSocketMessageService,
    ) -> None:
        self.logger_service = logger_service
        self.driver_repository = driver_repository
        self.configuration_repository = configuration_repository
        self.socket_service = socket_service
        self.fare_calculation_service = fare_calculation_service
        self.pub_sub_publisher_service = pub_sub_publisher_service
        self.routing_service = routing_service
        self.tx_service = tx_service
        self.web_socket_message_service = web_socket_message_service

    async def get_hail_by_id(self, hail_id: str) -> Hail | None:
        try:
            hail: Hail | None = await Hail.get(hail_id)
            return hail
        except Exception:
            return None

    async def delete_one(self, hail_id: str) -> None:
        await Hail.delete(hail_id)

    async def delete_many(self, hails: list[Hail]) -> None:
        await Hail.delete_many(hails)

    async def get_live_hails_within_radius(
        self, origin: Pin, km_radius: float = settings.default_radius
    ) -> set[str]:
        """
        Get all the pending hails within the radius of the start location
        """
        pending_hails_within_radius = await redis.georadius(
            HAIL_GEO_KEY,
            origin.lng,
            origin.lat,
            km_radius,
            settings.default_unit_of_distance,
        )
        return set(pending_hails_within_radius)

    async def filter_hails_by_driver_filters(
        self,
        hail_ids: set[str],
        driver: Driver,
    ) -> list[Hail]:
        """
        Get all hails and filter by viability for driver
        """

        hails = await self.__get_hails_by_ids(hail_ids)

        filtered_hails = [
            hail for hail in hails if hail.is_driver_viable_for_hail(driver)
        ]

        return filtered_hails

    async def get_hails_by_status(self, *statuses: HailStatus) -> list[Hail]:
        """
        Get all hails by status
        """
        hail_keys = await redis.keys("dash:hail:*")
        get_hails_by_status = redis.register_script(
            """
            local status_list = cjson.decode(ARGV[1])
            local statuses = {}
            for _, status in ipairs(status_list) do
                statuses[status] = true
            end
            local matched_hails = {}

            for i = 1, #KEYS do
                local hail_id = KEYS[i]
                local hail = redis.call("HGETALL", hail_id)

                if table.getn(hail) > 0 then
                    local hail_table = {}

                    for j = 1, #hail, 2 do
                        hail_table[hail[j]] = hail[j + 1]
                    end

                    local status = hail_table["status"]
                    if status and statuses[status] then
                        table.insert(matched_hails, hail_table)
                    end
                end
            end

            return cjson.encode(matched_hails)

        """
        )

        raw_hails_data = await get_hails_by_status(
            keys=hail_keys, args=[json.dumps(statuses)]
        )

        return [Hail.model_validate(hail) for hail in json.loads(raw_hails_data)]

    async def notify_active_hails_of_driver_pin(self) -> None:
        active_hails = await self.__get_active_hail_set()
        self.logger_service.log(
            f"Active hails to notify of driver pin: {len(active_hails)}",
            {"active_hails": [*active_hails]},
        )

        hails_to_notify = await self.__get_hails_by_ids(
            {
                hail_id
                for hail_id in active_hails
                if self.socket_service.socket_id_is_active(hail_id)
            }
        )

        await asyncio.gather(
            *[
                self.__notify_hail_of_driver_pin(
                    hail_id=hail.id,
                    driver_id=hail.matched_driver.driver_id,
                    driver_service=self.driver_repository,
                )
                for hail in hails_to_notify
                if hail.matched_driver is not None
            ]
        )

    async def notify_hail_by_id_of_driver_pin(self, hail_id: str) -> None:
        hail = await self.get_hail_by_id(hail_id)
        if hail is None:
            return
        if hail.matched_driver is not None and hail.matched_driver.driver_id is not None:
            await self.__notify_hail_of_driver_pin(
                hail_id=hail.id,
                driver_id=hail.matched_driver.driver_id,
                driver_service=self.driver_repository,
            )

    async def add_to_active_hail_set(
        self, hail_id: str, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        await (pipeline or redis).sadd(ACTIVE_HAILS_KEY, hail_id)

    async def replace_active_hail_set(self, active_hails: set[str]) -> None:
        await redis.delete(ACTIVE_HAILS_KEY)
        if len(active_hails) > 0:
            await redis.sadd(ACTIVE_HAILS_KEY, *active_hails)

    async def remove_from_active_hail_set(
        self, hail_id: str, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        await (pipeline or redis).srem(ACTIVE_HAILS_KEY, hail_id)

    async def add_live_hail_to_geo_set(self, hail: Hail) -> None:
        """
        Add the hail location to the geo set
        """
        await redis.geoadd(HAIL_GEO_KEY, hail.to_id_location().to_coords())

    async def get_hails_for_user(self, user_id: str) -> list[Hail]:
        """
        Get all the hails for the user that are yet to occur
        """
        hails = await self.get_hails_by_status(
            HailStatus.PENDING, HailStatus.ACCEPTED, HailStatus.APPROACHING, HailStatus.ARRIVED
        )
        return [hail for hail in hails if hail.user_id == user_id and (hail.client_type == ClientType.DASH or hail.client_type is None)]

    async def get_matched_hails_for_driver(
        self, driver: Driver, statuses: set[HailStatus]
    ) -> list[Hail]:
        """
        Get all the hails that are matched to the driver
        """
        hails = await self.get_hails_by_status(*statuses)

        return [
            hail
            for hail in hails
            if (hail.matched_driver and hail.matched_driver.driver_id == driver.id) and hail.is_dash_hail
        ]

    async def get_all_active_hails(self) -> list[Hail]:
        """
        Get all the hails that are active
        """
        matched_hails = await self.get_hails_by_status(
            HailStatus.ACCEPTED, HailStatus.APPROACHING, HailStatus.ON_GOING, HailStatus.ARRIVED
        )

        # Remove fleet hails from matched hails
        matched_hails = [
            hail for hail in matched_hails if not hail.is_fleet_hail]

        return [
            hail
            for hail in matched_hails
            if hail.is_approaching
            or hail.is_arrived
            or hail.is_on_going
            or hail.is_within_approaching_threshold
        ]

    async def set_hail_to_pending(self, hail: Hail, auth: Auth, background_tasks: BackgroundTasks) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_pending", hail_id=hail.id)

        pipeline = redis.pipeline()
        await hail.set_to_pending(pipeline=pipeline)
        await self.remove_from_active_hail_set(hail.id, pipeline=pipeline)
        await pipeline.execute()

        self.logger_service.log(f"Hail {hail.id} set to PENDING")

        await self.web_socket_message_service.trigger_message(
            {hail.id},
            HailPendingRiderMessage(
                payload=hail.to_hail_rider_response()),
        )

        if auth is not None:
            background_tasks.add_task(self.add_pending_tx_event, hail, auth)

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_pending", hail_id=hail.id)

    async def set_hail_to_accepted(
        self, hail: Hail, driver: Driver, timestamp: datetime, auth: Auth, background_tasks: BackgroundTasks
    ) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_accepted", hail_id=hail.id, driver_id=driver.id)

        hail_config = await self.configuration_repository.get_hail_configuration()

        background_tasks.add_task(self.add_accept_tx_event, hail, driver, auth)

        pipeline = redis.pipeline()

        await hail.set_to_accepted(
            driver,
            hail_config=hail_config,
            fare_calculation_service=self.fare_calculation_service,
            pipeline=pipeline,
        )

        await self.__remove_hail_from_operating_area_set(hail, pipeline=pipeline)
        if hail.is_live:
            await self.__remove_live_hail_from_geo_set(hail, pipeline=pipeline)
        await pipeline.execute()

        if not hail.is_within_approaching_threshold:
            await self.web_socket_message_service.trigger_message(
                {hail.id},
                HailAcceptedMessage(
                    payload=hail.to_hail_rider_response()),
            )

        await self.notify_drivers_of_unavailable_hail(hail, timestamp)

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_accepted", hail_id=hail.id, driver_id=driver.id)

    async def set_hail_to_cancelled(
        self,
        hail: Hail,
        auth: Auth,
        is_admin_cancelled: bool = False,
        cancellation_fee: float = 0.0,
        cancellation_fee_breakdown: CancellationFeeBreakdown | None = None,
    ) -> HailCharges:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_cancelled", hail_id=hail.id)

        charges = HailCharges(cancellation_fee=cancellation_fee,
                              cancellation_fee_breakdown=cancellation_fee_breakdown)

        pipeline = redis.pipeline()
        await hail.set_to_cancelled(
            charges,
            pipeline=pipeline,
        )

        if auth is not None:
            await self.add_cancel_tx_event(hail, auth)
        elif is_admin_cancelled:
            await self.send_cancel_message(hail)

        await self.__remove_hail_from_operating_area_set(hail, pipeline=pipeline)
        if hail.is_live:
            await self.__remove_live_hail_from_geo_set(hail, pipeline=pipeline)

        now = DateTimeUtils.utc_now_with_tz()
        await pipeline.execute()

        self.logger_service.log(f"Hail {hail.id} set to CANCELLED")

        await self.notify_drivers_of_unavailable_hail(hail, now)

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_cancelled", hail_id=hail.id)

        return charges

    async def set_hail_to_timed_out(self, hail: Hail, timestamp: datetime) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_timed_out", hail_id=hail.id)

        pipeline = redis.pipeline()
        await hail.set_to_timed_out(pipeline=pipeline)
        await self.__remove_hail_from_operating_area_set(hail, pipeline=pipeline)
        if hail.is_live:
            await self.__remove_live_hail_from_geo_set(hail, pipeline=pipeline)
        await pipeline.execute()

        self.logger_service.log(f"Hail {hail.id} set to TIMED_OUT")

        await self.web_socket_message_service.trigger_message(
            {hail.id}, HailTimedOutMessage(payload=HailId(hail_id=hail.id))
        )

        await self.notify_drivers_of_unavailable_hail(hail, timestamp)

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_timed_out", hail_id=hail.id)

    async def set_hail_to_approaching(
        self,
        hail: Hail,
        timestamp: datetime,
        driver: Driver | None = None,
        auth: Auth | None = None,
        background_tasks: BackgroundTasks | None = None,
    ) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_approaching", hail_id=hail.id, driver_id=driver.id if driver else None)

        pipeline = redis.pipeline()

        hail_config: HailConfiguration | None = None
        if driver is not None:
            hail_config = await self.configuration_repository.get_hail_configuration()

        await hail.set_to_approaching(
            fare_calculation_service=self.fare_calculation_service,
            driver=driver,
            hail_config=hail_config,
            pipeline=pipeline,
        )

        if auth is not None and background_tasks is not None:
            background_tasks.add_task(
                self.add_approaching_tx_event, hail, auth)

        if hail.matched_driver is None:
            raise HailExceptionBuilder().no_matched_driver()

        await self.add_to_active_hail_set(hail.id, pipeline=pipeline)
        await self.__remove_hail_from_operating_area_set(hail, pipeline=pipeline)
        if hail.is_live:
            await self.__remove_live_hail_from_geo_set(hail, pipeline=pipeline)

        await pipeline.execute()

        self.logger_service.log(
            f"Hail {hail.id} set to APPROACHING with driver {driver.id if driver else None}"
        )

        await asyncio.gather(
            self.web_socket_message_service.trigger_message(
                {hail.id},
                HailApproachingRiderMessage(
                    payload=hail.to_hail_rider_response()
                ),
            ),
            self.web_socket_message_service.trigger_message(
                {hail.matched_driver.driver_id},
                HailApproachingDriverMessage(
                    payload=hail.to_matched_driver_hail_response()
                ),
            ),
            self.notify_drivers_of_unavailable_hail(hail, timestamp),
        )

        if driver is not None and driver.heart_beat is not None:
            await self.web_socket_message_service.trigger_message(
                {hail.id},
                DriverHeartbeatMessage(payload=driver.heart_beat),
            )

            async def send_message(msg: DriverEtaMessage) -> None:
                await self.web_socket_message_service.trigger_message({hail.id}, msg)

            await self.routing_service.send_eta_update(
                driver_location=driver.heart_beat,
                destination=hail.itinerary[0],
                send_message=send_message,
                hail_id=hail.id
            )

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_approaching", hail_id=hail.id, driver_id=driver.id if driver else None)

    async def set_hail_to_on_going(self, hail: Hail, trip_id: str, driver: Driver, auth: Auth, background_tasks: BackgroundTasks) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_on_going", hail_id=hail.id, trip_id=trip_id, driver_id=driver.id)

        await hail.set_to_on_going(trip_id=trip_id)

        self.logger_service.log(f"Hail {hail.id} set to ON_GOING")

        if auth is not None:
            background_tasks.add_task(
                self.add_on_going_tx_event, hail, driver, trip_id, auth)

        await self.web_socket_message_service.trigger_message(
            {hail.id},
            HailOnGoingRiderMessage(
                payload=hail.to_hail_rider_response()),
        )

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_on_going", hail_id=hail.id, trip_id=trip_id, driver_id=driver.id)

    async def set_hail_to_completed(self, hail: Hail, auth: Auth, background_tasks: BackgroundTasks) -> None:
        self.logger_service.log_function_start(
            "hail_repository", "set_hail_to_completed", hail_id=hail.id)

        await hail.set_to_completed()

        if auth is not None:
            background_tasks.add_task(self.add_completed_tx_event, hail, auth)

        self.logger_service.log(f"Hail {hail.id} set to COMPLETED")

        self.__publish_status_change(hail)

        self.logger_service.log_function_end(
            "hail_repository", "set_hail_to_completed", hail_id=hail.id)

    async def get_viable_hails_for_driver(
        self, driver: Driver, pin: Pin | None = None
    ) -> list[Hail]:
        hail_config = await self.configuration_repository.get_hail_configuration()

        # Lantau drivers should see all lantau hails regardless of distance
        if driver.operating_area is MeterOperatingArea.LANTAU:
            possible_hails = await self.__get_hails_for_operating_area(
                driver.operating_area
            )
        # All other drivers should only get scheduled hails in their operating area
        # and live hails within the default search radius
        else:
            hail_ids_for_operating_area = await self.__get_hails_for_operating_area(
                driver.operating_area
            )
            hails_for_operating_area = await self.__get_hails_by_ids(
                hail_ids_for_operating_area
            )
            possible_hails = {
                hail.id for hail in hails_for_operating_area if hail.is_scheduled
            }

            if pin:
                possible_hails.update(
                    await self.get_live_hails_within_radius(
                        pin,
                        km_radius=hail_config.driver_search.max_search_radius_km,
                    )
                )

        self.logger_service.debug(
            f"Possible hails for driver {driver.id}: {possible_hails}"
        )

        filtered_hails = await self.filter_hails_by_driver_filters(
            possible_hails, driver
        )
        self.logger_service.debug(
            f"Viable hails for driver {driver.id}: {[hail.id for hail in filtered_hails]}"
        )
        return filtered_hails

    async def __remove_live_hail_from_geo_set(
        self, hail: Hail, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        """
        Remove the hail location from the Redis GeoSet
        """
        await (pipeline or redis).zrem(HAIL_GEO_KEY, hail.id)

    async def __get_active_hail_set(self) -> set[str]:
        return await redis.smembers(ACTIVE_HAILS_KEY)

    async def __notify_hail_of_driver_pin(
        self, hail_id: str, driver_id: str, driver_service: DriverRepository
    ) -> None:
        driver = await driver_service.get_driver_by_id(driver_id)
        if driver is not None and driver.heart_beat is not None:
            hail = await self.get_hail_by_id(hail_id)
            if hail is None:
                return

            # Send driver location update
            await self.socket_service.send_personal_message(
                socket_id=hail_id,
                data=DriverHeartbeatMessage(payload=driver.heart_beat),
            )

            # Send ETA update
            async def send_message(msg: DriverEtaMessage) -> None:
                await self.socket_service.send_personal_message(
                    socket_id=hail_id,
                    data=msg
                )

            await self.routing_service.send_eta_update(
                driver_location=driver.heart_beat,
                destination=hail.itinerary[0],
                send_message=send_message,
                hail_id=hail_id
            )
        else:
            self.logger_service.warning(
                f"No driver pin found for driver {driver_id}")

    async def __get_hails_by_ids(self, hail_ids: set[str]) -> list[Hail]:
        """
        Get all hail hashes by hail ids
        """
        if len(hail_ids) == 0:
            return []

        get_hails = redis.register_script(
            """
            local matched_hails = {}

            for i = 1, #KEYS do
                local hail_id = KEYS[i]
                local hail = redis.call("HGETALL", "dash:hail:" .. hail_id)

                if table.getn(hail) > 0 then
                    local hail_table = {}

                    for j = 1, #hail, 2 do
                        hail_table[hail[j]] = hail[j + 1]
                    end

                    table.insert(matched_hails, hail_table)
                end
            end

            return cjson.encode(matched_hails)
        """
        )

        raw_hails_data = await get_hails(keys=list(hail_ids))

        hails = [Hail.model_validate(hail)
                 for hail in json.loads(raw_hails_data)]

        return hails

    async def add_hail_to_operating_area_set(self, hail: Hail) -> None:
        """
        Add scheduled hail to the correct operating area set
        """
        for operating_area in hail.operating_areas:
            match operating_area:
                case MeterOperatingArea.NT:
                    await redis.sadd(NT_SCHEDULED_HAILS_KEY, hail.id)
                case MeterOperatingArea.LANTAU:
                    await redis.sadd(LANTAU_SCHEDULED_HAILS_KEY, hail.id)
                case _:
                    await redis.sadd(URBAN_SCHEDULED_HAILS_KEY, hail.id)

    async def notify_drivers_of_unavailable_hail(self, hail: Hail, timestamp: datetime) -> None:
        """Notify drivers that hail is no longer available and remove from redis"""

        self.logger_service.log_function_start(
            "hail_repository", "notify_drivers_of_unavailable_hail", hail_id=hail.id)
        driver_ids_for_hail = await self.driver_repository.get_drivers_for_hail(hail.id)

        if len(driver_ids_for_hail) > 0:
            if (
                hail.matched_driver
                and hail.matched_driver.driver_id in driver_ids_for_hail
            ):
                driver_ids_for_hail.remove(hail.matched_driver.driver_id)

            await self.web_socket_message_service.trigger_message(
                driver_ids_for_hail,
                HailNotAvailableMessage(
                    payload=hail.id, timestamp=timestamp,),
            )

        await self.driver_repository.remove_drivers_for_hail(hail.id)

        self.logger_service.log_function_end(
            "hail_repository", "notify_drivers_of_unavailable_hail", hail_id=hail.id, driver_ids_for_hail=driver_ids_for_hail
        )

    async def __get_hails_for_operating_area(
        self, operating_area: MeterOperatingArea | None
    ) -> set[str]:
        """
        Get all scheduled hails for the operating area
        """
        match operating_area:
            case MeterOperatingArea.NT:
                return await redis.smembers(NT_SCHEDULED_HAILS_KEY)
            case MeterOperatingArea.LANTAU:
                return await redis.smembers(LANTAU_SCHEDULED_HAILS_KEY)
            case _:
                return await redis.smembers(URBAN_SCHEDULED_HAILS_KEY)

    async def __remove_hail_from_operating_area_set(
        self, hail: Hail, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        """
        Remove scheduled hail from the correct operating area set
        """
        for operating_area in hail.operating_areas:
            match operating_area:
                case MeterOperatingArea.NT:
                    await (pipeline or redis).srem(NT_SCHEDULED_HAILS_KEY, hail.id)
                case MeterOperatingArea.LANTAU:
                    await (pipeline or redis).srem(LANTAU_SCHEDULED_HAILS_KEY, hail.id)
                case _:
                    await (pipeline or redis).srem(URBAN_SCHEDULED_HAILS_KEY, hail.id)

    def __publish_status_change(self, hail: Hail) -> None:
        self.pub_sub_publisher_service.publish_hail_bronze_topic(
            hail.to_hail_proto())

    async def remove_hail_from_expansion_sets(self, hail: Hail) -> None:
        """
        Remove hail from operating area sets to stop ongoing expansion
        """
        pipeline = redis.pipeline()
        await self.__remove_hail_from_operating_area_set(hail, pipeline=pipeline)
        if hail.is_live:
            await self.__remove_live_hail_from_geo_set(hail, pipeline=pipeline)
        await pipeline.execute()

        self.logger_service.log(f"Removed hail {hail.id} from expansion sets")

    async def add_accept_tx_event(self, hail: Hail, driver: Driver, auth: Auth) -> None:
        distance = None
        eta = None

        if driver.heart_beat is not None and hail.itinerary:
            valhalla_response = await self.routing_service.get_route(
                origin=driver.heart_beat,
                destination=Pin(
                    lat=hail.itinerary[0].lat,
                    lng=hail.itinerary[0].lng,
                ),
            )
            if valhalla_response is not None:
                distance = self.routing_service.get_distance_from_route(
                    valhalla_response)
                eta = self.routing_service.get_eta_from_route(
                    valhalla_response)

        if auth is not None:
            await self.tx_service.add_tx_event(
                tx_id=hail.id,
                auth=auth,
                event=HailingMerchantAcceptsOrder(
                    content=HailingMerchantAcceptsOrderContent(
                        phone_number=driver.phone_number,
                        heart_beat_on_accepted=driver.heart_beat,
                        meter=driver.license_plate,
                        distance=distance,
                        eta=eta,
                        boost_amount=hail.boost_amount,
                        bonus_amount=hail.bonus_amount,
                    )
                ),
            )
            
    async def send_cancel_message(self, hail: Hail) -> None:
        """Send cancellation message to rider and driver"""
        timestamp = DateTimeUtils.utc_now_with_tz()
        await self.web_socket_message_service.trigger_message(
            {hail.id},
            HailCancelledMessage(
                payload=hail.to_hail_rider_response(), timestamp=timestamp),
        )
        
        if hail.matched_driver is not None:
            driver_payout = (
                hail.charges.cancellation_fee_breakdown.driver_payout
                if hail.charges and hail.charges.cancellation_fee_breakdown
                else (hail.charges.cancellation_fee if hail.charges else 0)
            )

            driver_response = HailCancelledResponse(
                hail_id=hail.id,
                cancellation_fee=driver_payout,
                cancellation_fee_breakdown=(
                    hail.charges.cancellation_fee_breakdown if hail.charges is not None else None
                ),
            )

            await self.web_socket_message_service.trigger_message(
                {hail.matched_driver.driver_id},
                HailCancelledForDriverMessage(
                    payload=driver_response, timestamp=timestamp),
            )

    async def add_cancel_tx_event(self, hail: Hail, auth: Auth) -> HailCharges:
        """Background task for adding cancellation tx event"""
        tx_event_response = await self.tx_service.add_tx_event(
            tx_id=hail.id, auth=auth, event=HailingUserCancelsOrder()
        )

        charges = HailCharges(cancellation_fee=0)
        if isinstance(tx_event_response, HailingUserCancelsOrderResponse):
            charges = HailCharges(
                cancellation_fee=tx_event_response.content.charges.cancellation_fee,
                cancellation_fee_breakdown=tx_event_response.content.charges.cancellation_fee_breakdown
            )
            # Update the hail with the charges from tx response
            await hail.update_charges(charges)
    
        await self.send_cancel_message(hail)

        return charges

    async def add_pending_tx_event(self, hail: Hail, auth: Auth) -> None:
        """Background task for adding pending tx event"""
        await self.tx_service.add_tx_event(
            tx_id=hail.id,
            auth=auth,
            event=HailingMerchantCancelsOrder(),
        )

    async def add_approaching_tx_event(self, hail: Hail, auth: Auth) -> None:
        """Background task for adding approaching tx event"""
        await self.tx_service.add_tx_event(
            tx_id=hail.id,
            auth=auth,
            event=HailingMerchantApproachingDestination()
        )

    async def add_on_going_tx_event(self, hail: Hail, driver: Driver, trip_id: str, auth: Auth) -> None:
        """Background task for adding on going tx event"""
        await self.tx_service.add_tx_event(
            tx_id=hail.id,
            auth=auth,
            event=HailingMerchantPickUpConfirmed(
                content=HailingMerchantPickUpConfirmedContent(
                    tx_id=trip_id,
                    meter_id=driver.license_plate,
                )
            ),
        )

    async def add_completed_tx_event(self, hail: Hail, auth: Auth) -> None:
        """Background task for adding completed tx event"""
        await self.tx_service.add_tx_event(
            tx_id=hail.id,
            auth=auth,
            event=HailingOrderComplete(),
        )
