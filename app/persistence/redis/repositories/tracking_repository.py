import asyncio
from datetime import datetime, timedelta
from app.persistence.redis.redis import redis
from app.services.logger_service import LoggerService
from app.utils.datetime_utils import DateTimeUtils


class TrackingRepository:
    __driver_mins_key = "dash:driver:minutes"
    __start_hash_key = "session_start"
    __driver_id_hash_key = "driver_id"
    __date_key_format = "%Y-%m-%d"

    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService):
        self.logger_service = logger_service

    def __get_driver_mins_key(self, driver_id: str) -> str:
        return f"{self.__driver_mins_key}:{driver_id}"

    async def set_driver_session_start(
        self, driver_id: str, session_start: datetime
    ) -> None:
        """
        Store the session start time for the driver for tracking minutes online
        """

        await redis.hset(
            self.__get_driver_mins_key(driver_id),
            mapping={
                self.__driver_id_hash_key: driver_id,
                self.__start_hash_key: session_start.isoformat(),
            },
        )
        self.logger_service.log(f"Session start set for driver {driver_id}")

    async def set_driver_session_end(
        self, driver_id: str, session_end: datetime
    ) -> None:
        """
        Get the minutes between the session start and end times and store them with their respective dates
        """

        session_start_str = await redis.hget(
            self.__get_driver_mins_key(driver_id), self.__start_hash_key
        )
        if not session_start_str:
            self.logger_service.warning(
                f"Session start not found for driver {driver_id}"
            )
            return

        session_start = DateTimeUtils.set_to_hk_tz(
            datetime.fromisoformat(session_start_str)
        )
        session_end = DateTimeUtils.set_to_hk_tz(session_end)

        current_session_start = session_start
        while current_session_start < session_end:
            next_day = current_session_start + timedelta(days=1)
            current_session_end = min(next_day, session_end)
            minutes = (current_session_end - current_session_start).seconds // 60
            await redis.hincrby(
                self.__get_driver_mins_key(driver_id),
                current_session_start.strftime(self.__date_key_format),
                minutes,
            )
            current_session_start = current_session_end

    async def get_driver_sessions(self) -> list[dict[str, str]]:
        """
        Get all driver sessions
        """
        driver_mins_keys = await redis.keys(f"{self.__driver_mins_key}:*")
        driver_sessions = await asyncio.gather(
            *[redis.hgetall(key) for key in driver_mins_keys]
        )
        return driver_sessions

    async def get_driver_sessions_for_date_range(
        self, start_date: datetime, end_date: datetime
    ) -> dict[str, dict[str, str]]:
        """
        Get all driver sessions filtered by date range
        """
        driver_sessions = await self.get_driver_sessions()

        for session in driver_sessions:
            del session[self.__start_hash_key]
            for key in session:
                if key != self.__driver_id_hash_key:
                    date = datetime.strptime(key, self.__date_key_format)
                    if date < start_date or date > end_date:
                        del session[key]

        return {
            session[self.__driver_id_hash_key]: session for session in driver_sessions
        }
