from typing import Awaitable, Callable, Type, TypeVar, overload
from pydantic import BaseModel
from redis.asyncio import Redis
from aioredlock import Aiored<PERSON>, Lock, LockError
from app.infrastructure.settings import settings
from app.services.logger_service import LoggerService
from app.utils.exceptions.redis_exception_builder import RedisExceptionBuilder

redis = Redis.from_url(settings.redis_om_url, decode_responses=True)
redlock = Aioredlock([settings.redis_om_url])


T = TypeVar("T", bound=BaseModel)


class RedisService:
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService):
        self.logger_service = logger_service

    async def with_lock(
        self, lock_key: str, func: Callable[..., Awaitable[T]], timeout: int = 1000
    ) -> T:
        lock: Lock | None = None
        try:
            lock = await redlock.lock(f"lock:{lock_key}", timeout)
            if lock:
                result = await func()
                return result
            else:
                self.logger_service.error(f"Failed to acquire lock for {lock_key}")
                raise RedisExceptionBuilder().lock_failed_to_create(lock_key)
        except LockError as e:
            raise e
        finally:
            if lock:
                await redlock.unlock(lock)

    async def keyExists(self, key: str) -> bool:
        return True if await redis.exists(key) == 1 else False

    @overload
    async def getCachedOrFetch(
        self,
        key: str,
        fetch: Callable[..., Awaitable[T]],
        ttl_seconds: int,
    ) -> str: ...

    @overload
    async def getCachedOrFetch(
        self,
        key: str,
        fetch: Callable[..., Awaitable[T]],
        ttl_seconds: int,
        model_type: Type[T],
    ) -> T: ...

    async def getCachedOrFetch(
        self,
        key: str,
        fetch: Callable[..., Awaitable[T]],
        ttl_seconds: int,
        model_type: Type[T] | None = None,
    ) -> T | str:
        async def fetch_and_cache() -> T:
            self.logger_service.debug(f"Fetching and caching data for key {key}")
            result = await fetch()
            await redis.setex(key, ttl_seconds, result.model_dump_json())
            return result

        cached = await redis.get(key)

        if cached is not None:
            if model_type is None:
                return cached

            try:
                return model_type.model_validate_json(cached)
            except Exception as e:
                self.logger_service.warning(
                    f"Failed to parse cached data for key {key}. Error: {e}"
                )
                return await fetch_and_cache()
        else:
            return await fetch_and_cache()

    async def deleteKey(self, key: str) -> None:
        await redis.delete(key)
