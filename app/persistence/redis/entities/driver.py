from datetime import datetime
from decimal import Decimal
import json

from app.contracts.common.heart_beat import HeartBeat
from app.contracts.common.pin import Pin
from app.contracts.drivers.create_driver_request import CreateDriverSessionRequest
from app.contracts.drivers.driver_session_heartbeat_response import (
    DriverSessionHeartbeatResponse,
)
from app.contracts.drivers.driver_session_response import DriverSessionResponse
from app.contracts.hails.fleet_hail_update_request import FleetHailUpdateRequest
from app.models.discount_rules import DiscountRules
from app.models.driver_status import DriverStatus
from app.contracts.common.fare_calculation import FareCalculation
from app.models.meter_operating_area import MeterOperatingArea
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.entities.fleet import Fleet
from app.persistence.firestore.entities.hail_configuration import (
    HailConfiguration,
)
from app.persistence.firestore.entities.meter import Meter, MeterSettings
from app.persistence.firestore.entities.vehicle import Vehicle
from app.persistence.redis.entities.base import BaseRedisEntity
from app.contracts.hails.create_hail_request import PreferenceMatrix
from app.services.fare_calculation_service import FareCalculationService
from app.utils.datetime_utils import DateTimeUtils


VEHICLE_CLASS_MATCH_MATRIX = {
    VehicleClass.FOUR_SEATER: {
        VehicleClass.FOUR_SEATER: True,
        VehicleClass.FIVE_SEATER: False,
        VehicleClass.COMFORT: False,
        VehicleClass.LUXURY: False,
    },
    VehicleClass.FIVE_SEATER: {
        VehicleClass.FOUR_SEATER: True,
        VehicleClass.FIVE_SEATER: True,
        VehicleClass.COMFORT: False,
        VehicleClass.LUXURY: False,
    },
    VehicleClass.COMFORT: {
        VehicleClass.FOUR_SEATER: True,
        VehicleClass.FIVE_SEATER: False,
        VehicleClass.COMFORT: True,
        VehicleClass.LUXURY: False,
    },
    VehicleClass.LUXURY: {
        VehicleClass.FOUR_SEATER: True,
        VehicleClass.FIVE_SEATER: True,
        VehicleClass.COMFORT: True,
        VehicleClass.LUXURY: True,
    },
}
VEHICLE_HIERARCHY = [
    VehicleClass.LUXURY,
    VehicleClass.COMFORT,
    VehicleClass.FIVE_SEATER,
    VehicleClass.FOUR_SEATER,
]


class Driver(BaseRedisEntity):
    status: DriverStatus
    driver_id: str
    name: str
    name_tc: str
    phone_number: str
    license_plate: str
    vehicle_make: str
    vehicle_model: str
    vehicle_class: VehicleClass
    seats: int
    operating_area: MeterOperatingArea | None = None
    meter_str: str
    """Str representation of the meter in json format"""
    markup_str: str | None = None
    """Str representation of the driver markup map in json logic format"""
    service_filters_str: str
    """Str representation of the driver filter matrix in json format"""
    heart_beat_str: str | None = None
    created_at: datetime
    updated_at: datetime

    @property
    def service_filters(self) -> ServiceFilters:
        return ServiceFilters.model_validate_json(self.service_filters_str)

    @property
    def heart_beat(self) -> HeartBeat | None:
        return (
            HeartBeat.model_validate_json(self.heart_beat_str)
            if self.heart_beat_str
            else None
        )

    @property
    def meter(self) -> MeterSettings:
        return MeterSettings.model_validate_json(self.meter_str)

    @property
    def markup_dict(self) -> dict[VehicleClass, str] | None:
        return (
            json.loads(self.markup_str)
            if self.markup_str and len(self.markup_str) > 0
            else None
        )

    @property
    def is_available(self) -> bool:
        return self.status is DriverStatus.AVAILABLE

    @staticmethod
    async def from_fleet_hail_update_request(
        fleet_hail_update_request: FleetHailUpdateRequest,
        meter: Meter,
    ) -> "Driver":
        now = DateTimeUtils.utc_now_with_tz()
        driver = Driver(
            pk=fleet_hail_update_request.driver_phone_number,
            status=DriverStatus.FLEET,
            driver_id=fleet_hail_update_request.driver_phone_number,
            name=fleet_hail_update_request.driver_name,
            name_tc="",
            phone_number=fleet_hail_update_request.driver_phone_number,
            license_plate=fleet_hail_update_request.driver_license_plate,
            vehicle_make="",
            vehicle_model="",
            vehicle_class=VehicleClass.FOUR_SEATER,
            seats=4,
            operating_area=MeterOperatingArea.URBAN,
            meter_str=meter.settings.model_dump_json(),
            markup_str="{}",
            service_filters_str="{}",
            heart_beat_str=None,
            created_at=now,
            updated_at=now,
        )
        return driver

    @staticmethod
    async def from_create_driver_session_request(
        driver_id: str,
        create_driver_session_request: CreateDriverSessionRequest,
        meter: Meter,
        vehicle: Vehicle,
        fleet: Fleet | None,
    ) -> "Driver":
        now = DateTimeUtils.utc_now_with_tz()
        driver = Driver(
            pk=driver_id,
            status=DriverStatus.AVAILABLE,
            driver_id=driver_id,
            name=create_driver_session_request.name,
            name_tc=create_driver_session_request.name_tc,
            phone_number=create_driver_session_request.phone_number,
            license_plate=create_driver_session_request.license_plate,
            vehicle_make=vehicle.make,
            vehicle_model=vehicle.model,
            vehicle_class=vehicle.fleet_class,
            seats=vehicle.seats,
            operating_area=meter.settings.operating_area,
            meter_str=meter.settings.model_dump_json(),
            markup_str=(
                json.dumps(fleet.markup)
                if (fleet is not None and fleet.markup is not None)
                else ""
            ),
            service_filters_str=create_driver_session_request.filter_matrix.model_dump_json(),
            heart_beat_str=(
                create_driver_session_request.heart_beat.model_dump_json()
                if create_driver_session_request.heart_beat
                else None
            ),
            created_at=now,
            updated_at=now,
        )
        await driver.save()
        return driver

    def to_driver_session_response(self) -> DriverSessionResponse:
        return DriverSessionResponse(
            status=self.status,
            driver_id=self.driver_id,
            name=self.name,
            name_tc=self.name_tc,
            phone_number=self.phone_number,
            license_plate=self.license_plate,
            is_dash_meter=self.meter.is_dash_meter,
            fleet_id=self.meter.fleet_id,
            vehicle_id=self.meter.vehicle_id,
            vehicle_make=self.vehicle_make,
            vehicle_model=self.vehicle_model,
            vehicle_class=self.vehicle_class,
            seats=self.seats,
            filter_matrix=ServiceFilters.model_validate_json(self.service_filters_str),
            created_at=self.created_at,
            updated_at=self.updated_at,
            operating_area=self.operating_area,
        )

    def to_driver_session_heartbeat_response(self) -> DriverSessionHeartbeatResponse:
        return DriverSessionHeartbeatResponse(
            status=self.status,
            driver_id=self.driver_id,
            name=self.name,
            name_tc=self.name_tc,
            phone_number=self.phone_number,
            license_plate=self.license_plate,
            is_dash_meter=self.meter.is_dash_meter,
            fleet_id=self.meter.fleet_id,
            vehicle_id=self.meter.vehicle_id,
            vehicle_make=self.vehicle_make,
            vehicle_model=self.vehicle_model,
            vehicle_class=self.vehicle_class,
            seats=self.seats,
            filter_matrix=ServiceFilters.model_validate_json(self.service_filters_str),
            created_at=self.created_at,
            updated_at=self.updated_at,
            heart_beat=self.heart_beat,
            operating_area=self.operating_area,
        )

    async def set_to_offline(self) -> None:
        self.status = DriverStatus.OFFLINE
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        await self.save()

    async def set_heart_beat(self, heart_beat: HeartBeat) -> None:
        self.heart_beat_str = heart_beat.model_dump_json()
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        await self.save()

    def get_markup_for_vehicle_preferences(
        self,
        base_fare: Decimal,
        tunnel_fee: Decimal,
        datetime: datetime,
        origin: Pin,
        destination: Pin,
        hail_config: HailConfiguration,
        vehicle_preferences: PreferenceMatrix,
        fare_calculation_service: FareCalculationService,
        discount_rules: DiscountRules | None = None,
        boost_amount: float = 0.0,
    ) -> tuple[VehicleClass, FareCalculation, str | None]:
        hierarchy = [
            vehicle_class
            for vehicle_class in VEHICLE_HIERARCHY
            if vehicle_class in vehicle_preferences
        ]

        highest_matching_vehicle_class: VehicleClass | None = None
        markup_logic: str | None = None

        # Get highest matching vehicle class to calculate the markup logic
        for vehicle_class in hierarchy:
            if VEHICLE_CLASS_MATCH_MATRIX[self.vehicle_class][vehicle_class] and (
                len(vehicle_preferences[vehicle_class]) < 1
                or self.meter.fleet_id in vehicle_preferences[vehicle_class]
            ):
                highest_matching_vehicle_class = vehicle_class
                markup_logic = (
                    self.markup_dict.get(vehicle_class) if self.markup_dict else None
                )
                break

        highest_matching_vehicle_class = (
            highest_matching_vehicle_class or VEHICLE_HIERARCHY[-1]
        )

        fare_calculation = fare_calculation_service.calculate_fare(
            fare=float(base_fare),
            tunnel_fee=float(tunnel_fee),
            fleet_booking_fee_markup=markup_logic,
            datetime=datetime,
            origin=origin,
            destination=destination,
            dash_transaction_fees=self.meter.dash_transaction_fees,
            operating_area=self.meter.operating_area if self.meter else None,
            hail_config=hail_config,
            vehicle_class=highest_matching_vehicle_class,
            discount_rules=discount_rules,
            boost_amount=boost_amount,
        )
        return (highest_matching_vehicle_class, fare_calculation, markup_logic)

    class Meta:
        model_key_prefix = "driver"
