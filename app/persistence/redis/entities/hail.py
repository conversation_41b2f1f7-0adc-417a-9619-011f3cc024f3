from decimal import Decimal
from pydantic import Field
import json

from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.common.fleet_vehicle import FleetVehicleType
from app.contracts.common.heart_beat import HeartBeat
from app.contracts.common.payment_details import PaymentDetails
from app.contracts.common.pin import Pin
from app.contracts.common.trip_itinerary import TripItinerary
from app.contracts.hails.create_hail_request_v3 import CreateHailRequestV3
from app.contracts.hails.fee_settings import FeeSettings
from app.contracts.hails.hail_driver_response import HailDriverResponse
from app.contracts.hails.hail_response import HailResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetOptionMatrixV2
from app.contracts.matrix.hail_option_matrix import HailOptionMatrix
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.common.hail_charges import HailCharges
from app.contracts.matrix.hail_option_matrix_v2 import HailOptionMatrixV2
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.contracts.merchant_campaigns.merchant_campaign import MerchantCampaign
from app.models.client_type import ClientType
from app.models.driver_status import DriverStatus
from app.models.hail_platform_type import HailPlatformType
from app.models.id_location import IdLocation
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.notification_trigger_event_type import NotificationTriggerEventType
from app.models.service_filters import ServiceFilters
from app.models.vehicle_class_fleet_preferences import VehicleClassFleetPreferences
from app.models.hail_type import HailType
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.hails.matched_driver import MatchedDriver
from app.persistence.firestore.entities.hail_configuration import (
    HailConfiguration,
    TimeTriggersConfiguration,
)
from app.persistence.redis.entities.base import BaseRedisEntity
from app.contracts.hails.create_hail_request import (
    CreateHailRequest,
    PreferenceMatrix,
)
from app.models.hail_status import HailStatus
from datetime import datetime, timedelta, timezone

from app.persistence.redis.entities.driver import VEHICLE_CLASS_MATCH_MATRIX, Driver
from app.services.fare_calculation_service import FareCalculationService
from app.services.vehicles_class_service import VehiclesClassService
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from dash_proto.beam import hail_pb2  # type: ignore
from redis.asyncio.client import Pipeline

HAIL_GEO_KEY = "dash:geo:hails"
ACTIVE_HAILS_KEY = "dash:active:hails"


TIME_DELTA_TO_APPROACHING_MINS = 15
"""
The time delta threshold in minutes to consider a hail active
(e.g. within 15 minutes or less from the current time)
"""

TIME_DELTA_TO_LIVE_HAIL_TIMEOUT_MINS = 15
"""
The time delta threshold in minutes to consider a live hail request as timed out
(e.g. 15 minutes or more from the last status update time)
"""

TIME_DELTA_FOR_UNMATCHED_SCHEDULED_HAILS_MINS = 30
"""
The time delta threshold in minutes to notify a rider of a scheduled hail still unmatched
(e.g. 30 minutes or less from the current time)
"""

TIME_DELTA_FOR_SCHEDULED_HAIL_MINS = 20
"""
The time delta threshold in minutes to consider a hail to be scheduled
(e.g. 50 minutes or more from the current time)
"""

TIME_DELTA_FOR_HAIL_TO_EXPIRE_DAYS = 1
"""
The time delta to expire the hail record in redis
"""

TIME_DELTA_FOR_DRIVER_NOTIFICATION_OF_SCHEDULED_HAIL_MINS = 60
"""
The time delta threshold in minutes to notify a driver of a scheduled hail
(e.g. 60 minutes or less from the current time)
"""


class Hail(BaseRedisEntity):
    user_id: str

    user_phone_number: str

    platform_type: HailPlatformType = HailPlatformType.DASH
    """Type of hailing, either DASH or FLEET"""

    fleet_partner_key: str | None = None
    """Fleet partner key for fleet hails"""

    language: LocalizationLanguage
    """Language of requesting rider"""

    base_fare_estimation: Decimal | None = None
    """Base fare estimation for the hail request as estimated by the Fare Estimation service"""

    base_fare_estimations_by_area_str: str | None = None
    """Base fare estimations by operating area for the hail request as estimated by the Fare Estimation service"""

    time: datetime

    trip_id: str | None = None
    """The trip id created by the meter or by the system at pick up time"""

    type: HailType = HailType.LIVE

    status: HailStatus = HailStatus.PENDING

    client_type: ClientType = ClientType.DASH

    operating_area: MeterOperatingArea | None = None

    operating_areas_str: str | None = None
    
    is_fixed_price: bool = False

    trip_estimation_str: str
    """Stringified JSON representation of the TripEstimation object"""

    itinerary_str: str
    """Stringified JSON representation of the TripItinerary object"""

    payment_details_str: str
    """Stringified JSON representation of the PaymentDetails object"""

    preference_matrix_str: str
    """Stringified JSON representation of the vehicle class and fleet preference matrix"""

    min_max_fare_calculations_str: str
    """Stringified JSON representation of the estimated fee range"""

    filter_matrix_str: str | None = None
    """Stringified JSON representation of the filter matrix"""

    matched_driver_str: str = ""
    """Stringified JSON representation of the matched driver"""

    charges_str: str | None = None
    """Stringified JSON representation of the charges"""

    prioritize_favorite_drivers_int: int | None = 0
    """Send hail request to favorite drivers first"""

    double_tunnel_fee_int: int | None = 0
    """Double the tunnel fee"""

    favorite_drivers_online_str: str | None = None
    """Stringified JSON representation of the favorite drivers online ids"""

    notifications_sent_str: str | None = None
    """Stringified JSON representation of the notifications sent"""

    applicable_discounts_str: str | None = None
    """Stringified JSON representation of the applicable campaigns"""

    tunnel_fees_str: str | None = None
    """Stringified JSON representation of the tunnel fees"""

    driver_count_by_radius_str: str | None = None
    """Stringified JSON representation of the driver count by radius"""

    boost_amount: float | None = None
    """Optional boost amount to prioritize this hail request"""

    bonus_amount: Decimal = Field(
        default=Decimal(0), ge=0, examples=[Decimal(5.00)]
    )
    """Bonus amount to incentivize drivers to accept this hail request"""

    merchant_campaign_id: str | None = None
    """The merchant campaign id for bonus applied to this hail request"""

    fleet_vehicle_str: str | None = None
    """Stringified JSON representation of the fleet vehicle type"""

    created_at: datetime

    updated_at: datetime

    accepted_at: datetime | None = None
    """The time at which the hail was accepted by a driver"""

    approaching_at: datetime | None = None
    """The time at which the hail was approaching the rider"""

    arrived_at: datetime | None = None
    """The time at which the hail was arrived at the rider's location"""

    on_going_at: datetime | None = None
    """The time at which the hail was on going"""

    completed_at: datetime | None = None
    """The time at which the hail was completed"""

    timed_out_at: datetime | None = None
    """The time at which the hail was timed out"""

    version: int = 1
    """The version of the hail entity"""

    @property
    def is_scheduled(self) -> bool:
        return self.type == HailType.SCHEDULED

    @property
    def is_live(self) -> bool:
        return self.type == HailType.LIVE

    @property
    def is_pending(self) -> bool:
        return self.status == HailStatus.PENDING

    @property
    def is_accepted(self) -> bool:
        return self.status == HailStatus.ACCEPTED

    @property
    def is_approaching(self) -> bool:
        return self.status == HailStatus.APPROACHING

    @property
    def is_on_going(self) -> bool:
        return self.status == HailStatus.ON_GOING

    @property
    def is_cancelled(self) -> bool:
        return self.status == HailStatus.CANCELLED

    @property
    def is_timed_out(self) -> bool:
        return self.status == HailStatus.TIMED_OUT

    @property
    def is_completed(self) -> bool:
        return self.status == HailStatus.COMPLETED

    @property
    def is_arrived(self) -> bool:
        return self.status == HailStatus.ARRIVED

    @property
    def is_fleet_hail(self) -> bool:
        return self.platform_type == HailPlatformType.FLEET

    @property
    def is_dash_hail(self) -> bool:
        return self.platform_type == HailPlatformType.DASH

    @property
    def can_be_updated(self) -> bool:
        return not self.is_completed and not self.is_cancelled

    @property
    def payment_details(self) -> PaymentDetails:
        return PaymentDetails.model_validate_json(self.payment_details_str)

    @property
    def trip_estimation(self) -> TripEstimation:
        return TripEstimation.model_validate_json(self.trip_estimation_str)

    @property
    def operating_areas(self) -> set[MeterOperatingArea]:
        return (
            {MeterOperatingArea[area]
                for area in json.loads(self.operating_areas_str)}
            if self.operating_areas_str
            else set()
        )

    @property
    def base_fare_estimations_by_area(self) -> dict[MeterOperatingArea, Decimal] | None:
        return (
            {
                MeterOperatingArea[area]: Decimal(value)
                for area, value in json.loads(
                    self.base_fare_estimations_by_area_str
                ).items()
            }
            if self.base_fare_estimations_by_area_str
            else None
        )

    @property
    def itinerary(self) -> list[TripItinerary]:
        return [
            TripItinerary.model_validate(itinerary)
            for itinerary in json.loads(self.itinerary_str)
        ]

    @property
    def vehicle_class_fleet_preferences(self) -> VehicleClassFleetPreferences:
        return VehicleClassFleetPreferences.model_validate(
            {"preference_matrix": json.loads(self.preference_matrix_str)}
        )

    @property
    def min_max_fare_calculations(
        self,
    ) -> tuple[FareCalculation, ...]:
        return tuple(
            [
                FareCalculation.model_validate_json(calc)
                for calc in json.loads(self.min_max_fare_calculations_str)
            ]
        )

    @property
    def filter_matrix(self) -> ServiceFilters:
        return ServiceFilters.model_validate_json(self.filter_matrix_str or "{}")

    @property
    def matched_driver(self) -> MatchedDriver | None:
        return (
            MatchedDriver.model_validate_json(self.matched_driver_str)
            if len(self.matched_driver_str) > 0
            else None
        )

    @property
    def charges(self) -> HailCharges | None:
        return (
            HailCharges.model_validate_json(self.charges_str)
            if self.charges_str
            else None
        )

    @property
    def prioritize_favorite_drivers(self) -> bool:
        return self.prioritize_favorite_drivers_int == 1

    @property
    def double_tunnel_fee(self) -> bool:
        return self.double_tunnel_fee_int == 1

    @property
    def favorite_drivers_online(self) -> set[str]:
        return (
            set(json.loads(self.favorite_drivers_online_str))
            if self.favorite_drivers_online_str
            else set()
        )

    @property
    def notifications_sent(self) -> set[str]:
        return (
            set(json.loads(self.notifications_sent_str))
            if self.notifications_sent_str
            else set()
        )

    @property
    def fleet_vehicle(self) -> FleetVehicleType | None:
        return (
            FleetVehicleType.model_validate_json(self.fleet_vehicle_str)
            if self.fleet_vehicle_str
            else None
        )

    @property
    def applicable_discounts(self) -> ApplicableDiscounts | None:
        return (
            ApplicableDiscounts.model_validate_json(
                self.applicable_discounts_str)
            if self.applicable_discounts_str
            else None
        )

    @property
    def tunnel_fees(self) -> list[TunnelFee]:
        return (
            [
                TunnelFee.model_validate_json(tunnel_fee)
                for tunnel_fee in json.loads(self.tunnel_fees_str)
            ]
            if self.tunnel_fees_str
            else []
        )

    @property
    def driver_count_by_radius(self) -> dict[float, int] | None:
        return (
            {
                float(radius): int(count)
                for radius, count in json.loads(self.driver_count_by_radius_str).items()
            }
            if self.driver_count_by_radius_str
            else None
        )

    @property
    def is_within_approaching_threshold(self) -> bool:
        return self.time <= DateTimeUtils.utc_now_with_tz() + timedelta(
            minutes=TIME_DELTA_TO_APPROACHING_MINS
        )

    @property
    def is_within_unmatched_scheduled_hail_threshold(self) -> bool:
        now_utc = DateTimeUtils.utc_now_with_tz()
        return (
            self.is_scheduled
            and self.time
            <= now_utc
            + timedelta(minutes=TIME_DELTA_FOR_UNMATCHED_SCHEDULED_HAILS_MINS)
            and self.time > now_utc
        )

    @property
    def is_in_the_past(self) -> bool:
        return self.time <= DateTimeUtils.utc_now_with_tz()

    @property
    def is_past_scheduled_hail_timeout(self) -> bool:
        return self.is_scheduled and self.is_in_the_past

    @property
    def is_available_for_socket(self) -> bool:
        return not self.is_completed and not self.is_cancelled

    @staticmethod
    async def from_create_hail_request(
        user_id: str,
        create_hail_request: CreateHailRequest,
        phone_number: str,
        options_matrix: HailOptionMatrixV2 | HailFleetOptionMatrixV2,
        applicable_merchant_campaign: MerchantCampaign | None = None,
    ) -> "Hail":
        now = DateTimeUtils.utc_now_with_tz()

        hail_type: HailType
        order_time = create_hail_request.time
        if order_time <= now:
            hail_type = HailType.LIVE
        elif order_time >= now + timedelta(
            minutes=TIME_DELTA_FOR_SCHEDULED_HAIL_MINS
        ):
            hail_type = HailType.SCHEDULED
        else:
            raise HailExceptionBuilder().invalid_schedule_time(
                TIME_DELTA_FOR_SCHEDULED_HAIL_MINS
            )

        if create_hail_request.platform_type == "FLEET" and isinstance(
            options_matrix, HailFleetOptionMatrixV2
        ) and create_hail_request.fleet_vehicle_type_key:
            min_max_fare_calculations = options_matrix.min_max_fare_calculations(
                create_hail_request.fleet_vehicle_type_key
            )
        elif isinstance(options_matrix, HailOptionMatrixV2):
            min_max_fare_calculations = options_matrix.min_max_fare_calculations(
                create_hail_request.vehicle_preferences
            )
        else:
            min_max_fare_calculations = None

        if min_max_fare_calculations is None:
            raise HailExceptionBuilder().no_hail_options_available()

        operating_areas: set[MeterOperatingArea]
        if isinstance(options_matrix, HailOptionMatrixV2):
            if options_matrix.operating_area_options is None:
                operating_areas = set()
            else:
                operating_areas = {
                    *options_matrix.operating_area_options.keys()}
        elif isinstance(options_matrix, HailOptionMatrix) and options_matrix.operating_area:
            operating_areas = {options_matrix.operating_area}
        else:
            operating_areas = set()

        if create_hail_request.operating_areas:
            operating_areas = operating_areas & set(
                create_hail_request.operating_areas)

        base_fare_estimations_by_area_str = None
        operating_area = None
        if isinstance(options_matrix, HailOptionMatrixV2):
            base_fare_estimations_by_area_str = json.dumps(
                {
                    area: float(fare)
                    for area, fare in options_matrix.base_fare_estimations_by_area().items()
                }
            )

        if isinstance(options_matrix, HailOptionMatrixV2):
            operating_area = options_matrix.operating_area

        hail = Hail(
            pk=create_hail_request.tx_id,
            user_id=user_id,
            user_phone_number=phone_number,
            platform_type=create_hail_request.platform_type,
            fleet_partner_key=create_hail_request.fleet_partner_key,
            language=create_hail_request.language,
            base_fare_estimation=(
                options_matrix.estimated_base_fare
                if isinstance(options_matrix, HailOptionMatrix)
                else None
            ),
            base_fare_estimations_by_area_str=(
                base_fare_estimations_by_area_str),
            operating_area=(operating_area),
            operating_areas_str=json.dumps([*operating_areas]),
            trip_estimation_str=create_hail_request.trip_estimation.model_dump_json(),
            itinerary_str=json.dumps(
                [itinerary.model_dump()
                 for itinerary in create_hail_request.itinerary]
            ),
            time=order_time,
            payment_details_str=create_hail_request.payment_details.model_dump_json(),
            fleet_vehicle_str=create_hail_request.fleet_vehicle_type.model_dump_json(
            ) if create_hail_request.fleet_vehicle_type else None,
            min_max_fare_calculations_str=json.dumps(
                [calc.model_dump_json() for calc in min_max_fare_calculations]
            ),
            preference_matrix_str=json.dumps(
                create_hail_request.vehicle_preferences),
            filter_matrix_str=(
                create_hail_request.filters.model_dump_json()
                if create_hail_request.filters
                else None
            ),
            prioritize_favorite_drivers_int=int(
                create_hail_request.prioritize_favorite_drivers
            ),
            double_tunnel_fee_int=int(
                isinstance(options_matrix, HailOptionMatrixV2)
                and create_hail_request.double_tunnel_fee
                and any(
                    fee.is_double_fee_applicable for fee in options_matrix.tunnel_fees
                )
            ),
            applicable_discounts_str=(
                ApplicableDiscounts.model_validate(
                    options_matrix.applicable_discounts
                ).model_dump_json()
                if (
                    (isinstance(options_matrix, HailOptionMatrixV2)
                     or
                     isinstance(options_matrix, HailFleetOptionMatrixV2))
                    and options_matrix.applicable_discounts
                )
                else None
            ),
            tunnel_fees_str=(
                json.dumps(
                    [
                        tunnel_fee.model_dump_json()
                        for tunnel_fee in options_matrix.tunnel_fees
                    ]
                )
                if isinstance(options_matrix, HailOptionMatrixV2)
                else None
            ),
            bonus_amount=applicable_merchant_campaign.bonus_amount if applicable_merchant_campaign else None,
            merchant_campaign_id=applicable_merchant_campaign.campaign_id if applicable_merchant_campaign else None,
            type=hail_type,
            status=HailStatus.PENDING,
            created_at=now,
            updated_at=now,
            version=1,
        )
        await hail.save()

        time_difference = order_time - DateTimeUtils.utc_now_with_tz()
        expiration_in_seconds = (
            time_difference.total_seconds()
            + timedelta(days=TIME_DELTA_FOR_HAIL_TO_EXPIRE_DAYS).total_seconds()
        )
        await hail.expire(int(expiration_in_seconds))

        return hail

    @staticmethod
    async def from_create_hail_request_v3(
        create_hail_request: CreateHailRequestV3,
        user_id: str,
        phone_number: str,
        min_max_fare_calculations: tuple[FareCalculation, FareCalculation],
        tunnel_fare_estimation: list[TunnelFee],
        available_operating_areas: list[MeterOperatingArea],
    ) -> "Hail":
        now = DateTimeUtils.utc_now_with_tz()

        hail_type: HailType
        order_time = create_hail_request.time

        preference_matrix: PreferenceMatrix = {
            vehicle_class: []
            for vehicle_class in create_hail_request.prefer_vehicle_classes
        }

        if order_time <= now:
            hail_type = HailType.LIVE
        elif order_time >= now + timedelta(
            minutes=TIME_DELTA_FOR_SCHEDULED_HAIL_MINS
        ):
            hail_type = HailType.SCHEDULED
        else:
            raise HailExceptionBuilder().invalid_schedule_time(
                TIME_DELTA_FOR_SCHEDULED_HAIL_MINS
            )
        hail = Hail(
            pk=create_hail_request.tx_id,
            user_id=user_id,
            user_phone_number=phone_number,
            platform_type=create_hail_request.platform_type,
            fleet_partner_key=create_hail_request.fleet_partner_key,
            client_type=create_hail_request.client_type,
            language=create_hail_request.language,
            base_fare_estimation=min_max_fare_calculations[0].total,
            trip_estimation_str=create_hail_request.trip_estimation.model_dump_json(),
            itinerary_str=json.dumps(
                [itinerary.model_dump()
                 for itinerary in create_hail_request.itinerary]
            ),
            time=order_time,
            operating_areas_str=json.dumps([*available_operating_areas]),
            payment_details_str=create_hail_request.payment_details.model_dump_json(),
            fleet_vehicle_str=create_hail_request.fleet_vehicle_type.model_dump_json(
            ) if create_hail_request.fleet_vehicle_type else None,
            min_max_fare_calculations_str=json.dumps(
                [calc.model_dump_json() for calc in min_max_fare_calculations]
            ),
            filter_matrix_str=(
                create_hail_request.filters.model_dump_json()
                if create_hail_request.filters
                else None
            ),
            prioritize_favorite_drivers_int=int(
                create_hail_request.prioritize_favorite_drivers
            ),
            double_tunnel_fee_int=int(
                1 if create_hail_request.is_double_tunnel_fee else 0
            ),
            applicable_discounts_str=(
                create_hail_request.applicable_discounts.model_dump_json()
            ),
            tunnel_fees_str=(
                json.dumps(
                    [
                        tunnel_fee.model_dump_json()
                        for tunnel_fee in tunnel_fare_estimation
                    ]
                )
            ),
            preference_matrix_str=json.dumps(preference_matrix),
            type=hail_type,
            status=HailStatus.PENDING,
            created_at=now,
            updated_at=now,
            is_fixed_price=create_hail_request.is_fixed_price,
            version=3,
        )
        await hail.save()

        time_difference = order_time - DateTimeUtils.utc_now_with_tz()
        expiration_in_seconds = (
            time_difference.total_seconds()
            + timedelta(days=TIME_DELTA_FOR_HAIL_TO_EXPIRE_DAYS).total_seconds()
        )
        await hail.expire(int(expiration_in_seconds))

        return hail

    def to_hail_rider_response(self) -> HailRiderResponse:
        return HailRiderResponse(
            id=self.id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            user_id=self.user_id,
            user_phone_number=self.user_phone_number,
            language=self.language,
            trip_estimation=self.trip_estimation,
            itinerary=self.itinerary,
            trip_id=self.trip_id,
            time=self.time,
            platform_type=self.platform_type,
            payment_details=self.payment_details,
            min_max_fare_calculations=self.min_max_fare_calculations,
            preference_matrix=self.vehicle_class_fleet_preferences.preference_matrix,
            filters=(
                ServiceFilters.model_validate_json(self.filter_matrix_str)
                if self.filter_matrix_str
                else None
            ),
            type=self.type,
            status=self.status,
            matched_driver=self.matched_driver,
            charges=self.charges,
            prioritize_favorite_drivers=self.prioritize_favorite_drivers,
            double_tunnel_fee=self.double_tunnel_fee,
            num_favorite_drivers_online=len(self.favorite_drivers_online),
            operating_areas=[*self.operating_areas],
            applicable_discounts=self.applicable_discounts,
            driver_count_by_radius=self.driver_count_by_radius,
            boost_amount=self.boost_amount,
            merchant_campaign_id=self.merchant_campaign_id,
            fleet_vehicle=self.fleet_vehicle,
            fleet_partner_key=self.fleet_partner_key,
            client_type=self.client_type,
        )

    def to_hail_response(self, eta: float | None = None, heart_beat: HeartBeat | None = None) -> HailResponse:
        return HailResponse(
            id=self.id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            user_id=self.user_id,
            user_phone_number=self.user_phone_number,
            language=self.language,
            trip_estimation=self.trip_estimation,
            itinerary=self.itinerary,
            trip_id=self.trip_id,
            time=self.time,
            platform_type=self.platform_type,
            payment_details=self.payment_details,
            min_max_fare_calculations=self.min_max_fare_calculations,
            preference_matrix=self.vehicle_class_fleet_preferences.preference_matrix,
            filters=(
                ServiceFilters.model_validate_json(self.filter_matrix_str)
                if self.filter_matrix_str
                else None
            ),
            type=self.type,
            status=self.status,
            matched_driver=self.matched_driver,
            charges=self.charges,
            prioritize_favorite_drivers=self.prioritize_favorite_drivers,
            double_tunnel_fee=self.double_tunnel_fee,
            num_favorite_drivers_online=len(self.favorite_drivers_online),
            operating_areas=[*self.operating_areas],
            applicable_discounts=self.applicable_discounts,
            driver_count_by_radius=self.driver_count_by_radius,
            boost_amount=self.boost_amount,
            merchant_campaign_id=self.merchant_campaign_id,
            fleet_vehicle=self.fleet_vehicle,
            fleet_partner_key=self.fleet_partner_key,
            bonus_amount=self.bonus_amount,
            client_type=self.client_type,
            eta=eta,
            heart_beat=heart_beat,
        )

    def cancellation_fee(self, hail_config: HailConfiguration) -> float:
        if self.matched_driver is None:
            return 0.0
        elif self.matched_driver is not None and self.accepted_at is not None and DateTimeUtils.utc_now_with_tz() - self.accepted_at < timedelta(minutes=3):
            return 0.0
        elif self.is_fleet_hail and self.time - DateTimeUtils.utc_now_with_tz() < timedelta(minutes=60) and len(self.min_max_fare_calculations) > 0 and self.matched_driver is not None and self.accepted_at is not None:
            return float(min(self.min_max_fare_calculations[0].total, hail_config.fleet_cancellation_fee))
        elif self.matched_driver is not None and self.accepted_at is not None and DateTimeUtils.utc_now_with_tz() - self.accepted_at > timedelta(minutes=3):
            return 20.0
        else:
            return 0.0

    def to_matched_driver_hail_response(
        self,
    ) -> HailDriverResponse:
        if self.matched_driver is None:
            raise HailExceptionBuilder().no_matched_driver()

        return HailDriverResponse(
            id=self.id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            user_id=self.user_id,
            user_phone_number=self.user_phone_number,
            trip_estimation=self.trip_estimation,
            itinerary=self.itinerary,
            trip_id=self.trip_id,
            time=self.time,
            markup=self.matched_driver.fare_calculation.fleet_booking_fee
            + self.matched_driver.fare_calculation.additional_booking_fee
            + self.matched_driver.fare_calculation.boost_amount
            + self.bonus_amount,
            vehicle_class=self.matched_driver.vehicle_class,
            filters=(
                ServiceFilters.model_validate_json(self.filter_matrix_str)
                if self.filter_matrix_str
                else None
            ),
            type=self.type,
            status=self.status,
            tunnel_fees=self.tunnel_fees,
            matched_driver=self.matched_driver,
            is_favorite_driver=self.matched_driver.driver_id
            in self.favorite_drivers_online,
            double_tunnel_fee=self.double_tunnel_fee,
        )

    def to_hail_driver_response(
        self,
        driver: Driver,
        hail_config: HailConfiguration,
        fare_calculation_service: FareCalculationService,
    ) -> HailDriverResponse:
        if driver.operating_area is None or (
            self.base_fare_estimations_by_area is not None
            and driver.operating_area not in self.base_fare_estimations_by_area
        ):
            raise HailExceptionBuilder().no_base_fare_estimation_for_area(
                driver.operating_area
            )

        highest_vehicle_class_match, fare_calculation, _ = (
            driver.get_markup_for_vehicle_preferences(
                base_fare=(
                    self.base_fare_estimations_by_area[driver.operating_area]
                    if self.base_fare_estimations_by_area
                    else self.base_fare_estimation or Decimal(0)
                ),
                tunnel_fee=(
                    self.min_max_fare_calculations[0].estimated_tunnel_fee
                    if len(self.min_max_fare_calculations) > 0
                    else Decimal(0)
                ),
                datetime=self.time,
                origin=self.itinerary[0],
                destination=self.itinerary[-1],
                vehicle_preferences=self.vehicle_class_fleet_preferences.preference_matrix,
                hail_config=hail_config,
                fare_calculation_service=fare_calculation_service,
                discount_rules=(
                    self.applicable_discounts.to_discount_rules()
                    if self.applicable_discounts
                    else None
                ),
                boost_amount=self.boost_amount or 0.0,
            )
        )

        return HailDriverResponse(
            id=self.id,
            created_at=self.created_at,
            updated_at=self.updated_at,
            user_id=self.user_id,
            user_phone_number=self.user_phone_number,
            trip_estimation=self.trip_estimation,
            itinerary=self.itinerary,
            trip_id=self.trip_id,
            time=self.time,
            fixed_price=fare_calculation.total if self.is_fixed_price else None,
            markup=fare_calculation.fleet_booking_fee
            + fare_calculation.additional_booking_fee
            + fare_calculation.boost_amount
            + self.bonus_amount,
            vehicle_class=highest_vehicle_class_match,
            filters=(
                ServiceFilters.model_validate_json(self.filter_matrix_str)
                if self.filter_matrix_str
                else None
            ),
            type=self.type,
            status=self.status,
            tunnel_fees=self.tunnel_fees,
            matched_driver=self.matched_driver,
            is_favorite_driver=driver.id in self.favorite_drivers_online,
            double_tunnel_fee=self.double_tunnel_fee,
        )

    def to_hail_proto(self) -> hail_pb2.Hail:
        proto = hail_pb2.Hail(
            id=self.id,
            user_id=self.user_id,
            user_phone_number=self.user_phone_number,
            language=self.language.value,
            base_fare_estimations_by_area=(
                {
                    area.value: float(fare)
                    for area, fare in self.base_fare_estimations_by_area.items()
                }
                if self.base_fare_estimations_by_area
                else {}
            ),
            time_of_hail=self.time.isoformat(),
            type=self.type.value,
            status=self.status.value,
            operating_areas=[*self.operating_areas],
            trip_estimation__distance_meters=self.trip_estimation.distance_meters,
            trip_estimation__duration_seconds=self.trip_estimation.duration_seconds,
            itinerary=[itinerary.to_itinerary_proto()
                       for itinerary in self.itinerary],
            filter_matrix__is_assistant=self.filter_matrix.is_assistant,
            filter_matrix__is_pet_friendly=self.filter_matrix.is_pet_friendly,
            matched_driver=(
                self.matched_driver.to_matched_driver_proto()
                if self.matched_driver
                else None
            ),
            min_max_fare_calculations=[
                fare_calculation.to_fare_calculation_proto()
                for fare_calculation in self.min_max_fare_calculations
            ],
            charges__cancellation_fee=(
                self.charges.cancellation_fee if self.charges else None
            ),
            prioritize_favorite_drivers=self.prioritize_favorite_drivers,
            double_tunnel_fee=self.double_tunnel_fee,
            favorite_drivers_online=[*self.favorite_drivers_online],
            vehicle_class_fleet_preferences={
                k: ",".join(v)
                for k, v in self.vehicle_class_fleet_preferences.preference_matrix.items()
            },
        )

        # Only set boost_amount if the field exists in the protobuf message
        if hasattr(proto, 'boost_amount'):
            proto.boost_amount = self.boost_amount or 0.0

        if hasattr(proto, 'bonus_amount'):
            proto.bonus_amount = self.bonus_amount or 0.0

        return proto

    def to_id_location(self) -> IdLocation:
        return IdLocation(id=self.id, location=self.origin_pin())

    def is_past_live_hail_timeout(
        self, time_triggers: TimeTriggersConfiguration
    ) -> bool:
        updated_at_with_tz = (
            self.updated_at.replace(tzinfo=timezone.utc)
            if self.updated_at.tzinfo is None
            else self.updated_at
        )
        return (
            self.is_live
            and updated_at_with_tz
            <= DateTimeUtils.utc_now_with_tz()
            - timedelta(minutes=time_triggers.time_delta_for_live_hail_timeout_mins)
        )

    def is_past_timeout(self, time_triggers: TimeTriggersConfiguration) -> bool:
        print(time_triggers)
        return (
            self.is_past_live_hail_timeout(time_triggers)
            or self.is_past_scheduled_hail_timeout
        )

    def origin_pin(self) -> Pin:
        origin = self.itinerary[0]
        return Pin(
            lat=origin.lat,
            lng=origin.lng,
        )

    async def set_to_accepted(
        self,
        driver: Driver,
        hail_config: HailConfiguration,
        fare_calculation_service: FareCalculationService,
        pipeline: Pipeline | None = None,  # type: ignore
    ) -> None:
        self.matched_driver_str = self.__get_matched_driver_for_hail(
            driver=driver,
            hail_config=hail_config,
            fare_calculation_service=fare_calculation_service,
        ).model_dump_json()

        self.status = HailStatus.ACCEPTED
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.accepted_at = DateTimeUtils.utc_now_with_tz()
        await self.save(pipeline=pipeline)

        return

    async def set_to_approaching(self, fare_calculation_service: FareCalculationService, driver: Driver | None = None, hail_config: HailConfiguration | None = None, pipeline: Pipeline | None = None) -> None:  # type: ignore
        if driver and hail_config:
            self.matched_driver_str = self.__get_matched_driver_for_hail(
                driver=driver,
                hail_config=hail_config,
                fare_calculation_service=fare_calculation_service,
            ).model_dump_json()

        if self.matched_driver is None:
            raise HailExceptionBuilder().no_matched_driver()

        self.status = HailStatus.APPROACHING
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.approaching_at = DateTimeUtils.utc_now_with_tz()
        await self.save(pipeline=pipeline)

    async def set_to_on_going(self, trip_id: str) -> None:
        self.status = HailStatus.ON_GOING
        self.trip_id = trip_id
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.on_going_at = DateTimeUtils.utc_now_with_tz()
        await self.save()

    async def set_to_pending(self, pipeline: Pipeline | None = None) -> None:  # type: ignore
        self.status = HailStatus.PENDING
        self.matched_driver_str = ""
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        await self.save(pipeline=pipeline)

    async def set_to_cancelled(
        self, charges: HailCharges, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        self.status = HailStatus.CANCELLED
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.charges_str = charges.model_dump_json()
        await self.save(pipeline=pipeline)

    async def set_to_arrived(self) -> None:
        self.status = HailStatus.ARRIVED
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.arrived_at = DateTimeUtils.utc_now_with_tz()
        await self.save()

    async def set_to_timed_out(
        self, pipeline: Pipeline | None = None  # type: ignore
    ) -> None:
        self.status = HailStatus.TIMED_OUT
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.timed_out_at = DateTimeUtils.utc_now_with_tz()
        await self.save(pipeline=pipeline)

    async def set_to_completed(self) -> None:
        self.status = HailStatus.COMPLETED
        self.updated_at = DateTimeUtils.utc_now_with_tz()
        self.completed_at = DateTimeUtils.utc_now_with_tz()
        await self.save()

    async def update_charges(self, charges: HailCharges) -> None:
        """Update the charges for this hail"""
        self.charges_str = charges.model_dump_json()
        await self.save()

    async def set_favorite_drivers_online(self, driver_ids: set[str]) -> None:
        self.favorite_drivers_online_str = json.dumps(list(driver_ids))
        await self.save()

    async def set_driver_count_by_radius(
        self, driver_count_by_radius: dict[float, int]
    ) -> None:
        self.driver_count_by_radius_str = json.dumps(driver_count_by_radius)
        await self.save()

    async def add_sent_notification(
        self, notifications_sent: NotificationTriggerEventType
    ) -> None:
        self.notifications_sent_str = json.dumps(
            list(self.notifications_sent.union({notifications_sent}))
        )
        await self.save()

    def is_created_by_user(self, user_id: str) -> bool:
        return self.user_id == user_id

    def is_matched_with_driver(self, driver: Driver) -> bool:
        return (
            self.matched_driver is not None
            and self.matched_driver.driver_id == driver.id
        )

    def is_driver_viable_for_hail(self, driver: Driver) -> bool:
        """
        Determine if the driver is viable for a hail request
        """
        if driver.operating_area not in self.operating_areas:
            return False

        # Filter on filter key values
        if self.filter_matrix:
            for filter_key, filter_value in self.filter_matrix:
                if filter_value and not driver.service_filters[filter_key]:
                    return False

        # Filter on vehicle class and fleet ids
        is_viable = False
        for (
            vehicle_class,
            fleet_ids,
        ) in self.vehicle_class_fleet_preferences.preference_matrix.items():
            if VEHICLE_CLASS_MATCH_MATRIX[driver.vehicle_class][vehicle_class] and (
                len(fleet_ids) == 0 or driver.meter.fleet_id in fleet_ids
            ):
                is_viable = True
                break

        return is_viable

    def __get_matched_driver_for_hail(
        self,
        driver: Driver,
        hail_config: HailConfiguration,
        fare_calculation_service: FareCalculationService,
    ) -> MatchedDriver:
        """
        Calculates matched driver data for hail
        """
        base_fare = Decimal(0)
        if self.version == 3 and len(self.min_max_fare_calculations) > 0 or self.is_fleet_hail:
            base_fare = self.min_max_fare_calculations[1].estimated_fare_fee
        elif self.base_fare_estimations_by_area:
            if driver.operating_area is None or (
                self.base_fare_estimations_by_area is not None
                and driver.operating_area not in self.base_fare_estimations_by_area
            ):
                raise HailExceptionBuilder().no_base_fare_estimation_for_area(
                    driver.operating_area
                )
            base_fare = self.base_fare_estimations_by_area[driver.operating_area]

        highest_vehicle_class_match, fare_calculation, fleet_booking_fee_markup = (
            driver.get_markup_for_vehicle_preferences(
                base_fare=base_fare,
                tunnel_fee=(
                    self.min_max_fare_calculations[0].estimated_tunnel_fee
                    if len(self.min_max_fare_calculations) > 0
                    else Decimal(0)
                ),
                datetime=self.time,
                origin=self.itinerary[0],
                destination=self.itinerary[-1],
                vehicle_preferences=self.vehicle_class_fleet_preferences.preference_matrix,
                hail_config=hail_config,
                fare_calculation_service=fare_calculation_service,
                discount_rules=(
                    self.applicable_discounts.to_discount_rules()
                    if self.applicable_discounts
                    else None
                ),
                boost_amount=self.boost_amount or 0.0,
            )
        )

        is_dash_meter = driver.meter.is_dash_meter

        if self.is_fleet_hail and len(self.min_max_fare_calculations) > 0:
            fare_calculation = self.min_max_fare_calculations[0]
            is_dash_meter = False

        return MatchedDriver(
            driver_id=driver.id,
            name=driver.name,
            name_tc=driver.name_tc,
            phone_number=driver.phone_number,
            license_plate=driver.license_plate,
            vehicle_make=driver.vehicle_make,
            vehicle_model=driver.vehicle_model,
            vehicle_class=highest_vehicle_class_match,
            operating_area=driver.operating_area,
            fare_calculation=fare_calculation,
            is_dash_meter=is_dash_meter,
            fee_settings=FeeSettings(
                additional_booking_fee_rule=(
                    hail_config.lantau_additional_booking_fee
                    if driver.meter.operating_area == MeterOperatingArea.LANTAU
                    else hail_config.additional_booking_fee
                ),
                fleet_booking_fee_rule=fleet_booking_fee_markup,
            ),
            heart_beat_on_accepted=driver.heart_beat if driver.heart_beat else None,
            vehicle_icon_url=VehiclesClassService.get_vehicle_image_by_operating_areas_and_vehicle_class(
                operating_areas=[area for area in [driver.operating_area] if area is not None], vehicle_class=driver.vehicle_class
            ),
        )

    class Meta:
        model_key_prefix = "hail"
