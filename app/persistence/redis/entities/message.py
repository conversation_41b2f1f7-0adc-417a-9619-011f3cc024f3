from app.persistence.redis.entities.base import BaseRedisEntity
from app.sockets.messages.base_message import BaseMessage

SOCKET_ID_DELIMITER = ","


class Message(BaseRedisEntity):
    """Redis entity for distributing socket messages to all subscriber clients"""

    socket_ids_str: str
    """The stringified ids of the sockets to send the message to"""

    data: str
    """Stringified JSON data to be sent as the socket message"""

    @property
    def socket_ids(self) -> set[str]:
        """Returns the set of socket ids"""
        return set(self.socket_ids_str.split(SOCKET_ID_DELIMITER))

    @property
    def message_data(self) -> BaseMessage:
        """Returns the message as a BaseMessage instance"""
        return BaseMessage.model_validate_json(self.data)

    @staticmethod
    async def trigger(socket_ids: set[str], message_data: BaseMessage) -> "Message":
        """Triggers a new socket message to be sent to all clients subscribed to the socket_id"""
        message = Message(
            socket_ids_str=SOCKET_ID_DELIMITER.join(socket_ids),
            data=message_data.model_dump_json(by_alias=True),
        )
        await message.save()
        expiration_seconds = 60
        await message.expire(expiration_seconds)
        return message

    class Meta:
        model_key_prefix = "message"
