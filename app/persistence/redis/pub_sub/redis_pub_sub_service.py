import uuid
from app.persistence.redis.redis import redis
import asyncio
from app.persistence.redis.pub_sub.redis_pub_sub_subscriber import RedisPubSubSubscriber
from typing import List

from app.services.logger_service import LoggerService


class RedisPubSubService:
    """
    Service for handling Redis PubSub
    """

    logger_service: LoggerService
    instance_id: str

    def __init__(self, logger_service: LoggerService) -> None:
        self.__pub_sub = redis.pubsub()
        self.__subscribers: List[RedisPubSubSubscriber] = []
        self.logger_service = logger_service
        self.instance_id = str(uuid.uuid4())

    async def __pub_sub_reader(self) -> None:
        while True:
            if self.__pub_sub.subscribed:
                async for message in self.__pub_sub.listen():
                    if message["type"] == "psubscribe":
                        continue
                    for subscriber in self.__subscribers:
                        if message["pattern"] in subscriber.pub_sub_key_spaces():
                            await subscriber.handle_pub_sub_message(message)

    async def add_subscribers(self, subscribers: list[RedisPubSubSubscriber]) -> None:
        """
        Registers subscribing classes and listens for messages
        """
        for subscriber in subscribers:
            self.__subscribers.append(subscriber)
            for key_space in subscriber.pub_sub_key_spaces():
                await self.__pub_sub.psubscribe(key_space)

            self.logger_service.log(
                f"Instance {self.instance_id} successfully subscribed {subscriber.__class__.__name__}"
            )

    def start(self) -> None:
        asyncio.create_task(self.__pub_sub_reader())

    async def unsubscribe(self) -> None:
        for subscriber in self.__subscribers:
            for key_space in subscriber.pub_sub_key_spaces():
                await self.__pub_sub.punsubscribe(key_space)

        self.logger_service.log(
            f"Instance {self.instance_id} unsubscribed subscribers {", ".join([subscriber.__class__.__name__ for subscriber in self.__subscribers])}"
        )
