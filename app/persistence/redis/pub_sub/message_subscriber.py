import json
from typing import Any, List
from app.persistence.redis.pub_sub.pub_sub_message import RedisPubSubMessage
from app.persistence.redis.repositories.message_repository import MessageRepository
from app.services.logger_service import LoggerService
from app.persistence.redis.pub_sub.redis_pub_sub_subscriber import RedisPubSubSubscriber
from app.sockets.socket_service import SocketService


MESSAGE_KEY_SPACE = "__keyspace@0__:dash:message:*"


class MessageSubscriber(RedisPubSubSubscriber):
    logger_service: LoggerService
    socket_service: SocketService
    message_repository: MessageRepository

    def __init__(
        self,
        logger_service: LoggerService,
        socket_service: SocketService,
        message_repository: MessageRepository,
    ) -> None:
        self.logger_service = logger_service
        self.socket_service = socket_service
        self.message_repository = message_repository

    def pub_sub_key_spaces(self) -> List[str]:
        return [MESSAGE_KEY_SPACE]

    async def handle_pub_sub_message(self, raw_message: dict[str, Any]) -> None:
        if raw_message is None:
            return

        message = self.extract_message_data(raw_message)
        if message.pattern == MESSAGE_KEY_SPACE:
            await self.__handle_message_key_space(message)

    async def __handle_message_key_space(
        self, pubsub_message: RedisPubSubMessage
    ) -> None:
        """
        Listens for new messages and sends them to the appropriate socket
        """

        if pubsub_message.data == "hset":
            return

        message_key = pubsub_message.channel.split(":")[-1]
        message = await self.message_repository.get_message_by_id(message_key)
        if message is None:
            return
        if message.message_data.correlation_id is not None:
            self.logger_service.set_correlation_id(
                message.message_data.correlation_id)

        if len(message.socket_ids) == 1:
            await self.socket_service.send_personal_message(
                message.socket_ids.pop(),
                message.message_data,
            )
        elif len(message.socket_ids) > 1:
            await self.socket_service.broadcast_to_group(
                message.socket_ids,
                message.message_data,
            )
