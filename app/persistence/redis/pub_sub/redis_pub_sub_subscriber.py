from abc import ABC, abstractmethod
from typing import List

from app.persistence.redis.pub_sub.pub_sub_message import RedisPubSubMessage


class RedisPubSubSubscriber(ABC):
    @abstractmethod
    def pub_sub_key_spaces(self) -> List[str]:
        pass

    @abstractmethod
    async def handle_pub_sub_message(self, message: dict[str, str]) -> None:
        pass

    def extract_message_data(self, message: dict[str, str]) -> RedisPubSubMessage:
        return RedisPubSubMessage.model_validate(message)
