from decimal import Decimal
from pydantic import BaseModel, Field

from app.contracts.common.dash_transaction_fees import DashTransactionFees
from app.models.meter_operating_area import MeterOperatingArea


METER_COLLECTION_NAME = "meters"


class MeterSettings(BaseModel):
    fleet_id: str | None = Field(default=None)
    vehicle_id: str
    operating_area: MeterOperatingArea
    is_dash_meter: bool = Field(default=False)
    dash_fee_constant: Decimal = Field(default=Decimal(0))
    dash_fee_rate: Decimal = Field(default=Decimal(0))

    @property
    def dash_transaction_fees(self) -> DashTransactionFees | None:
        return (
            DashTransactionFees(
                dash_fee_constant=self.dash_fee_constant,
                dash_fee_rate=self.dash_fee_rate,
            )
            if (self.dash_fee_constant and self.dash_fee_rate)
            else None
        )


class Meter(BaseModel):
    settings: MeterSettings
