from datetime import datetime
from typing import Any
from uuid import uuid4
from pydantic import BaseModel, Field

from app.contracts.common.fleet_vehicle import FleetVehicleType
from app.contracts.hails.fee_settings import FeeSettings
from app.models.client_type import ClientType
from app.models.hail_platform_type import HailPlatformType
from app.models.hail_type import HailType
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.trip_payment_type import TripPaymentType
from app.models.trip_type import TripType
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.dto.update_trip_dto import UpdateTripDto
from app.persistence.redis.entities.hail import Hail
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder


class TripDriver(BaseModel):
    id: str
    name: str
    name_ch: str | None = None


class TripUser(BaseModel):
    id: str
    phone: str


class TripVehicle(BaseModel):
    make: str
    model: str
    vehicle_class: VehicleClass | None = Field(None, alias="class")
    operating_area: MeterOperatingArea | None = None


class TripBillingDiscountSettings(BaseModel):
    discount_id_dash: str | None = None
    discount_id_third_party: str | None = None
    discount_rules_third_party: str | None = None
    discount_rules_dash: str | None = None


class TripBillingTransactionFees(BaseModel):
    dash_fee_constant: float
    dash_fee_rate: float


class TripBilling(BaseModel):
    estimated_fare: float
    fare: float
    extra: float
    fleet_booking_fee: float
    dash_booking_fee: float
    additional_booking_fee: float
    dash_fee_total: float
    dash_transaction_fee: float
    dash_tips: float
    discount: float
    trip_total: float
    total: float
    boost_amount: float
    bonus_amount: float
    dash_fee_settings: TripBillingTransactionFees
    discount_settings: TripBillingDiscountSettings
    hailing_fee_settings: FeeSettings | None


class TripPaymentInstrument(BaseModel):
    card_suffix: str = Field(examples=["5678"])
    card_type: str = Field(examples=["VISA"])


# class TripLocation(BaseModel):
#     start: GeoPoint
#     start_address: str
#     end: GeoPoint
#     end_address: str

#     model_config = ConfigDict(arbitrary_types_allowed=True)


class Trip(BaseModel):
    id: str
    platform_type: HailPlatformType = HailPlatformType.DASH
    hail_order_type: HailType | None = None
    hail_id: str | None = None
    type: TripType | None = None
    language: LocalizationLanguage | None = None
    license_plate: str | None = None
    payment_type: str | None = TripPaymentType.DASH
    trip_start: datetime
    trip_end: datetime | None = None
    driver: dict[str, Any] | None = None
    user: dict[str, Any] | None = None
    is_dash_meter: bool | None = None
    vehicle: dict[str, Any] | None = None
    trip_itinerary: list[dict[str, Any]] | None = None
    billing: dict[str, Any] | None = None
    payment_instrument: dict[str, Any] | None = None
    # location: dict[str, Any] | None = None # This is not currently needed as the meter will be updated later to to write the location as specified
    creation_time: datetime | None = None
    last_update_time: datetime | None = None
    fleet_vehicle: FleetVehicleType | None = None
    fleet_partner_key: str | None = None
    merchant_campaign_id: str | None = None
    client_type: ClientType = ClientType.DASH

    @staticmethod
    def create_new(hail: Hail) -> "Trip":
        if hail.matched_driver is None:
            raise MeterExceptionBuilder().insufficient_hail_data_for_trip(hail.id)

        now = DateTimeUtils.utc_now_with_tz()
        return Trip(
            id=str(uuid4()),
            platform_type=hail.platform_type,
            hail_order_type=hail.type,
            hail_id=hail.id,
            type=TripType.HAIL,
            language=hail.language,
            license_plate=hail.matched_driver.license_plate,
            trip_start=now,
            driver=TripDriver(
                id=hail.matched_driver.driver_id,
                name=hail.matched_driver.name,
                name_ch=hail.matched_driver.name_tc,
            ).model_dump(),
            user=TripUser(
                id=hail.user_id,
                phone=hail.user_phone_number,
            ).model_dump(),
            is_dash_meter=hail.matched_driver.is_dash_meter,
            vehicle=TripVehicle(
                **{
                    "make": hail.matched_driver.vehicle_make,
                    "model": hail.matched_driver.vehicle_model,
                    "class": hail.matched_driver.vehicle_class,
                    "operating_area": hail.matched_driver.operating_area,
                }
            ).model_dump(by_alias=True),
            fleet_vehicle=hail.fleet_vehicle,
            fleet_partner_key=hail.fleet_partner_key,
            trip_itinerary=[
                itinerary.convert_for_firestore() for itinerary in hail.itinerary
            ],
            billing=TripBilling(
                estimated_fare=float(
                    hail.matched_driver.fare_calculation.estimated_fare_fee
                ),
                fare=float(
                    hail.matched_driver.fare_calculation.estimated_fare_fee),
                extra=float(
                    hail.matched_driver.fare_calculation.estimated_tunnel_fee),
                fleet_booking_fee=float(
                    hail.matched_driver.fare_calculation.fleet_booking_fee
                ),
                fleet_booking_display_fee=float(
                    hail.matched_driver.fare_calculation.estimated_fare_fee
                ),
                dash_booking_fee=float(
                    hail.matched_driver.fare_calculation.dash_booking_fee
                ),
                additional_booking_fee=float(
                    hail.matched_driver.fare_calculation.additional_booking_fee
                ),
                dash_fee_total=float(
                    hail.matched_driver.fare_calculation.dash_booking_fee
                    + hail.matched_driver.fare_calculation.dash_transaction_fee
                ),
                dash_transaction_fee=float(
                    hail.matched_driver.fare_calculation.dash_transaction_fee
                ),
                dash_tips=0,
                discount=-float(hail.matched_driver.fare_calculation.discount),
                trip_total=float(
                    hail.matched_driver.fare_calculation.estimated_fare_fee
                ),
                total=float(hail.matched_driver.fare_calculation.total),
                boost_amount=float(
                    hail.matched_driver.fare_calculation.boost_amount or 0.0),
                bonus_amount=float(hail.bonus_amount or 0.0),
                dash_fee_settings=TripBillingTransactionFees(
                    dash_fee_constant=float(
                        hail.matched_driver.fare_calculation.transaction_fees.dash_fee_constant
                    ),
                    dash_fee_rate=float(
                        hail.matched_driver.fare_calculation.transaction_fees.dash_fee_rate
                    ),
                ),
                discount_settings=TripBillingDiscountSettings(
                    discount_id_dash=(
                        hail.applicable_discounts.discount_id_dash
                        if hail.applicable_discounts
                        else None
                    ),
                    discount_id_third_party=(
                        hail.applicable_discounts.discount_id_third_party
                        if hail.applicable_discounts
                        else None
                    ),
                    discount_rules_third_party=(
                        hail.applicable_discounts.discount_rules_third_party
                        if hail.applicable_discounts
                        else None
                    ),
                    discount_rules_dash=(
                        hail.applicable_discounts.discount_rules_dash
                        if hail.applicable_discounts
                        else None
                    ),
                ),
                hailing_fee_settings=hail.matched_driver.fee_settings,
            ).model_dump(),
            payment_instrument=(
                TripPaymentInstrument(
                    card_suffix=hail.payment_details.card_suffix,
                    card_type=hail.payment_details.card_type,
                ).model_dump() 
                if hail.payment_details.card_type else None
            ),
            merchant_campaign_id=hail.merchant_campaign_id,
            # location=TripLocation(
            #     start=GeoPoint(
            #         latitude=hail.itinerary[0].lat, longitude=hail.itinerary[0].lng
            #     ),
            #     start_address=hail.itinerary[0].i18n[hail.language].formatted_address,
            #     end=GeoPoint(
            #         latitude=hail.itinerary[-1].lat, longitude=hail.itinerary[-1].lng
            #     ),
            #     end_address=hail.itinerary[-1].i18n[hail.language].formatted_address,
            # ).model_dump(),
            creation_time=now,
            last_update_time=now,
            client_type=hail.client_type,
        )

    def to_update_trip_dto(self) -> UpdateTripDto:
        return UpdateTripDto.model_validate(self.model_dump())
