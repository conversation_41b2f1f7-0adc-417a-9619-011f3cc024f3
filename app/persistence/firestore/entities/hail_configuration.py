from decimal import Decimal
from pydantic import BaseModel, Field

from app.contracts.common.dash_transaction_fees import DashTransactionFees
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.tunnel_name import TunnelName
from app.models.vehicle_class import VehicleClass

from app.models.vehicle_image import VehicleImage

CONFIGURATION_COLLECTION_NAME = "configurations"
DATE_STRING_FORMAT = "%Y-%m-%d"
"""Date string formatted as 'YYYY-MM-DD'"""


class DashFeeConfiguration(BaseModel):
    common: DashTransactionFees
    dash_fees: dict[MeterOperatingArea, DashTransactionFees]


class VehicleClassConfiguration(BaseModel):
    image_base: str
    vehicle_class_name: str
    vehicle_class_name_tc: str
    vehicle_class_description: str
    vehicle_class_description_tc: str
    seats_count: int | None = None
    luggage_count: int | None = None

    @staticmethod
    def get_vehicle_image(
        image_base: str, operating_area: MeterOperatingArea
    ) -> VehicleImage | None:
        match image_base:
            case VehicleClass.FOUR_SEATER | VehicleClass.FIVE_SEATER:
                match operating_area:
                    case MeterOperatingArea.URBAN:
                        return VehicleImage.STANDARD_URBAN
                    case MeterOperatingArea.NT:
                        return VehicleImage.STANDARD_NT
                    case MeterOperatingArea.LANTAU:
                        return VehicleImage.STANDARD_LANTAU
            case VehicleClass.COMFORT:
                match operating_area:
                    case MeterOperatingArea.URBAN:
                        return VehicleImage.COMFORT_URBAN
                    case MeterOperatingArea.NT:
                        return VehicleImage.COMFORT_NT
                    case MeterOperatingArea.LANTAU:
                        return VehicleImage.COMFORT_LANTAU
            case VehicleClass.LUXURY:
                match operating_area:
                    case MeterOperatingArea.URBAN:
                        return VehicleImage.LUXURY_URBAN
                    case MeterOperatingArea.NT:
                        return VehicleImage.LUXURY_NT
                    case MeterOperatingArea.LANTAU:
                        return VehicleImage.LUXURY_LANTAU
            case _:
                return None

    def vehicle_image(self, operating_area: MeterOperatingArea) -> VehicleImage | None:
        return VehicleClassConfiguration.get_vehicle_image(
            self.image_base, operating_area
        )

    def name_for_language(self, language: LocalizationLanguage) -> str:
        match language:
            case LocalizationLanguage.ZH_HK:
                return self.vehicle_class_name_tc
            case _:
                return self.vehicle_class_name

    def description_for_language(self, language: LocalizationLanguage) -> str:
        match language:
            case LocalizationLanguage.ZH_HK:
                return self.vehicle_class_description_tc
            case _:
                return self.vehicle_class_description


class DriverSearch(BaseModel):
    km_radii_for_online_drivers_search: list[float] | tuple[float, float, float] = (
        1,
        3,
        5,
    )
    """List of search radii in kilometers for searching online drivers"""

    default_search_radius_km: int
    """Default search radius in kilometers"""

    max_search_radius_km: int
    """Maximum search radius in kilometers"""

    seconds_pause_between_expansions: int
    """Seconds to wait before expanding search radius"""

    min_drivers_for_search_radius: int
    """Minimum number of drivers in results required to not instantly expand search radius"""


class FavoriteDriverConfiguration(BaseModel):
    live_hail_timeout_seconds: int
    scheduled_hail_timeout_seconds: int


class TimeTriggersConfiguration(BaseModel):
    time_delta_for_live_hail_timeout_mins: int


class HailConfiguration(BaseModel):
    dash_booking_fee: Decimal = Field(default=Decimal(0))
    enabled_operating_areas: list[MeterOperatingArea]
    additional_booking_fee: str | float
    lantau_additional_booking_fee: str
    vehicle_classes: dict[VehicleClass | str, VehicleClassConfiguration]
    public_holidays: list[str]
    tunnel_fare: dict[TunnelName | str, Decimal]
    dash_fee_configuration: DashFeeConfiguration
    driver_search: DriverSearch
    favorite_drivers: FavoriteDriverConfiguration | None = None
    time_triggers: TimeTriggersConfiguration
    fleet_cancellation_fee: Decimal = Field(default=Decimal(115))
    dash_cancellation_fee: Decimal = Field(default=Decimal(15))
    max_boost_limit: Decimal = Field(default=Decimal(500)) 
