from datetime import datetime
from typing import Any
from pydantic import BaseModel

from app.models.localization_language import LocalizationLanguage
from app.models.trip_payment_type import TripPaymentType
from app.models.trip_type import TripType


class UpdateTripDto(BaseModel):
    hail_id: str
    type: TripType
    language: LocalizationLanguage | None = None
    payment_type: str | None = TripPaymentType.DASH
    user: dict[str, str]
    is_dash_meter: bool
    vehicle: dict[str, Any]
    trip_itinerary: list[dict[str, Any]]
    billing: dict[str, Any]
    payment_instrument: dict[str, Any]
    # location: dict[str, Any] # not currently needed. See Trip document
    last_update_time: datetime
