from app.persistence.firestore.entities.user import USER_COLLECTION_NAME, User
from app.persistence.firestore.repositories.base_repository import BaseRepository
from app.services.firebase_service import FirebaseService

from app.services.logger_service import LoggerService
from app.utils.exceptions.user_exception_builder import UserExceptionBuilder


class UserRepository(BaseRepository):
    def __init__(
        self, logger_service: LoggerService, firebase_service: FirebaseService
    ):
        super().__init__(logger_service, firebase_service, USER_COLLECTION_NAME)

    async def get_user_by_id(self, user_id: str) -> User:
        user_dict = await self._get_by_id(user_id)

        if user_dict is None:
            self._logger_service.error(f"User {user_id} not found")
            raise UserExceptionBuilder().user_not_found(user_id)
        try:
            user = User.model_validate(user_dict)
        except Exception as e:
            print(e)
        return user
