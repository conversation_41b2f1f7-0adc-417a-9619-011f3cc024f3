from typing import Any

from app.services.firebase_service import FirebaseService
from google.cloud.firestore_v1.collection import CollectionReference
from app.services.logger_service import LoggerService


class BaseRepository:
    _logger_service: LoggerService
    _collection_ref: CollectionReference

    def __init__(
        self,
        logger_service: LoggerService,
        firebase_service: FirebaseService,
        collection: str,
    ):
        self._logger_service = logger_service
        self._collection_ref = firebase_service.db.collection(collection)

    async def _get_by_id(self, id: str) -> dict[str, Any] | None:
        data = self._collection_ref.document(id).get()
        dict_data = data.to_dict()
        return dict_data or None

    async def _get_all(self) -> list[dict[str, Any] | None]:
        data = self._collection_ref.get()
        return [doc.to_dict() for doc in data]
