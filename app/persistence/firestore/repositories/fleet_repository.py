from typing import Any
from app.persistence.firestore.entities.fleet import FLEET_COLLECTION_NAME, Fleet
from app.persistence.firestore.repositories.base_repository import BaseRepository
from app.services.firebase_service import FirebaseService

from app.services.logger_service import LoggerService


class FleetRepository(BaseRepository):
    def __init__(
        self, logger_service: LoggerService, firebase_service: FirebaseService
    ):
        super().__init__(logger_service, firebase_service, FLEET_COLLECTION_NAME)

    async def get_fleets(self) -> list[Fleet]:
        fleets_data: Any = self._collection_ref.stream()
        fleets = []
        for fleet_data in fleets_data:
            fleet_dict = fleet_data.to_dict()
            if fleet_dict is not None:
                fleets.append(Fleet(**fleet_dict, id=fleet_data.id))
        return fleets

    async def get_fleet_by_id(self, fleet_id: str) -> Fleet | None:
        fleet_dict = await self._get_by_id(fleet_id)
        if fleet_dict is None:
            self._logger_service.warning(f"Fleet {fleet_id} not found")
            return None

        return Fleet(**fleet_dict, id=fleet_id)
