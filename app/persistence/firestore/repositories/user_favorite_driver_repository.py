from app.persistence.firestore.entities.user import (
    FAVORITE_DRIVER_COLLECTION_NAME,
    USER_COLLECTION_NAME,
)
from app.persistence.firestore.repositories.base_repository import BaseRepository
from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService


class UserFavoriteDriverRepository(BaseRepository):

    def __init__(
        self,
        user_id: str,
        logger_service: LoggerService,
        firebase_service: FirebaseService,
    ):
        super().__init__(
            logger_service,
            firebase_service,
            f"{USER_COLLECTION_NAME}/{user_id}/{FAVORITE_DRIVER_COLLECTION_NAME}",
        )

    async def get_user_favorite_drivers(self) -> set[str]:
        favorite_driver_dicts = await self._get_all()
        return {
            favorite["driver_id"]
            for favorite in favorite_driver_dicts
            if favorite is not None and "driver_id" in favorite
        }
