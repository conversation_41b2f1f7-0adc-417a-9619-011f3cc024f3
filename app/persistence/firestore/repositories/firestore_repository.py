from app.persistence.firestore.repositories.fleet_repository import FleetRepository
from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService
from app.persistence.firestore.repositories.user_repository import (
    UserRepository,
)
from app.persistence.firestore.repositories.user_favorite_driver_repository import (
    UserFavoriteDriverRepository,
)


class FirestoreRepository:
    _logger_service: LoggerService
    _firebase_service: FirebaseService

    def __init__(
        self, logger_service: LoggerService, firebase_service: FirebaseService
    ):
        self._logger_service = logger_service
        self._firebase_service = firebase_service

    def fleet_repository(self) -> FleetRepository:
        return FleetRepository(self._logger_service, self._firebase_service)

    def user_repository(self) -> UserRepository:
        return UserRepository(self._logger_service, self._firebase_service)

    def user_favorite_driver_repository(
        self, user_id: str
    ) -> UserFavoriteDriverRepository:
        return UserFavoriteDriverRepository(
            user_id, self._logger_service, self._firebase_service
        )
