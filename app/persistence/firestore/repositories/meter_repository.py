from app.persistence.firestore.entities.meter import (
    METER_COLLECTION_NAME,
    Meter,
    MeterSettings,
)
from app.persistence.firestore.entities.trip import Trip
from app.persistence.redis.entities.driver import Driver
from app.persistence.redis.entities.hail import Hail
from google.cloud.firestore_v1.async_collection import AsyncCollectionReference

from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder

METER_TRIPS_COLLECTION_NAME = "trips"


class MeterRepository:
    meter_collection_ref: AsyncCollectionReference
    logger_service: LoggerService

    def __init__(
        self, firebase_service: FirebaseService, logger_service: LoggerService
    ):
        self.meter_collection_ref = firebase_service.asyncDb.collection(
            METER_COLLECTION_NAME
        )
        self.logger_service = logger_service

    async def get_meter_by_id(self, meter_id: str) -> Meter | None:
        meter_data = await self.meter_collection_ref.document(meter_id).get(
            field_paths=["settings"]
        )
        meter_dict = meter_data.to_dict()
        if meter_dict is None:
            return None

        try:
            meter_settings = MeterSettings.model_validate(
                meter_dict.get("settings"))
            return Meter(settings=meter_settings)
        except Exception:
            raise MeterExceptionBuilder().invalid_meter_data(
                meter_id, meter_data=meter_dict
            )

    async def get_active_meter_trip(self, driver: Driver) -> Trip | None:
        latest_trips = await (
            self.meter_collection_ref.document(driver.license_plate)
            .collection(METER_TRIPS_COLLECTION_NAME)
            .order_by("trip_start", direction="DESCENDING")
            .limit(1)
            .get()
        )
        if len(latest_trips) < 1:
            return None

        self.logger_service.log(
            f"Active meter trip found: {latest_trips[0].id}")

        latest_trip = Trip.model_validate(
            {**latest_trips[0].to_dict(), "id": latest_trips[0].id}
        )
        return latest_trip if latest_trip.trip_end is None else None

    async def pair_hail_with_existing_meter_trip(self, hail: Hail, trip_id: str) -> str:
        if hail.matched_driver is None:
            raise HailExceptionBuilder().invalid_meter_pairing_state()

        trip_ref = (
            self.meter_collection_ref.document(
                hail.matched_driver.license_plate)
            .collection(METER_TRIPS_COLLECTION_NAME)
            .document(trip_id)
        )
        self.logger_service.log(
            f"Pairing with existing meter trip {trip_id}",
        )

        try:
            trip = Trip.create_new(hail)
            await trip_ref.update(trip.to_update_trip_dto().model_dump())
            return trip_id
        except Exception:
            raise MeterExceptionBuilder().active_meter_trip_not_found(
                hail.matched_driver.license_plate
            )

    async def pair_hail_with_new_meter_trip(self, hail: Hail) -> str:
        if hail.matched_driver is None:
            raise MeterExceptionBuilder().insufficient_hail_data_for_trip(hail.id)

        new_trip = Trip.create_new(hail)
        self.logger_service.log(
            f"Creating new meter trip {new_trip.id}",
        )

        meter_trips_collections_ref = self.meter_collection_ref.document(
            hail.matched_driver.license_plate
        ).collection(METER_TRIPS_COLLECTION_NAME)

        await meter_trips_collections_ref.document(new_trip.id).set(
            new_trip.model_dump()
        )

        self.logger_service.log(
            f"New meter trip created: {new_trip.id}",
            {"trip": new_trip.model_dump_json()},
        )

        return new_trip.id

    async def pair_hail_with_fleet_meter_trip(self, hail: Hail, meter_id: str) -> str:
        if hail.matched_driver is None:
            raise MeterExceptionBuilder().insufficient_hail_data_for_trip(hail.id)

        new_trip = Trip.create_new(hail)
        self.logger_service.log(
            f"Creating new meter trip {new_trip.id}",
        )

        meter_trips_collections_ref = self.meter_collection_ref.document(
            meter_id
        ).collection(METER_TRIPS_COLLECTION_NAME)

        await meter_trips_collections_ref.document(new_trip.id).set(
            new_trip.model_dump()
        )

        self.logger_service.log(
            f"New meter trip created: {new_trip.id}",
            {"trip": new_trip.model_dump_json()},
        )

        return new_trip.id
