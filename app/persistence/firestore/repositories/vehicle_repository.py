from app.persistence.firestore.entities.vehicle import Vehicle
from google.cloud.firestore_v1.async_collection import AsyncCollectionReference

from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService

VEHICLE_TYPES_COLLECTION_NAME = "vehicle_types"


class VehicleRepository:
    vehicle_types_collection_ref: AsyncCollectionReference
    logger_service: LoggerService

    def __init__(
        self, firebase_service: FirebaseService, logger_service: LoggerService
    ):
        self.vehicle_types_collection_ref = firebase_service.asyncDb.collection(
            VEHICLE_TYPES_COLLECTION_NAME
        )
        self.logger_service = logger_service

    async def get_vehicle_by_id(self, vehicle_id: str) -> Vehicle | None:
        vehicle_data = await self.vehicle_types_collection_ref.document(
            vehicle_id
        ).get()
        vehicle_dict = vehicle_data.to_dict()
        if vehicle_dict is not None:
            try:
                return Vehicle.model_validate(vehicle_dict)
            except Exception as e:
                raise e

        return None
