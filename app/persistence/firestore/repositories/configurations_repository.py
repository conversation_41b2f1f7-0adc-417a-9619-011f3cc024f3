import asyncio
from google.cloud.firestore_v1.async_collection import AsyncCollectionReference

from app.persistence.firestore.entities.hail_configuration import (
    CONFIGURATION_COLLECTION_NAME,
    HailConfiguration,
)
from app.persistence.redis.redis import RedisService
from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder


class ConfigurationsRepository:
    configuration_collection_ref: AsyncCollectionReference
    logger_service: LoggerService
    redis_service: RedisService

    def __init__(
        self,
        firebase_service: FirebaseService,
        logger_service: LoggerService,
        redis_service: RedisService,
    ):
        self.configuration_collection_ref = firebase_service.asyncDb.collection(
            CONFIGURATION_COLLECTION_NAME
        )
        self.logger_service = logger_service
        self.redis_service = redis_service

    async def get_hail_configuration(self) -> HailConfiguration:
        async def fetch_hail_configuration() -> HailConfiguration:
            hail_config_doc_id = "hailing"
            second_row_config_doc_id = "2nd_row"
            common_config_doc_id = "common"
            meter_sdk_config_doc_id = "meter-sdk"

            hailing_config, second_row_config, common_config, meter_sdk_config = (
                await asyncio.gather(
                    self.configuration_collection_ref.document(
                        hail_config_doc_id
                    ).get(),
                    self.configuration_collection_ref.document(
                        second_row_config_doc_id
                    ).get(),
                    self.configuration_collection_ref.document(
                        common_config_doc_id
                    ).get(),
                    self.configuration_collection_ref.document(
                        meter_sdk_config_doc_id
                    ).get(),
                )
            )

            hailing_config_dict = hailing_config.to_dict()

            second_row_config_dict = second_row_config.to_dict()
            second_row_common_config_dict = (
                second_row_config_dict.get("common")
                if second_row_config_dict is not None
                else None
            )
            common_config_dict = common_config.to_dict()
            meter_sdk_config_dict = meter_sdk_config.to_dict()

            if (
                hailing_config_dict is None
                or second_row_common_config_dict is None
                or common_config_dict is None
            ):
                self.logger_service.error("Hail configuration not found")
                raise HailExceptionBuilder().hail_config_not_found()

            return HailConfiguration.model_validate(
                {
                    **hailing_config_dict,
                    **second_row_common_config_dict,
                    **common_config_dict,
                    "dash_fee_configuration": meter_sdk_config_dict,
                },
                strict=False,
            )

        return await self.redis_service.getCachedOrFetch(
            "dash:configuration",
            fetch_hail_configuration,
            10,
            HailConfiguration,
        )
