from typing import Annotated
from uuid import uuid4
from fastapi_controllers import Controller, get, put
from fastapi import BackgroundTasks, Depends, Query, status
from dependency_injector.wiring import inject, Provide

from app.contracts.drivers.driver_session_heartbeat_response import (
    DriverSessionHeartbeatResponse,
)
from app.contracts.hails.admin_hail_cancallation_request import AdminHailCancellationFeeRequest
from app.contracts.hails.hail_cancallation_fee_response import HailCancellationFeeResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import HttpUserAuth
from app.infrastructure.dependencies.http_x_api_key_auth import HttpXApi<PERSON>eyAuth
from app.models.auth import Auth
from app.models.hail_status import HailStatus
from app.models.role import Role
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.services.hails_service import HailsService
from app.services.logger_service import LoggerService
from app.utils.exception_doc_utils import ExceptionDocUtils
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder


class AdminController(Controller):
    prefix = "/admin"
    tags = ["Admin"]

    logger_service: LoggerService
    driver_repository: DriverRepository
    hail_repository: HailRepository
    hails_service: HailsService

    @inject
    def __init__(
        self,
        logger_service: LoggerService = Depends(
            Provide[Container.logger_service]),
        driver_repository: DriverRepository = Depends(
            Provide[Container.driver_repository]
        ),
        hail_repository: HailRepository = Depends(
            Provide[Container.hail_repository]),
        hails_service: HailsService = Depends(
            Provide[Container.hails_service]),
    ) -> None:
        self.logger_service = logger_service
        self.driver_repository = driver_repository
        self.hail_repository = hail_repository
        self.hails_service = hails_service

    @get("/drivers", status_code=status.HTTP_200_OK)
    async def get_active_drivers(
        self,
        _: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.ADMIN))],
    ) -> list[DriverSessionHeartbeatResponse]:
        active_drivers = await self.driver_repository.get_active_drivers()
        return [
            driver.to_driver_session_heartbeat_response() for driver in active_drivers
        ]

    @get("/hails", status_code=status.HTTP_200_OK)
    async def get_hails(
        self,
        _: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.ADMIN))],
        statuses: list[HailStatus] = Query([]),
    ) -> list[HailRiderResponse]:
        hails = await self.hail_repository.get_hails_by_status(*statuses)
        return [hail.to_hail_rider_response() for hail in hails]

    @get("/hails/{hail_id}/cancellation_fee", status_code=status.HTTP_200_OK)
    async def get_hail_cancellation_fee(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.ADMIN))],
        hail_id: str,
    ) -> HailCancellationFeeResponse:
        self.logger_service.log(
            f"Rider {auth.user_id} get hail {hail_id}",
        )
        return await self.hails_service.get_hail_cancellation_fee(hail_id)

    @put(
        "/hails/{hail_id}",
        response_model=HailRiderResponse,
        status_code=status.HTTP_200_OK,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
            ]
        ),
    )
    async def cancel_hail_by_admin(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.ADMIN))],
        hail_id: str,
        request: AdminHailCancellationFeeRequest,
    ) -> HailRiderResponse:
        return await self.hails_service.cancel_hail_by_admin(
            hail_id, auth, request.cancellation_fee, request.cancellation_fee_breakdown
        )