import csv
from datetime import datetime, timed<PERSON>ta
from typing import Annotated
from fastapi.responses import FileResponse
from fastapi_controllers import Controller, get
from fastapi import Depends, Query, status
from dependency_injector.wiring import inject, Provide

from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import HttpUserAuth
from app.models.auth import Auth
from app.models.role import Role
from app.persistence.redis.repositories.tracking_repository import TrackingRepository
from app.services.logger_service import LoggerService
from app.utils.exceptions.report_exception_builder import ReportExceptionBuilder


class ReportsController(Controller):
    prefix = "/reports"
    tags = ["Reports"]

    logger_service: LoggerService
    tracking_repository: TrackingRepository

    @inject
    def __init__(
        self,
        logger_service: LoggerService = Depends(Provide[Container.logger_service]),
        tracking_repository: TrackingRepository = Depends(
            Provide[Container.tracking_repository]
        ),
    ) -> None:
        self.logger_service = logger_service
        self.tracking_repository = tracking_repository

    @get("/driver-sessions", status_code=status.HTTP_200_OK)
    async def get_driver_session_report(
        self,
        _: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.ADMIN))],
        start_date: datetime = Query(..., example=datetime.now().replace(day=1)),
        end_date: datetime = Query(..., example=datetime.now()),
    ) -> FileResponse:
        if start_date > end_date:
            ReportExceptionBuilder().invalid_date_range()

        driver_sessions = (
            await self.tracking_repository.get_driver_sessions_for_date_range(
                start_date=start_date.replace(tzinfo=None).replace(
                    hour=0, minute=0, second=0, microsecond=0
                ),
                end_date=end_date.replace(tzinfo=None).replace(
                    hour=23, minute=59, second=59, microsecond=999999
                ),
            )
        )

        date_format = "%Y-%m-%d"

        date_range = []
        current_date = start_date
        while current_date <= end_date:
            date_range.append(current_date.strftime(date_format))
            current_date += timedelta(days=1)

        csv_data = [["Driver"] + date_range]
        for driver_id in driver_sessions:
            row = [f"{driver_id}"]
            for date in date_range:
                row.append(driver_sessions[driver_id].get(date, "0"))
            csv_data.append(row)

        file_path = f"Driver sessions report {start_date.strftime(date_format)} {end_date.strftime(date_format)}.csv"
        with open(file_path, "w", newline="") as csv_file:
            writer = csv.writer(csv_file)
            writer.writerows(csv_data)

        return FileResponse(file_path, media_type="text/csv", filename=file_path)
