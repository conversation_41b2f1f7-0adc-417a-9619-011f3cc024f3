from typing import Annotated, Union
from uuid import uuid4
from app.contracts.hails.hail_cancallation_fee_response import (
    HailCancellationFeeResponse,
)
from app.contracts.hails.hail_response import HailResponse
from app.infrastructure.dependencies.multi_auth import multi_auth_dependency
from app.models import auth
from app.utils.datetime_utils import DateTimeUtils
from fastapi import (
    BackgroundTasks,
    Body,
    Query,
    WebSocket,
    status,
    Depends,
)
from app.contracts.hails.driver_hail_accepted_request import DriverHailAcceptedRequest
from app.contracts.hails.driver_hail_cancelled_request import DriverHailCancelledRequest
from app.contracts.hails.driver_hail_completed_request import DriverHailCompletedRequest
from app.contracts.hails.driver_hail_on_going_request import DriverHailOnGoingRequest
from app.contracts.hails.fleet_hail_update_request import FleetHailUpdateRequest
from app.contracts.hails.hail_boost_update_response import BoostUpdateResponse
from app.contracts.hails.hail_driver_response import HailDriverResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.contracts.hails.rider_hail_boost_request import RiderHailBoostRequest
from app.contracts.hails.rider_hail_cancelled_request import RiderHailCancelledRequest
from app.contracts.hails.service_hail_completed_request import (
    ServiceHailCompletedRequest,
)
from app.infrastructure.dependencies.http_service_auth import HttpServiceAuth
from app.infrastructure.settings import settings
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import HttpUserAuth
from app.infrastructure.dependencies.socket_auth import SocketAuth
from app.models.auth import Auth
from app.models.hail_status import HailStatus
from app.models.role import Role
from app.contracts.hails.create_hail_request import (
    CreateHailRequest,
)
from app.services.fleet_hails_service import FleetHailsService
from app.services.hails_service import HailsService
from app.services.logger_service import LoggerService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.services.option_matrix_service import OptionMatrixService
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder
from app.utils.exceptions.driver_exception_builder import DriverExceptionBuilder
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from app.utils.exception_doc_utils import ExceptionDocUtils
from fastapi_controllers import Controller, delete, get, patch, post, websocket
from dependency_injector.wiring import inject, Provide

from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder


class HailsController(Controller):
    prefix = "/hails"
    tags = ["Hails"]
    driver_repository: DriverRepository
    logger_service: LoggerService
    hails_service: HailsService
    option_matrix_service: OptionMatrixService
    fleet_hails_service: FleetHailsService

    @inject
    def __init__(
        self,
        driver_service: DriverRepository = Depends(
            Provide[Container.driver_repository]
        ),
        logger_service: LoggerService = Depends(
            Provide[Container.logger_service]),
        hails_service: HailsService = Depends(
            Provide[Container.hails_service]),
        option_matrix_service: OptionMatrixService = Depends(
            Provide[Container.option_matrix_service]
        ),
        fleet_hails_service: FleetHailsService = Depends(
            Provide[Container.fleet_hails_service]
        ),
    ) -> None:
        self.driver_repository = driver_service
        self.logger_service = logger_service
        self.hails_service = hails_service
        self.option_matrix_service = option_matrix_service
        self.fleet_hails_service = fleet_hails_service

    @get("/riders", response_model=list[HailRiderResponse], status_code=200)
    async def get_hails_for_riders(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        test_id: str = Query(None),
    ) -> list[HailRiderResponse]:
        """
        Get all the hails for the user that are yet to occur
        """
        user_id = (
            test_id if test_id is not None and settings.allow_testing else auth.user_id
        )

        self.logger_service.log(
            f"Rider {user_id} get hails",
        )
        return await self.hails_service.get_hails_for_riders(user_id)

    @get(
        "/{hail_id}/cancellation_fee",
        response_model=HailCancellationFeeResponse,
        status_code=200,
    )
    async def get_hail_cancellation_fee(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        hail_id: str,
    ) -> HailCancellationFeeResponse:
        """
        Get a hail cancellation fee for the user
        """
        self.logger_service.log(
            f"Rider {auth.user_id} get hail {hail_id}",
        )
        return await self.hails_service.get_hail_cancellation_fee(hail_id)

    @get("/drivers", response_model=list[HailDriverResponse], status_code=200)
    async def get_hails_for_drivers(
        self, auth: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.DRIVER))]
    ) -> list[HailDriverResponse]:
        """
        Get all hails matched with a driver
        """
        self.logger_service.log(
            f"Driver {auth.driver_id} get hails",
        )

        driver = await self.driver_repository.get_driver_by_id(auth.driver_id)

        return await self.hails_service.get_hails_for_drivers(driver)

    @post(
        "",
        response_model=HailResponse,
        status_code=201,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [HailExceptionBuilder().hail_already_exists("123")]
        ),
    )
    async def create_hail_request(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        create_hail_request: CreateHailRequest,
        background_tasks: BackgroundTasks,
        test_id: str = Query(None),
    ) -> HailResponse:
        self.logger_service.log(
            f"Creating hail",
            {"create_hail_request": create_hail_request.model_dump_json()},
        )

        user_id = (
            test_id if test_id is not None and settings.allow_testing else auth.user_id
        )

        return await self.hails_service.create_hail_request(
            auth=auth,
            create_hail_request=create_hail_request,
            user_id=user_id,
            background_tasks=background_tasks,
        )

    @patch(
        "/{hail_id}/riders",
        response_model=HailRiderResponse,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
            ]
        ),
    )
    async def update_hail_by_riders(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        hail_id: str,
        update_hail_request: Annotated[
            RiderHailCancelledRequest,
            Body(
                openapi_examples={
                    HailStatus.CANCELLED: {
                        "summary": RiderHailCancelledRequest.__name__,
                        "value": RiderHailCancelledRequest(
                            status=HailStatus.CANCELLED,
                            reasons=["Driver not at position"],
                        ).model_dump(by_alias=True),
                    },
                }
            ),
        ],
    ) -> HailRiderResponse:
        self.logger_service.log(
            f"Rider {auth.user_id} updating hail {hail_id}",
            update_hail_request.model_dump(),
        )

        return await self.hails_service.update_hail_by_riders(
            auth=auth, hail_id=hail_id, update_hail_request=update_hail_request
        )

    @patch(
        "/{hail_id}/boost",
        response_model=BoostUpdateResponse,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
                HailExceptionBuilder().invalid_boost_amount(
                    "Boost amount is required and must be greater than 0"
                ),
                HailExceptionBuilder().boost_limit_exceeded(
                    current_boost=450.0, max_limit=500.0
                ),
            ]
        ),
    )
    async def update_hail_boost(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        hail_id: str,
        boost_request: RiderHailBoostRequest,
    ) -> BoostUpdateResponse:
        self.logger_service.log(
            f"Rider {auth.user_id} updating boost for hail {hail_id}",
            boost_request.model_dump(),
        )

        now = DateTimeUtils.utc_now_with_tz()

        return await self.hails_service.update_hail_boost(
            auth=auth,
            hail_id=hail_id,
            boost_request=boost_request,
            timestamp=now,
        )

    @get(
        "/{hail_id}/drivers",
        response_model=HailDriverResponse,
        status_code=status.HTTP_200_OK,
    )
    async def get_hail_by_drivers(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.DRIVER))],
        hail_id: str,
    ) -> HailDriverResponse:
        self.logger_service.log(
            f"Driver {auth.driver_id} get hail {hail_id}",
        )
        return await self.hails_service.get_hail_by_drivers(auth=auth, hail_id=hail_id)

    @patch(
        "/{hail_id}/drivers",
        response_model=HailDriverResponse,
        status_code=status.HTTP_200_OK,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                HailExceptionBuilder().invalid_meter_pairing_state(),
                AuthExceptionBuilder().auth_forbidden_resource(),
                DriverExceptionBuilder().driver_not_found(driver_id="123"),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
                MeterExceptionBuilder().active_meter_trip_not_found(meter_id="123"),
            ]
        ),
    )
    async def update_hail_by_drivers(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth(required_role=Role.DRIVER))],
        hail_id: str,
        update_hail_request: Annotated[
            Union[
                DriverHailAcceptedRequest,
                DriverHailOnGoingRequest,
                DriverHailCancelledRequest,
                DriverHailCompletedRequest,
            ],
            Body(
                openapi_examples={
                    HailStatus.ACCEPTED: {
                        "summary": DriverHailAcceptedRequest.__name__,
                        "value": DriverHailAcceptedRequest(
                            status=HailStatus.ACCEPTED
                        ).model_dump(by_alias=True),
                    },
                    HailStatus.ON_GOING: {
                        "summary": DriverHailOnGoingRequest.__name__,
                        "value": DriverHailOnGoingRequest(
                            status=HailStatus.ON_GOING
                        ).model_dump(by_alias=True),
                    },
                    HailStatus.CANCELLED: {
                        "summary": DriverHailCancelledRequest.__name__,
                        "value": DriverHailCancelledRequest(
                            status=HailStatus.CANCELLED,
                            reasons=["Passenger not at position"],
                        ).model_dump(by_alias=True),
                    },
                    HailStatus.COMPLETED: {
                        "summary": DriverHailCompletedRequest.__name__,
                        "value": DriverHailCompletedRequest(
                            status=HailStatus.COMPLETED
                        ).model_dump(by_alias=True),
                    },
                }
            ),
        ],
        background_tasks: BackgroundTasks,
    ) -> HailDriverResponse:
        self.logger_service.log(
            f"Driver {auth.driver_id} updating hail {hail_id}",
            update_hail_request.model_dump(),
        )

        now = DateTimeUtils.utc_now_with_tz()

        return await self.hails_service.update_hail_by_drivers(
            auth=auth,
            hail_id=hail_id,
            update_hail_request=update_hail_request,
            background_tasks=background_tasks,
            timestamp=now,
        )

    @patch(
        "/{hail_id}",
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
            ]
        ),
        status_code=status.HTTP_204_NO_CONTENT,
    )
    async def update_hail_by_service(
        self,
        auth: Annotated[Auth, Depends(HttpServiceAuth())],
        hail_id: str,
        update_hail_request: Annotated[
            ServiceHailCompletedRequest,
            Body(
                openapi_examples={
                    HailStatus.COMPLETED: {
                        "summary": ServiceHailCompletedRequest.__name__,
                        "value": ServiceHailCompletedRequest(
                            status=HailStatus.COMPLETED
                        ).model_dump(by_alias=True),
                    },
                }
            ),
        ],
        background_tasks: BackgroundTasks,
    ) -> None:
        self.logger_service.log(
            f"Service updating hail {hail_id}",
            update_hail_request.model_dump(),
        )

        await self.hails_service.update_hail_by_service(
            auth=auth, hail_id=hail_id, update_hail_request=update_hail_request, background_tasks=background_tasks
        )

        return None

    @post("/active", status_code=status.HTTP_204_NO_CONTENT)
    async def update_active_hails(self) -> None:
        await self.hails_service.update_active_hails()

    @post(
        "/{hail_id}/fleet",
        response_model=HailRiderResponse,
        status_code=status.HTTP_200_OK,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()),
                ),
            ]
        ),
    )
    async def update_hail_by_fleet(
        self,
        _: Annotated[Auth, Depends(multi_auth_dependency)],
        hail_id: str,
        update_hail_request: FleetHailUpdateRequest,
    ) -> HailRiderResponse:
        return await self.fleet_hails_service.update_hail_by_status(
            hail_id=hail_id, fleet_hail_update_request=update_hail_request
        )

    @delete(
        "/{hail_id}/fleet",
        response_model=HailRiderResponse,
        status_code=status.HTTP_200_OK,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                AuthExceptionBuilder().auth_forbidden_resource(),
                HailExceptionBuilder().hail_not_found("123"),
                HailExceptionBuilder().hail_not_updatable(
                    hail_status=HailStatus.COMPLETED,
                    hail_id=str(uuid4()), 
                ),
            ]
        ),
    )
    async def cancel_hail_by_fleet(
        self,
        auth: Annotated[Auth, Depends(multi_auth_dependency)],
        hail_id: str,
    ) -> HailRiderResponse:
        return await self.fleet_hails_service.cancel_hail_by_fleet(
            auth=auth, hail_id=hail_id
        )

    @websocket("/{hail_id}")
    async def rider_hail_websocket(
        self,
        auth: Annotated[Auth, Depends(SocketAuth())],
        websocket: WebSocket,
        hail_id: str,
        test_id: str = Query(None),
    ) -> None:
        user_id = (
            test_id if test_id is not None and settings.allow_testing else auth.user_id
        )
        self.logger_service.log(
            f"Rider {user_id} connecting to hail {hail_id} websocket",
        )
        await self.hails_service.rider_hail_websocket(
            user_id=user_id, websocket=websocket, hail_id=hail_id
        )
