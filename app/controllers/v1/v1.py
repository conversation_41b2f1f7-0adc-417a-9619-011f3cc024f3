from fastapi import APIRouter

from app.controllers.v1.admin_controller import <PERSON>min<PERSON><PERSON>roll<PERSON>
from app.controllers.v1.health_controller import Health<PERSON>ontroller
from app.controllers.v1.pricing_controller import PricingController
from app.controllers.v1.reports_controller import ReportsController
from .hails_controller import Hai<PERSON><PERSON><PERSON>roller
from .drivers_controller import DriversController
from .docs_controller import DocsController


v1_router = APIRouter(prefix="/v1")

v1_router.include_router(HealthController.create_router())
v1_router.include_router(DocsController.create_router())
v1_router.include_router(DriversController.create_router())
v1_router.include_router(HailsController.create_router())
v1_router.include_router(PricingController.create_router())
v1_router.include_router(ReportsController.create_router())
v1_router.include_router(AdminController.create_router())
