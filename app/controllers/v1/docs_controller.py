from fastapi import Response
from fastapi_controllers import Controller, get
import yaml
from app.models.socket_doc_type import SocketDocType
from app.utils.async_api_utils import async_api_utils


class DocsController(Controller):
    prefix = "/docs"
    tags = ["Docs"]

    @get(
        "/drivers-socket",
        name="Asyncapi driver socket document generator",
        description="Copy and paste yaml to https://playground.asyncapi.io/ to visualize the asyncapi spec.",
    )
    def get_driver_socket_docs(self) -> Response:
        output = yaml.dump(
            async_api_utils.docs[SocketDocType.DRIVERS], default_flow_style=False
        )
        return Response(content=output, media_type="text/yaml")

    @get(
        "/hails-socket",
        name="Asyncapi hail socket document generator",
        description="Copy and paste yaml to https://playground.asyncapi.io/ to visualize the asyncapi spec.",
    )
    def get_hail_socket_docs(self) -> Response:
        output = yaml.dump(
            async_api_utils.docs[SocketDocType.HAILS], default_flow_style=False
        )
        return Response(content=output, media_type="text/yaml")
