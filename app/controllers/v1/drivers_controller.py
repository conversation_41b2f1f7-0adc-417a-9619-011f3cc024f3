import asyncio
from datetime import datetime
from typing import Annotated
from app.contracts.drivers.update_heart_beat_request import UpdateHeartBeatRequest
from app.infrastructure.dependencies.multi_auth import multi_auth_dependency
from app.services.correlation_service import CorrelationService
from app.utils.datetime_utils import DateTimeUtils
from fastapi import (
    Depends,
    Query,
    WebSocket,
    WebSocketDisconnect,
    WebSocketException,
    status,
)
import traceback
from fastapi.websockets import WebSocketState
from fastapi_controllers import Controller, patch, websocket
from pydantic import ValidationError
from websockets import ConnectionClosed
from app.infrastructure.settings import settings
from app.infrastructure.container import Container
from app.infrastructure.dependencies.socket_auth import SocketAuth
from app.models.auth import Auth
from app.models.role import Role
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.firestore.repositories.firestore_repository import (
    FirestoreRepository,
)
from app.persistence.firestore.repositories.meter_repository import MeterRepository
from app.persistence.firestore.repositories.vehicle_repository import VehicleRepository
from app.persistence.redis.entities.driver import Driver
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.persistence.redis.repositories.tracking_repository import TrackingRepository
from app.services.fare_calculation_service import FareCalculationService
from app.services.logger_service import LoggerService
from app.sockets.message_code import MessageCode
from app.sockets.messages.driver_ack_message import DriverAckMessage
from app.sockets.messages.driver_heartbeat_message import DriverHeartbeatMessage
from app.sockets.messages.driver_pending_hails_message import DriverPendingHailsMessage
from app.sockets.messages.driver_session_created_message import (
    DriverSessionCreatedMessage,
)
from app.sockets.messages.driver_session_start_message import (
    DriverSessionStartMessage,
)
from app.sockets.messages.exception_message import ExceptionMessage
from app.persistence.redis.repositories.driver_repository import DriverRepository
from dependency_injector.wiring import inject, Provide

from app.sockets.socket_service import SocketService
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.dash_exception_builder import DashExceptionBuilder
from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder
from app.utils.exceptions.vehicle_exception_builder import VehicleExceptionBuilder


class DriversController(Controller):
    prefix = "/drivers"
    tags = ["Drivers"]
    logger_service: LoggerService
    hail_repository: HailRepository
    driver_service: DriverRepository
    firestore_repository: FirestoreRepository
    vehicle_repository: VehicleRepository
    configuration_repository: ConfigurationsRepository
    meter_repository: MeterRepository
    socket_service: SocketService
    tracking_repository: TrackingRepository
    fare_calculation_service: FareCalculationService
    correlation_service: CorrelationService

    @inject
    def __init__(
        self,
        logger_service: LoggerService = Depends(
            Provide[Container.logger_service]),
        hail_service: HailRepository = Depends(
            Provide[Container.hail_repository]),
        driver_service: DriverRepository = Depends(
            Provide[Container.driver_repository]
        ),
        firestore_repository: FirestoreRepository = Depends(
            Provide[Container.firestore_repository]
        ),
        vehicle_repository: VehicleRepository = Depends(
            Provide[Container.vehicle_repository]
        ),
        configuration_repository: ConfigurationsRepository = Depends(
            Provide[Container.configuration_repository]
        ),
        meter_repository: MeterRepository = Depends(
            Provide[Container.meter_repository]
        ),
        socket_service: SocketService = Depends(
            Provide[Container.socket_service]),
        tracking_repository: TrackingRepository = Depends(
            Provide[Container.tracking_repository]
        ),
        fare_calculation_service: FareCalculationService = Depends(
            Provide[Container.fare_calculation_service]
        ),
        correlation_service: CorrelationService = Depends(
            Provide[Container.correlation_service]
        ),
    ):
        self.logger_service = logger_service
        self.hail_repository = hail_service
        self.driver_service = driver_service
        self.firestore_repository = firestore_repository
        self.vehicle_repository = vehicle_repository
        self.configuration_repository = configuration_repository
        self.meter_repository = meter_repository
        self.socket_service = socket_service
        self.tracking_repository = tracking_repository
        self.fare_calculation_service = fare_calculation_service
        self.correlation_service = correlation_service

    @websocket("")
    async def driver_websocket(
        self,
        auth: Annotated[Auth, Depends(SocketAuth(required_role=Role.DRIVER))],
        websocket: WebSocket,
        test_id: str = Query(None),
    ) -> None:
        self.logger_service.debug(
            f"Driver connecting to websocket {auth.phone_number}",
        )
        is_testing = settings.allow_testing and test_id is not None
        driver_id = test_id if is_testing else auth.phone_number
        now = DateTimeUtils.utc_now_with_tz()

        try:
            await self.socket_service.connect(driver_id, websocket)
        except Exception as e:
            self.logger_service.error(
                f"Failed to accept driver WebSocket connection: {e}"
            )
            raise WebSocketException(
                code=status.WS_1013_TRY_AGAIN_LATER,
            )

        is_connected = websocket.client_state == WebSocketState.CONNECTED

        async def close_connection(driver_id: str, websocket: WebSocket) -> None:
            self.logger_service.log(f"Driver session end: {driver_id}")
            await self.socket_service.disconnect(driver_id, websocket=websocket)
            if not self.socket_service.socket_id_has_connections(driver_id):
                await self.driver_service.remove_driver(driver_id)
                await self.tracking_repository.set_driver_session_end(
                    driver_id=driver_id, session_end=datetime.now()
                )

        while is_connected and websocket.client_state == WebSocketState.CONNECTED:
            try:
                message_dict = await websocket.receive_json()
                message = message_dict["message"]
                if message is None:
                    continue

                match message:
                    case MessageCode.DRIVER_ACK:
                        driver_ack_message = DriverAckMessage.model_validate(
                            message_dict
                        )
                        self.logger_service.log_function_end(
                            "drivers_controller", "driver_ack",
                            driver_ack_message=driver_ack_message, driver_id=driver_id,
                        )
                    case MessageCode.DRIVER_HEARTBEAT:
                        driver_heartbeat_message = (
                            DriverHeartbeatMessage.model_validate(message_dict)
                        )
                        await self.driver_service.update_driver_location(
                            driver_id=driver_id,
                            driver_heartbeat=driver_heartbeat_message.payload,
                        )
                    case MessageCode.DRIVER_SESSION_START:
                        driver_session_start_message = (
                            DriverSessionStartMessage.model_validate(
                                message_dict)
                        )

                        self.logger_service.log_function_end(
                            "drivers_controller", "driver_session_start", 
                            driver_session_start_message=driver_session_start_message,
                            driver_id=driver_id,
                        )

                        meter = await self.meter_repository.get_meter_by_id(
                            driver_session_start_message.payload.license_plate
                        )
                        if meter is None:
                            raise MeterExceptionBuilder().meter_not_found(
                                meter_id=driver_session_start_message.payload.license_plate
                            )

                        vehicle = await self.vehicle_repository.get_vehicle_by_id(
                            meter.settings.vehicle_id
                        )
                        if vehicle is None:
                            raise VehicleExceptionBuilder().vehicle_not_found(
                                vehicle_id=meter.settings.vehicle_id
                            )

                        fleet = (
                            await self.firestore_repository.fleet_repository().get_fleet_by_id(
                                meter.settings.fleet_id
                            )
                            if meter.settings.fleet_id
                            else None
                        )

                        driver = await Driver.from_create_driver_session_request(
                            driver_id,
                            driver_session_start_message.payload,
                            meter,
                            vehicle,
                            fleet,
                        )

                        driver_session_response = driver.to_driver_session_response()

                        await self.socket_service.send_personal_message(
                            driver_id,
                            data=DriverSessionCreatedMessage(
                                payload=driver_session_response
                            ),
                        )

                        if driver.operating_area:
                            await self.driver_service.add_driver_to_operating_area(
                                driver
                            )

                        if driver_session_start_message.payload.heart_beat:
                            await self.driver_service.update_driver_location(
                                driver_id=driver_id,
                                driver_heartbeat=driver_session_start_message.payload.heart_beat,
                            )

                        await self.tracking_repository.set_driver_session_start(
                            driver.driver_id, session_start=datetime.now()
                        )

                        hails_viable_for_driver = (
                            await self.hail_repository.get_viable_hails_for_driver(
                                driver=driver,
                                pin=driver_session_start_message.payload.heart_beat,
                            )
                        )

                        viable_pending_hails = [
                            hail for hail in hails_viable_for_driver if hail.is_pending
                        ]

                        self.logger_service.log_function_end(
                            "drivers_controller", "viable_pending_hails", driver_id=driver_id, viable_pending_hails=viable_pending_hails,
                        )

                        hail_config = (
                            await self.configuration_repository.get_hail_configuration()
                        )

                        testing_hail_count = 30
                        await self.socket_service.send_personal_message(
                            driver.id,
                            DriverPendingHailsMessage(
                                payload=[
                                    hail.to_hail_driver_response(
                                        driver=driver,
                                        hail_config=hail_config,
                                        fare_calculation_service=self.fare_calculation_service,
                                    )
                                    for hail in (
                                        viable_pending_hails[:testing_hail_count]
                                        if is_testing
                                        else viable_pending_hails
                                    )
                                ]
                            ),
                        )

                        await asyncio.gather(
                            *[
                                self.driver_service.set_drivers_for_hail(hail, [
                                                                         driver], timestamp=now)
                                for hail in viable_pending_hails
                            ]
                        )

            except (WebSocketDisconnect, ConnectionClosed) as e:
                is_connected = False
                await close_connection(driver_id, websocket)
            except BaseHailException as e:
                self.logger_service.warning(
                    f"[{e.status_code}] {e.code} - {e.detail}")
                await self.socket_service.send_personal_message(
                    driver_id,
                    ExceptionMessage(
                        payload=e.to_response(
                            {"origin": message_dict["message"]}),
                    ),
                )
            except ValidationError as e:
                self.logger_service.warning("Validation error")
                validation_exception = BaseHailException.from_validation_error(
                    e)
                await self.socket_service.send_personal_message(
                    driver_id,
                    ExceptionMessage(
                        payload=validation_exception.to_response(
                            {"origin": message_dict["message"]}
                        ),
                    ),
                )
            except Exception as e:
                unexpected_exception = DashExceptionBuilder().unexpected_error()
                exception_str = str(e)
                self.logger_service.error(
                    f"[{unexpected_exception.status_code}] {unexpected_exception.code} - {exception_str}",
                    extra={
                        "exception": exception_str,
                        "traceback": traceback.format_exc(),
                    },
                )
                await self.socket_service.send_personal_message(
                    driver_id,
                    ExceptionMessage(
                        payload=unexpected_exception.to_response(
                            {"origin": message_dict["message"]}
                        ),
                    ),
                )
                is_connected = False
                await close_connection(driver_id, websocket)

        is_connected = False
        await close_connection(driver_id, websocket)

    @patch("/update_heart_beat")
    async def update_heart_beat(
        self,
        _: Annotated[Auth, Depends(multi_auth_dependency)],
        update_heart_beat_request: UpdateHeartBeatRequest,
    ) -> None:
        await self.driver_service.update_driver_location(
            update_heart_beat_request.phone_number,
            update_heart_beat_request.heart_beat,
        )

        await self.hail_repository.notify_hail_by_id_of_driver_pin(
            update_heart_beat_request.hail_id
        )
