import json
from typing import Annotated
from app.contracts.campaigns.rule_params import Rule<PERSON>ara<PERSON>
from fastapi import Depends, status
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from app.contracts.matrix.create_pre_auth_amount_request import (
    CreateEstimatedFeeRangeRequest,
)
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetOptionMatrixV2
from app.contracts.matrix.hail_option_matrix import HailOptionMatrix
from app.contracts.matrix.hail_option_matrix_v2 import HailOptionMatrixV2
from app.contracts.matrix.pre_auth_estimation_response import (
    EstimatedTotalFeeRangeResponse,
)
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import HttpUserAuth
from app.models.auth import Auth
from app.models.hail_platform_type import HailPlatformType
from app.services.campaign_service import CampaignService
from app.services.logger_service import LoggerService
from app.services.option_matrix_service import (
    OptionMatrixService,
)
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from app.utils.exception_doc_utils import ExceptionDocUtils
from fastapi_controllers import Controller, post
from dependency_injector.wiring import inject, Provide

from app.utils.exceptions.location_exception_builder import LocationExceptionBuilder
from app.utils.exceptions.pricing_exception_builder import PricingExceptionBuilder


class PricingController(Controller):
    prefix = "/pricing"
    tags = ["Pricing"]
    option_matrix_service: OptionMatrixService
    campaign_service: CampaignService
    logger_service: LoggerService

    @inject
    def __init__(
        self,
        option_matrix_service: OptionMatrixService = Depends(
            Provide[Container.option_matrix_service]
        ),
        campaign_service: CampaignService = Depends(
            Provide[Container.campaign_service]
        ),
        logger_service: LoggerService = Depends(
            Provide[Container.logger_service]),
    ) -> None:
        self.option_matrix_service = option_matrix_service
        self.campaign_service = campaign_service
        self.logger_service = logger_service

    @post(
        "/matrix",
        response_model=HailOptionMatrix,
        status_code=status.HTTP_201_CREATED,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                LocationExceptionBuilder().location_service_failure(),
                HailExceptionBuilder().hail_config_not_found(),
            ]
        ),
    )
    async def get_hail_option_matrix(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
    ) -> HailOptionMatrix:
        """
        Build a matrix of hail options and sub options including pricing.
        """
        option_matrix = await self.option_matrix_service.get_hail_option_matrix_v1(
            create_hail_option_matrix_request,
            auth=auth,
        )
        self.logger_service.log(
            "Hail option matrix created",
            extra={"matrix": json.loads(option_matrix.model_dump_json())},
        )
        return option_matrix

    @post(
        "/estimated-total-fee-range",
        response_model=EstimatedTotalFeeRangeResponse,
        status_code=status.HTTP_201_CREATED,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                PricingExceptionBuilder().insufficient_options_selected(),
                PricingExceptionBuilder().fleet_vehicle_type_not_provided(),
                LocationExceptionBuilder().location_service_failure(),
                LocationExceptionBuilder().location_outside_of_service_area(),
                HailExceptionBuilder().hail_config_not_found(),
            ]
        ),
    )
    async def get_pre_auth_amount(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        create_estimated_fee_range_request: CreateEstimatedFeeRangeRequest,
    ) -> EstimatedTotalFeeRangeResponse:
        """
        Estimate the minimum and maximum total fee for a hail request.
        """
        self.logger_service.log(
            f"Get Pre Auth Range",
            {
                "create_estimated_fee_range_request": create_estimated_fee_range_request.model_dump_json()
            },
        )

        matrix: HailOptionMatrixV2 | HailFleetOptionMatrixV2

        applicable_campaigns = await self.campaign_service.get_applicable_campaigns(
            auth=auth,
            rule_params=RuleParams.from_create_hail_option_matrix_request(
                create_estimated_fee_range_request
            ),
        )

        if create_estimated_fee_range_request.platform_type == HailPlatformType.FLEET:
            matrix = await self.option_matrix_service.get_fleet_hail_option_matrix(
                CreateHailOptionMatrixRequest(
                    place_ids=create_estimated_fee_range_request.place_ids,
                    time=create_estimated_fee_range_request.time,
                    language=create_estimated_fee_range_request.language,
                    session_token=create_estimated_fee_range_request.session_token,
                    double_tunnel_fee=create_estimated_fee_range_request.double_tunnel_fee,
                    payment_instrument_type=create_estimated_fee_range_request.payment_instrument_type,
                    applicable_campaigns=applicable_campaigns,
                ),
                auth=auth,
            )
            if create_estimated_fee_range_request.fleet_vehicle_type is None:
                raise PricingExceptionBuilder().fleet_vehicle_type_not_provided()
            min_max_fare_calculations = matrix.min_max_fare_calculations(
                create_estimated_fee_range_request.fleet_vehicle_type
            )
        else:
            matrix = await self.option_matrix_service.get_hail_option_matrix_v2(
                CreateHailOptionMatrixRequest(
                    place_ids=create_estimated_fee_range_request.place_ids,
                    time=create_estimated_fee_range_request.time,
                    language=create_estimated_fee_range_request.language,
                    session_token=create_estimated_fee_range_request.session_token,
                    double_tunnel_fee=create_estimated_fee_range_request.double_tunnel_fee,
                    payment_instrument_type=create_estimated_fee_range_request.payment_instrument_type,
                    applicable_campaigns=applicable_campaigns,
                ),
                auth=auth,
            )
            min_max_fare_calculations = matrix.min_max_fare_calculations(
                create_estimated_fee_range_request.vehicle_preferences
            )

        self.logger_service.debug(
            "Min Max Fare Calculations",
            extra={
                "min_max_fare_calculations": (
                    [
                        json.loads(fare.model_dump_json())
                        for fare in min_max_fare_calculations
                    ]
                    if min_max_fare_calculations
                    else None
                ),
            },
        )

        if min_max_fare_calculations is None or len(min_max_fare_calculations) < 1:
            raise PricingExceptionBuilder().insufficient_options_selected()

        return EstimatedTotalFeeRangeResponse(
            min=min_max_fare_calculations[0].discounted_total,
            max=min_max_fare_calculations[1].discounted_total,
        )
