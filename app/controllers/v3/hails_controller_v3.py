from typing import Annotated
import traceback
from fastapi import BackgroundTasks, Depends
from fastapi_controllers import Controller, get, post

from app.contracts.hails.create_hail_request_v3 import CreateHailRequestV3
from app.contracts.hails.hail_response import HailResponse
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import Http<PERSON><PERSON><PERSON>uth
from app.infrastructure.dependencies.http_x_api_key_auth import HttpXApi<PERSON>eyAuth
from app.models.auth import Auth
from app.services.fleet_fare_service import FleetFareService
from app.services.hail_quote_service import HailQuoteService
from app.services.hails_service_v3 import HailsV3Service
from app.services.logger_service import LoggerService
from app.services.standard_fare_service import StandardFareService
from dependency_injector.wiring import inject, Provide

class HailsControllerV3(Controller):
    prefix = "/hails"
    tags = ["HailsV3"]
    logger_service: LoggerService
    standard_fare_service: StandardFareService
    fleet_fare_service: FleetFareService
    hail_quote_service: HailQuoteService
    hails_v3_service: HailsV3Service

    @inject
    def __init__(
        self,
        logger_service: LoggerService = Depends(Provide[Container.logger_service]),
        hails_v3_service: HailsV3Service = Depends(Provide[Container.hails_v3_service]),
    ) -> None:
        self.logger_service = logger_service
        self.hails_v3_service = hails_v3_service

    @post("")
    async def create_hail_request(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        request: CreateHailRequestV3,
        background_tasks: BackgroundTasks,
    ) -> HailResponse:
        """
        Create a new hail request.
        """
        try:
            return await self.hails_v3_service.create_hail_request(auth, request, background_tasks)
        except Exception as e:
            # Log error with stack trace in JSON format
            self.logger_service.error(
                f"HailsControllerV3-create_hail_request-end",
                extra={
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                    "user_id": auth.user_id if auth else None,
                    "phone_number": auth.phone_number if auth else None,
                    "request_data": request.model_dump() if request else None,
                }
            )
            # Re-raise the exception to be handled by the global exception handler
            raise e
        
    @get("/{hail_id}/riders", response_model=HailResponse | None, status_code=200)
    async def get_hail_by_riders(
        self,
        _: Annotated[Auth, Depends(HttpXApiKeyAuth())],
        hail_id: str,
    ) -> HailResponse | None:
        """
        Get a hail request by id.
        """
        try:
            return await self.hails_v3_service.get_hail_by_riders(hail_id)
        except Exception as e:
            # Log error with stack trace in JSON format
            self.logger_service.error(
                f"HailsControllerV3-get_hail_by_riders-end",
                extra={
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                    "hail_id": hail_id,
                }
            )
            # Re-raise the exception to be handled by the global exception handler
            raise e