import traceback
from typing import Annotated
from fastapi import Depends
from fastapi_controllers import Controller, post

from app.contracts.matrix.create_estimate_total_range_price_request import CreateEstimateTotalRangePriceRequest
from app.contracts.matrix.create_hail_quote_request import CreateHailQuoteRequest
from app.contracts.matrix.create_hail_quote_response import CreateHailQuoteResponse
from app.contracts.matrix.pre_auth_estimation_response import EstimatedTotalFeeRangeResponse
from app.contracts.matrix.pre_auth_estimation_response_v2 import EstimatedTotalFeeRangeResponseV2
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import HttpUserAuth
from app.models.auth import Auth
from app.models.discount_rules import DiscountRules
from app.models.hail_platform_type import HailPlatformType
from app.services.fleet_fare_service import FleetFareService
from app.services.hail_quote_service import HailQuoteService
from app.services.logger_service import LoggerService
from app.services.standard_fare_service import StandardFareService
from dependency_injector.wiring import inject, Provide

class PricingControllerV3(Controller):
    prefix = "/pricing"
    tags = ["PricingV3"]
    logger_service: LoggerService
    standard_fare_service: StandardFareService
    fleet_fare_service: FleetFareService
    hail_quote_service: HailQuoteService

    @inject
    def __init__(
        self,
        logger_service: LoggerService = Depends(Provide[Container.logger_service]),
        standard_fare_service: StandardFareService = Depends(
            Provide[Container.standard_fare_service]
        ),
        fleet_fare_service: FleetFareService = Depends(
            Provide[Container.fleet_fare_service]
        ),
        hail_quote_service: HailQuoteService = Depends(
            Provide[Container.hail_quote_service]
        ),
    ) -> None:
        self.logger_service = logger_service
        self.standard_fare_service = standard_fare_service
        self.fleet_fare_service = fleet_fare_service
        self.hail_quote_service = hail_quote_service


    @post("/estimated-total-fee-range")
    async def get_pre_auth_amount(
      self,
      auth: Annotated[Auth, Depends(HttpUserAuth())],
      request: CreateEstimateTotalRangePriceRequest,
    ) -> EstimatedTotalFeeRangeResponseV2:
        """
        Estimate the minimum and maximum total fee for a hail request.
        """
        try:
            discount_rules = request.applicable_discounts.to_discount_rules()
            
            if request.platform_type == HailPlatformType.FLEET and request.fleet_vehicle_type_key:
                min_fare, max_fare = await self.fleet_fare_service.get_min_max_fare_calculation_by_fleet_vehicle_type(
                    auth=auth,
                    place_ids=request.place_ids,
                    language=request.language,
                    fleet_vehicle_type=request.fleet_vehicle_type_key,
                    price_rules=request.price_rules,
                    request_time=request.time,
                    discount_rules=discount_rules,
                )
            else:
                min_fare, max_fare = await self.standard_fare_service.get_min_max_fare_calculation_by_vehicle_classes(
                    auth=auth,
                    place_ids=request.place_ids,
                    language=request.language,
                    price_rules=request.price_rules,
                    is_double_tunnel_fee=request.is_double_tunnel_fee,
                    vehicle_classes=request.prefer_vehicle_classes,
                    discount_rules=discount_rules,
                )
            
            self.logger_service.debug(
                f"Estimated total fee range",
                {
                    "min": min_fare.model_dump_json(),
                    "max": max_fare.model_dump_json(),
                },
            )
            
            return EstimatedTotalFeeRangeResponseV2(
                min=min_fare.discounted_total,
                max=max_fare.discounted_total,
                min_fare_calculation=min_fare,
                max_fare_calculation=max_fare,
            )
        except Exception as e:
            # Log error with stack trace in JSON format
            self.logger_service.error(
                f"PricingControllerV3-get_pre_auth_amount-end",
                extra={
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                    "user_id": auth.user_id if auth else None,
                    "phone_number": auth.phone_number if auth else None,
                    "request_data": request.model_dump() if request else None,
                }
            )
            # Re-raise the exception to be handled by the global exception handler
            raise e
        
    @post('/quotes')
    async def get_hail_quote(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        request: CreateHailQuoteRequest,
    ) -> CreateHailQuoteResponse:
        try:
            return await self.hail_quote_service.get_quotes(auth, request)
        except Exception as e:
            # Log error with stack trace in JSON format
            self.logger_service.error(
                f"PricingControllerV3-get_hail_quote-end",
                extra={
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                    "user_id": auth.user_id if auth else None,
                    "phone_number": auth.phone_number if auth else None,
                    "request_data": request.model_dump() if request else None,
                }
            )
            # Re-raise the exception to be handled by the global exception handler
            raise e
