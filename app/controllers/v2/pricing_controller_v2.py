import json
from typing import Annotated
from app.contracts.campaigns.rule_params import Rule<PERSON>ara<PERSON>
from fastapi import Depends, status
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetOptionMatrixV2
from app.contracts.matrix.hail_option_matrix_v2 import HailOptionMatrixV2
from app.infrastructure.container import Container
from app.infrastructure.dependencies.http_user_auth import Http<PERSON>serAuth
from app.models.auth import Auth
from app.models.hail_platform_type import HailPlatformType
from app.services.campaign_service import CampaignService
from app.services.logger_service import LoggerService
from app.services.option_matrix_service import (
    OptionMatrixService,
)
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from app.utils.exception_doc_utils import ExceptionDocUtils
from fastapi_controllers import Controller, post
from dependency_injector.wiring import inject, Provide

from app.utils.exceptions.location_exception_builder import LocationExceptionBuilder


class PricingControllerV2(Controller):
    prefix = "/pricing"
    tags = ["Pricing"]
    option_matrix_service: OptionMatrixService
    campaign_service: CampaignService
    logger_service: LoggerService

    @inject
    def __init__(
        self,
        option_matrix_service: OptionMatrixService = Depends(
            Provide[Container.option_matrix_service]
        ),
        campaign_service: CampaignService = Depends(
            Provide[Container.campaign_service]
        ),
        logger_service: LoggerService = Depends(
            Provide[Container.logger_service]),
    ) -> None:
        self.option_matrix_service = option_matrix_service
        self.campaign_service = campaign_service
        self.logger_service = logger_service

    @post(
        "/matrix",
        response_model=HailOptionMatrixV2 | HailFleetOptionMatrixV2,
        status_code=status.HTTP_201_CREATED,
        responses=ExceptionDocUtils.get_exception_response_docs(
            [
                LocationExceptionBuilder().location_service_failure(),
                HailExceptionBuilder().hail_config_not_found(),
            ]
        ),
    )
    async def get_hail_option_matrix(
        self,
        auth: Annotated[Auth, Depends(HttpUserAuth())],
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
    ) -> HailOptionMatrixV2 | HailFleetOptionMatrixV2:
        """
        Build a matrix of hail options and sub options including pricing.
        """

        self.logger_service.log(
            f"Get V2 hail option matrix request",
            {
                "create_hail_option_matrix_request": create_hail_option_matrix_request.model_dump_json()
            },
        )

        applicable_campaigns = await self.campaign_service.get_applicable_campaigns(
            auth=auth,
            rule_params=RuleParams.from_create_hail_option_matrix_request(
                create_hail_option_matrix_request
            ),
        )

        option_matrix: HailOptionMatrixV2 | HailFleetOptionMatrixV2

        if create_hail_option_matrix_request.platform_type == HailPlatformType.FLEET:
            option_matrix = await self.option_matrix_service.get_fleet_hail_option_matrix(
                CreateHailOptionMatrixRequest(
                    place_ids=create_hail_option_matrix_request.place_ids,
                    time=create_hail_option_matrix_request.time,
                    language=create_hail_option_matrix_request.language,
                    session_token=create_hail_option_matrix_request.session_token,
                    double_tunnel_fee=create_hail_option_matrix_request.double_tunnel_fee,
                    payment_instrument_type=create_hail_option_matrix_request.payment_instrument_type,
                    applicable_campaigns=applicable_campaigns,
                ),
                auth=auth,
            )
        else:
            option_matrix = await self.option_matrix_service.get_hail_option_matrix_v2(
                CreateHailOptionMatrixRequest(
                    place_ids=create_hail_option_matrix_request.place_ids,
                    time=create_hail_option_matrix_request.time,
                    language=create_hail_option_matrix_request.language,
                    session_token=create_hail_option_matrix_request.session_token,
                    double_tunnel_fee=create_hail_option_matrix_request.double_tunnel_fee,
                    payment_instrument_type=create_hail_option_matrix_request.payment_instrument_type,
                    applicable_campaigns=applicable_campaigns,
                ),
                auth=auth,
            )

        self.logger_service.log(
            "Hail option matrix v2 created",
            extra={"matrix": json.loads(option_matrix.model_dump_json())},
        )
        return option_matrix
