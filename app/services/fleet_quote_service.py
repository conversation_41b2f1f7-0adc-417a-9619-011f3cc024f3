from datetime import datetime
from app.contracts.matrix.create_hail_option_matrix_request import CreateHailOptionMatrixRequest
from app.infrastructure.settings import settings
from app.models.auth import Auth
import httpx
from app.utils.http_client import create_http_client

from app.infrastructure.settings import settings

from app.models.localization_language import LocalizationLanguage
from app.services.logger_service import LoggerService

from app.utils.exceptions.fleet_quote_exception_builder import FleetQuoteExceptionBuilder
from app.contracts.fleetQuote.fleet_quote_response import FleetQuoteResponse

class FleetQuoteService:
    """
    Service to interact with dash fleet quote API
    """

    base_url: str
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.base_url = settings.dash_api_url
        self.logger_service = logger_service

    async def get_fleet_quote(
        self,
        auth: Auth,
        price_ids: list[str],
        time: datetime,
        language: LocalizationLanguage,
    ) -> FleetQuoteResponse:
        async with create_http_client() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/me/fleet-taxi/quotes",
                    headers={
                        "Authorization": f"Bearer {auth.token}",
                    },
                    json={
                        "placeIds": price_ids,
                        "time": time.isoformat(),
                        "language": language.value,
                    }
                )

                response.raise_for_status()
                fleet_quote_response = response.json()
                
                return FleetQuoteResponse.model_validate(fleet_quote_response)
            except httpx.ReadTimeout as e:
                self.logger_service.warning(
                    "Fleet quote service timeout",
                    extra={
                        "warning": str(e),
                    },
                )
                raise FleetQuoteExceptionBuilder().timeout()
            except Exception as e:
                self.logger_service.error(
                    "Fleet service failure",
                    extra={
                        "error": str(e),
                    },
                )
                raise FleetQuoteExceptionBuilder().fleet_service_failure()
