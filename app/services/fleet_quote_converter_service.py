from datetime import datetime
from decimal import Decimal
from typing import List

from app.contracts.campaigns.applicable_discounts import ApplicableDiscounts
from app.contracts.common.pin import Pin
from app.contracts.fleetQuote.fleet_quote_response import FleetQuoteResponse, FleetVehicleClassOption
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetVehicleClassOptionV2
from app.contracts.matrix.hail_option_matrix import HailSubOption
from app.models.discount_rules import DiscountRules
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.persistence.firestore.entities.hail_configuration import HailConfiguration
from app.services.fare_calculation_service import FareCalculationService
from app.services.logger_service import LoggerService


class FleetQuoteConverterService:
    """
    Service to convert FleetQuoteResponse to fleet_vehicle_class_options with price calculations
    """

    fare_calculation_service: FareCalculationService
    logger_service: LoggerService

    def __init__(
        self,
        fare_calculation_service: FareCalculationService,
        logger_service: LoggerService,
    ) -> None:
        self.fare_calculation_service = fare_calculation_service
        self.logger_service = logger_service

    def convert_fleet_quote_to_vehicle_class_options(
        self,
        language: LocalizationLanguage,
        fleet_quote_response: FleetQuoteResponse,
        discount_rules: DiscountRules | None,
        datetime: datetime,
        origin: Pin,
        destination: Pin,
        hail_config: HailConfiguration,
    ) -> List[HailFleetVehicleClassOptionV2]:
        """
        Convert FleetQuoteResponse to HailVehicleClassOptionV2 list with price calculations
        
        Args:
            fleet_quote_response: The fleet quote response to convert
            discount_rules: Discounts to apply to the pricing
            datetime: The datetime for the trip
            origin: Origin location
            destination: Destination location
            hail_config: Hail configuration for pricing rules
            
        Returns:
            List of HailVehicleClassOptionV2 with calculated prices
        """
        self.logger_service.log("Converting fleet quote response to vehicle class options", {
            "fleet_quote_response": fleet_quote_response.model_dump_json(),
            "discount_rules": discount_rules.model_dump_json() if discount_rules else None,
            "datetime": datetime.isoformat(),
            "origin": [origin.lat, origin.lng],
            "destination": [destination.lat, destination.lng],
            "hail_config": hail_config.model_dump_json(),
        })
        
        converted_options: List[HailFleetVehicleClassOptionV2] = []
                
        for fleet_option in fleet_quote_response.vehicle_class_options:
            # Create HailSubOption with fare calculation
            sub_option = self._create_hail_sub_option(
                fleet_option=fleet_option,
                discount_rules=discount_rules,
                hail_config=hail_config,
            )
            
            # Create HailVehicleClassOptionV2
            vehicle_class_option = HailFleetVehicleClassOptionV2(
                vehicle_class_name=fleet_option.vehicle_class_name_tc if language == LocalizationLanguage.ZH_HK else fleet_option.vehicle_class_name,
                vehicle_class_description=fleet_option.vehicle_class_description_tc if language == LocalizationLanguage.ZH_HK else fleet_option.vehicle_class_description,
                fleet_vehicle_type=fleet_option.fleet_vehicle_type,
                fleet_vehicle_id=fleet_option.fleet_vehicle_id,
                fleet_partner_key=fleet_option.fleet_partner_key,
                seats_count=fleet_option.seats_count,
                luggage_count=fleet_option.luggage_count,
                sub_options=[sub_option],
                vehicle_icon_url=fleet_option.vehicle_icon_url,
                fleet_icon_url=fleet_option.fleet_icon_url,
                is_include_wheel_chair=fleet_option.is_include_wheel_chair,
            )
            
            converted_options.append(vehicle_class_option)
            
        self.logger_service.log(f"Converted {len(converted_options)} vehicle class options")
        return converted_options

    def _create_hail_sub_option(
        self,
        fleet_option: FleetVehicleClassOption,
        discount_rules: DiscountRules | None,
        hail_config: HailConfiguration,
    ) -> HailSubOption:
        """
        Create a HailSubOption with fare calculation based on fleet option base_fare
        """
        
        self.logger_service.log(f"Creating hail sub option for fleet option: {fleet_option.model_dump_json()}")
        
        dash_transaction_fees = hail_config.dash_fee_configuration.dash_fees.get(
            MeterOperatingArea.URBAN, hail_config.dash_fee_configuration.common
        )

        # Calculate fare using the base_fare from fleet option
        fare_calculation = self.fare_calculation_service.calculate_fare_for_fleet_quote(
            fare=float(fleet_option.base_fare),
            hail_config=hail_config,
            discount_rules=discount_rules,
            fleet_booking_fee_markup=0,  # No fleet markup for fleet quote response
            dash_transaction_fees=dash_transaction_fees,
        )
        
        # Create HailSubOption with calculated fare
        return HailSubOption(
            fleet_id=None,
            fleet_name=None,
            **fare_calculation.model_dump(),
        )
