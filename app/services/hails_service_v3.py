import json
from datetime import datetime

from dependency_injector.wiring import inject
from fastapi import BackgroundTasks
from app.contracts.common.pin import Pin
from app.contracts.common.trip_estimation import TripEstimation
from app.contracts.hails.create_hail_request_v3 import CreateHailRequestV3
from app.contracts.hails.hail_response import HailResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.models.auth import Auth
from app.models.discount_rules import DiscountRules
from app.models.meter_operating_area import MeterOperatingArea
from app.persistence.firestore.repositories.configurations_repository import ConfigurationsRepository
from app.persistence.firestore.repositories.firestore_repository import FirestoreRepository
from app.persistence.redis.entities.hail import Hail
from app.persistence.redis.redis import RedisService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.services.fare_estimation_service import FareEstimationService
from app.services.fleet_fare_service import FleetFareService
from app.services.geo_spacial_service import GeoSpatialService
from app.services.hail_quote_service import HailQuoteService
from app.services.hails_service import HailsService
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.services.routing_service import RoutingService
from app.services.standard_fare_service import StandardFareService
from app.services.pub_sub_publisher_service import PubSubPublisherService
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder


class HailsV3Service:
    logger_service: LoggerService
    standard_fare_service: StandardFareService
    fleet_fare_service: FleetFareService
    driver_repository: DriverRepository
    configuration_repository: ConfigurationsRepository
    location_service: LocationService
    firestore_repository: FirestoreRepository
    pub_sub_publisher_service: PubSubPublisherService
    hails_service: HailsService
    fare_estimation_service: FareEstimationService
    geo_spacial_service: GeoSpatialService
    redis_service: RedisService
    hail_quote_service: HailQuoteService
    hail_repository: HailRepository
    routing_service: RoutingService

    @inject
    def __init__(
        self,
        logger_service: LoggerService,
        standard_fare_service: StandardFareService,
        fleet_fare_service: FleetFareService,
        driver_repository: DriverRepository,
        configuration_repository: ConfigurationsRepository,
        location_service: LocationService,
        firestore_repository: FirestoreRepository,
        pub_sub_publisher_service: PubSubPublisherService,
        hails_service: HailsService,
        fare_estimation_service: FareEstimationService,
        geo_spacial_service: GeoSpatialService,
        redis_service: RedisService,
        hail_quote_service: HailQuoteService,
        hail_repository: HailRepository,
        routing_service: RoutingService,
    ) -> None:
        self.logger_service = logger_service
        self.standard_fare_service = standard_fare_service
        self.fleet_fare_service = fleet_fare_service
        self.driver_repository = driver_repository
        self.configuration_repository = configuration_repository
        self.location_service = location_service
        self.firestore_repository = firestore_repository
        self.pub_sub_publisher_service = pub_sub_publisher_service
        self.hails_service = hails_service
        self.fare_estimation_service = fare_estimation_service
        self.geo_spacial_service = geo_spacial_service
        self.redis_service = redis_service
        self.hail_quote_service = hail_quote_service
        self.hail_repository = hail_repository
        self.routing_service = routing_service

    async def create_hail_request(
        self,
        auth: Auth,
        request: CreateHailRequestV3,
        background_tasks: BackgroundTasks,
    ) -> HailResponse:
        self.logger_service.log(
            f"Creating hail request for user {auth.user_id} with request: {request}"
        )

        if request.trip_estimation.encoded_polyline:
            tunnel_fare_estimation = self.fare_estimation_service.tunnel_fare_estimation(
                request.trip_estimation.encoded_polyline,
                await self.configuration_repository.get_hail_configuration(),
            )
        else:
            tunnel_fare_estimation = []

        if request.platform_type == "FLEET":
            hail = await self.create_fleet_hail_request(
                auth=auth,
                request=request,
                user_id=auth.user_id,
            )
        else:
            hail = await self.create_dash_hail_request(
                auth=auth,
                request=request,
                user_id=auth.user_id,
                tunnel_fare_estimation=tunnel_fare_estimation,
            )

        await self.after_hail_create(hail, request, background_tasks, user_id=auth.user_id)

        return hail.to_hail_response()

    async def create_dash_hail_request(
        self,
        auth: Auth,
        request: CreateHailRequestV3,
        user_id: str,
        tunnel_fare_estimation: list[TunnelFee],
    ) -> Hail:
        place_ids = [itinerary.place_id for itinerary in request.itinerary]

        available_operating_areas: list[MeterOperatingArea] = (
            self.geo_spacial_service.operating_area(
                origin=Pin(
                    lat=request.itinerary[0].lat,
                    lng=request.itinerary[0].lng,
                ),
                destination=Pin(
                    lat=request.itinerary[-1].lat,
                    lng=request.itinerary[-1].lng,
                ),
            )
            or []
        )

        min_max_fare_calculations = await self.standard_fare_service.get_min_max_fare_calculation_by_vehicle_classes(
            auth=auth,
            place_ids=place_ids,
            language=request.language,
            price_rules=request.price_rules,
            is_double_tunnel_fee=request.is_double_tunnel_fee,
            vehicle_classes=request.prefer_vehicle_classes,
            discount_rules=request.applicable_discounts.to_discount_rules()
        )

        hail = await Hail.from_create_hail_request_v3(
            create_hail_request=request,
            user_id=user_id,
            phone_number=auth.phone_number,
            min_max_fare_calculations=min_max_fare_calculations,
            tunnel_fare_estimation=tunnel_fare_estimation,
            available_operating_areas=available_operating_areas,
        )

        await self.redis_service.deleteKey(
            self.hail_quote_service.generate_hail_cache_key(auth, request)
        )

        return hail

    async def create_fleet_hail_request(
        self,
        auth: Auth,
        request: CreateHailRequestV3,
        user_id: str,
    ) -> Hail:
        place_ids = [itinerary.place_id for itinerary in request.itinerary]

        min_max_fare_calculations = await self.fleet_fare_service.get_min_max_fare_calculation_by_fleet_vehicle_type(
            auth=auth,
            place_ids=place_ids,
            language=request.language,
            request_time=request.time,
            price_rules=request.price_rules,
            fleet_vehicle_type=request.fleet_vehicle_type_key if request.fleet_vehicle_type_key else "",
            discount_rules=request.applicable_discounts.to_discount_rules(),
        )

        hail = await Hail.from_create_hail_request_v3(
            create_hail_request=request,
            user_id=user_id,
            phone_number=auth.phone_number,
            min_max_fare_calculations=min_max_fare_calculations,
            tunnel_fare_estimation=[],
            available_operating_areas=[MeterOperatingArea.URBAN],
        )

        return hail

    async def after_hail_create(self, hail: Hail, request: CreateHailRequestV3, background_tasks: BackgroundTasks, user_id: str) -> None:
        now = DateTimeUtils.utc_now_with_tz()

        hail_config = await self.configuration_repository.get_hail_configuration()
        filtered_driver_count_by_radius = (
            await self.driver_repository.get_driver_count_by_radius(
                hail,
                *hail_config.driver_search.km_radii_for_online_drivers_search,
            )
        )
        await hail.set_driver_count_by_radius(filtered_driver_count_by_radius)

        favorite_drivers_online_ids: set[str] | None = None
        if request.prioritize_favorite_drivers:
            favorite_driver_ids = (
                await self.firestore_repository.user_favorite_driver_repository(
                    user_id
                ).get_user_favorite_drivers()
            )

            if len(favorite_driver_ids) > 0:
                favorite_drivers_online_ids = (
                    await self.hails_service.get_favorite_drivers_online_for_hail(
                        hail, favorite_driver_ids
                    )
                )
                favorite_drivers_online = (
                    await self.driver_repository.get_drivers_by_ids(
                        favorite_drivers_online_ids
                    )
                )
                await hail.set_favorite_drivers_online(favorite_drivers_online_ids)

                await self.driver_repository.set_drivers_for_hail(
                    hail, favorite_drivers_online, timestamp=now,
                )
            else:
                await hail.set_favorite_drivers_online(set())

        num_favorite_drivers_online = (
            len(favorite_drivers_online_ids)
            if favorite_drivers_online_ids is not None
            else 0
        )
        if hail.is_scheduled and hail.is_dash_hail:
            background_tasks.add_task(
                self.hails_service.find_matching_drivers_for_scheduled_hail,
                hail,
                num_favorite_drivers_online,
                timestamp=now,
            )
        elif hail.is_live and hail.is_dash_hail:
            background_tasks.add_task(
                self.hails_service.find_matching_drivers_for_live_hail,
                hail,
                num_favorite_drivers_online
            )

        self.logger_service.log(
            f"HAIL CREATED: {hail.id} requested by {hail.user_id}",
            {"hail": json.loads(hail.model_dump_json())},
        )

        self.pub_sub_publisher_service.publish_hail_bronze_topic(
            hail.to_hail_proto())

    async def get_hail_by_riders(
        self,
        hail_id: str,
    ) -> HailResponse | None:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            return None
        driver = await self.driver_repository.get_driver_by_id(
            hail.matched_driver.driver_id
        ) if hail.matched_driver is not None else None

        eta = None
        destination = hail.itinerary[1] if hail.is_on_going else hail.itinerary[0]

        if driver is not None and driver.heart_beat is not None:
            eta = await self.routing_service.get_driver_eta(
                driver_location=driver.heart_beat,
                destination=destination,
            )

        return hail.to_hail_response(eta, driver.heart_beat if driver is not None else None) if hail is not None else None
