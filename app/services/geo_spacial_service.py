import json
import re
import os
import tempfile
from typing import Any, Dict, Optional, Callable
from fastkml import Document, Placemark, kml
from shapely import LineString, Polygon
from shapely.geometry import Point, shape
import polyline

from app.contracts.common.pin import Pin
from app.models.geo_spacial_data import GeoSpatialData
from app.models.meter_operating_area import MeterOperatingArea
from app.models.polygon_feature import PolygonFeature
from app.services.firebase_service import FirebaseService
from app.services.logger_service import LoggerService


class GeoSpatialService:
    """
    Handles spatial queries using polygon-based KML features.
    """

    lantau_data: GeoSpatialData
    tunnel_data: GeoSpatialData
    firebase_service: FirebaseService
    logger_service: LoggerService

    def __init__(
        self, firebase_service: FirebaseService, logger_service: LoggerService
    ):
        self.firebase_service = firebase_service
        self.logger_service = logger_service

        self.__initialize_kml_data()

    # def find_pickup_points(self, pin: Pin) -> Optional[List[Dict]]:
    #     """
    #     Find all pickup points inside any polygon that contains the given point (lat, lng).
    #     :return: A list of pickup points, or None if none found.
    #     """
    #     point = Point(pin.lng, pin.lat)
    #     candidates = self.pickup_data.query(point.bounds)
    #     results: List[Dict] = []

    #     for idx in candidates:
    #         feature = self.pickup_data.features[idx]
    #         if feature.geometry.contains(point) and feature.pickup_points:
    #             results.extend(feature.pickup_points)

    #     return results if results else None

    def get_tunnels_crossed(self, route: str) -> list[str]:
        """
        Check if the given route intersects any polygon in the tunnels KML.
        :return: A list of tunnel (polygon) names if route intersects them.
        """
        polyline_in_geojson = polyline.decode(route, geojson=True)
        route_in_line_string = LineString(polyline_in_geojson)

        candidates = self.tunnel_data.query(route_in_line_string.bounds)
        tunnel_names: list[str] = []

        for idx in candidates:
            feature = self.tunnel_data.features[idx]
            if feature.geometry.intersects(route_in_line_string):
                if feature.name:
                    tunnel_names.append(feature.name)
        return tunnel_names

    def operating_area(
        self, origin: Pin, destination: Pin
    ) -> Optional[list[MeterOperatingArea]]:
        """
        Check if the origin and destination points are in the same operating area.
        :return: The name of the operating area if both points are in the same area, otherwise None.
        """
        origin_point = Point(origin.lng, origin.lat)
        dest_point = Point(destination.lng, destination.lat)

        originFeature = self.operating_areas_data.find_polygon_containing_point(
            origin_point
        )

        destFeature = self.operating_areas_data.find_polygon_containing_point(
            dest_point
        )

        # get area from extra_data, then compare the two lists
        # e.g.
        # origin = {"areas": ["Urban", "NT"]}; dest = {"areas": ["NT"]} => return ["NT"]
        # origin = {"areas": ["Urban", "NT"]}; dest = {"areas": ["Urban", "NT"]} => return ["Urban", "NT"]
        common = []
        if originFeature and destFeature:
            origin_areas_str: list[str] = (
                json.loads(originFeature.description).get("areas", [])
                if originFeature.description
                else []
            )
            dest_areas_str: list[str] = (
                json.loads(destFeature.description).get("areas", [])
                if destFeature.description
                else []
            )

            self.logger_service.log(
                "Origin and destination areas found",
                {
                    "origin": origin_areas_str,
                    "destination": dest_areas_str,
                },
            )

            origin_areas = [MeterOperatingArea[area]
                            for area in origin_areas_str]
            dest_areas = [MeterOperatingArea[area] for area in dest_areas_str]

            def is_valid_area_list(area: Any) -> bool:
                return isinstance(area, list) and all(
                    isinstance(item, str) for item in area
                )

            if not is_valid_area_list(origin_areas):
                self.logger_service.error(
                    f"Invalid 'areas' field in origin feature: {origin_areas}"
                )
                origin_areas = []

            if not is_valid_area_list(dest_areas):
                self.logger_service.error(
                    f"Invalid 'areas' field in destination feature: {dest_areas}"
                )
                dest_areas = []

            common = list(set(origin_areas) & set(dest_areas))
        else:
            self.logger_service.warning(
                f"Operating area not found for origin and destination",
            )

        self.logger_service.debug(
            f"Common operating areas: {[ area.value for area in common]}"
        )
        return sorted(common)

    def find_lantau_area_by_pin(self, point: Pin) -> Optional[str]:
        """
        Check if the given point is in the Lantau area.
        :return: The name of the Lantau area if the point is in the area, otherwise None.
        """
        point_to_search = Point(point.lng, point.lat)
        feature = self.lantau_data.find_polygon_containing_point(
            point_to_search)
        if feature:
            return feature.extra_data.get("area_category", None)
        return None

    def __initialize_kml_data(self) -> None:
        self.logger_service.log("Initializing GeoSpatialService KML data")

        dir = tempfile.gettempdir()
        if not os.path.exists(os.path.join(dir, "geo_spacial")):
            os.mkdir(os.path.join(dir, "geo_spacial"))

        lantau_kml_file_blob = "geo_spacial/lantau.kml"
        tunnel_kml_file_blob = "geo_spacial/tunnels.kml"
        operating_areas_kml_file_blob = "geo_spacial/operating_areas.kml"

        lantau_kml_file = os.path.join(dir, lantau_kml_file_blob)
        tunnel_kml_file = os.path.join(dir, tunnel_kml_file_blob)
        operating_areas_kml_file = os.path.join(
            dir, operating_areas_kml_file_blob)

        self.logger_service.debug(
            f"Downloading KML files from Firebase to {dir}",
        )

        self.firebase_service.bucket.blob(lantau_kml_file_blob).download_to_filename(
            lantau_kml_file
        )
        self.firebase_service.bucket.blob(tunnel_kml_file_blob).download_to_filename(
            tunnel_kml_file
        )
        self.firebase_service.bucket.blob(
            operating_areas_kml_file_blob
        ).download_to_filename(operating_areas_kml_file)

        self.lantau_data = self.__load_kml_data(
            lantau_kml_file, self.__parse_lantau_description
        )
        self.tunnel_data = self.__load_kml_data(tunnel_kml_file)
        self.operating_areas_data = self.__load_kml_data(
            operating_areas_kml_file)

        self.lantau_data.build_rtree_index()
        self.tunnel_data.build_rtree_index()
        self.operating_areas_data.build_rtree_index()

    def __load_kml_data(
        self,
        file_path: str,
        parse_description_cb: Optional[Callable[[str], Dict[str, str]]] = None,
    ) -> GeoSpatialData:
        """
        Load and parse the KML from disk using `fastkml.KML.parse()`,
        then build a list of `PolygonFeature` from the placemarks.
        """
        k = kml.KML.parse(file_path)

        features: list[PolygonFeature] = []

        for feature in k.features:
            if not isinstance(feature, Document):
                continue

            for i, placeMark in enumerate(feature.features):
                if not isinstance(placeMark, Placemark):
                    continue

                geom = placeMark.geometry

                if not geom:
                    continue

                extra_data_obj = (
                    parse_description_cb(placeMark.description)
                    if parse_description_cb and placeMark.description
                    else {}
                )

                polygon_feature = PolygonFeature(
                    name=placeMark.name,
                    description=placeMark.description,
                    geometry=shape(geom),  # type: ignore
                    extra_data=extra_data_obj,
                )

                self.logger_service.debug(
                    f"Loaded polygon {i} {polygon_feature.name} with extra data: {polygon_feature.extra_data}",
                    extra={
                        "geometry": polygon_feature.geometry.wkt,
                    }
                )

                features.append(polygon_feature)

        return GeoSpatialData(features=features, logger_service=self.logger_service)

    def __get_all_tunnel_polygons(self) -> list[Polygon]:
        """
        Return all polygons from the tunnels KML.
        """
        return [f.geometry for f in self.tunnel_data.features]

    def __parse_lantau_description(self, description: Optional[str]) -> Dict[str, str]:
        """
        If the KML has a JSON object specifying area_category tags, store them in 'area_category'.
        e.g. description might be:
        {
        "area_category": "Lantau"
        }
        """

        if not description:
            return {}
        cleaned = re.sub(r"<br>\s*", "", description, flags=re.IGNORECASE)
        try:
            obj = json.loads(cleaned)
            return {str(k): str(v) for k, v in obj.items()}
        except json.JSONDecodeError:
            self.logger_service.error(
                f"Failed to parse Lantau area description: {description}"
            )
            return {}

        # def __parse_pickup_description(self, description: Optional[str]) -> Dict:

    #     """If the KML has pickup info in JSON array format, store it as 'pickup_points'."""
    #     if not description:
    #         return {}
    #     # Example: <![CDATA[[{"location":"A","lat":22.3,"lng":114.2}]]>
    #     cleaned = re.sub(r"<br>\s*", "", description, flags=re.IGNORECASE)
    #     match = re.search(r"\[(\s*{.*?}\s*)\]", cleaned, re.DOTALL)
    #     if match:
    #         try:
    #             pickup_list = json.loads(match.group(0))
    #             return {"pickup_points": pickup_list}
    #         except json.JSONDecodeError:
    #             pass
    #     return {}

    # def __parse_operating_area_description(self, description: Optional[str]) -> Dict:
    #     """If the KML has a JSON object specifying area tags, store them in 'areas'."""
    #     # e.g. description might be:
    #     # {
    #     #   "areas": ["Urban", "NT"]
    #     # }
    #     if not description:
    #         return {}
    #     cleaned = re.sub(r"<br>\s*", "", description, flags=re.IGNORECASE)
    #     try:
    #         obj = json.loads(cleaned)
    #         self.logger_service.debug(
    #             f"Operating area description: {obj.get('areas', [])}"
    #         )
    #         # Expecting something like {"areas": ["Urban", "NT"]}
    #         return obj
    #     except json.JSONDecodeError:
    #         self.logger_service.error(
    #             f"Failed to parse operating area description: {description}"
    #         )
    #         return {}
