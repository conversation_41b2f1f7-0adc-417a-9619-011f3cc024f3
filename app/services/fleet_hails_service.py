from app.contracts.common.hail_charges import HailCharges
from app.contracts.hails.fleet_hail_update_request import FleetHailUpdateRequest
from app.contracts.hails.hail_cancelled_response import HailCancelledResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.contracts.pub_sub.publish_push_notification import PublishPushNotification
from app.contracts.tx.hail_order_request_timeout import HailOrderRequestTimeout
from app.models.auth import Auth
from app.models.hail_status import HailStatus
from app.models.notification_recipient_type import NotificationRecipientType
from app.models.notification_trigger_event_type import NotificationTriggerEventType
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.firestore.repositories.meter_repository import MeterRepository
from app.services.web_socket_message_service import WebSocketMessageService
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.services.fare_calculation_service import FareCalculationService

from app.services.logger_service import LoggerService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.services.pub_sub_publisher_service import PubSubPublisherService
from fastapi import BackgroundTasks
from app.sockets.messages.hail_accepted_message import HailAcceptedMessage
from app.sockets.messages.hail_approaching_rider_message import HailApproachingRiderMessage
from app.sockets.messages.hail_cancelled_message import HailCancelledMessage
from app.sockets.messages.hail_on_going_rider_message import HailOnGoingRiderMessage
from app.sockets.messages.hail_pending_rider_message import HailPendingRiderMessage
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from fastapi_controllers import Controller
from dependency_injector.wiring import inject


from app.contracts.hails.fleet_hail_update_request import FleetHailUpdateRequest
from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder


class FleetHailsService(Controller):
    hail_repository: HailRepository
    driver_repository: DriverRepository
    configuration_repository: ConfigurationsRepository
    logger_service: LoggerService
    fare_calculation_service: FareCalculationService
    meter_repository: MeterRepository
    pub_sub_publisher_service: PubSubPublisherService
    web_socket_message_service: WebSocketMessageService

    @inject
    def __init__(
        self,
        hail_repository: HailRepository,
        driver_repository: DriverRepository,
        configuration_repository: ConfigurationsRepository,
        logger_service: LoggerService,
        fare_calculation_service: FareCalculationService,
        meter_repository: MeterRepository,
        pub_sub_publisher_service: PubSubPublisherService,
        web_socket_message_service: WebSocketMessageService,
    ) -> None:
        self.hail_repository = hail_repository
        self.driver_repository = driver_repository
        self.configuration_repository = configuration_repository
        self.meter_repository = meter_repository
        self.logger_service = logger_service
        self.fare_calculation_service = fare_calculation_service
        self.pub_sub_publisher_service = pub_sub_publisher_service
        self.web_socket_message_service = web_socket_message_service

    async def update_hail_by_status(
        self,
        hail_id: str,
        fleet_hail_update_request: FleetHailUpdateRequest,
    ) -> HailRiderResponse:
        now = DateTimeUtils.utc_now_with_tz()
        self.logger_service.log(
            f"Fleet updating hail {hail_id}", fleet_hail_update_request.model_dump())
        meter = await self.meter_repository.get_meter_by_id(
            fleet_hail_update_request.meter_id
        )

        if meter is None:
            raise MeterExceptionBuilder().meter_not_found(
                meter_id=fleet_hail_update_request.meter_id
            )

        if fleet_hail_update_request.driver_phone_number is not None:
            driver = await self.driver_repository.upsert_driver_by_id(
                fleet_hail_update_request,
                meter,
            )
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        hail_config = await self.configuration_repository.get_hail_configuration()

        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)

        match fleet_hail_update_request.status:
            case HailStatus.PENDING:
                await hail.set_to_pending()
                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailPendingRiderMessage(
                        payload=hail.to_hail_rider_response()),
                )
            case HailStatus.ACCEPTED:
                await hail.set_to_accepted(
                    driver, hail_config, self.fare_calculation_service
                )
                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailAcceptedMessage(
                        payload=hail.to_hail_rider_response()),
                )
            case HailStatus.APPROACHING:
                if not hail.is_accepted:
                    await hail.set_to_accepted(
                        driver, hail_config, self.fare_calculation_service
                    )
                    await self.web_socket_message_service.trigger_message(
                        {hail.id},
                        HailAcceptedMessage(
                            payload=hail.to_hail_rider_response()),
                    )

                await hail.set_to_approaching(self.fare_calculation_service, driver=driver)
                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailApproachingRiderMessage(
                        payload=hail.to_hail_rider_response()
                    ),
                )
            case HailStatus.ARRIVED:
                if not hail.is_approaching:
                    await hail.set_to_accepted(
                        driver, hail_config, self.fare_calculation_service
                    )
                    await hail.set_to_approaching(self.fare_calculation_service, driver=driver)
                    await self.web_socket_message_service.trigger_message(
                        {hail.id},
                        HailApproachingRiderMessage(
                            payload=hail.to_hail_rider_response()
                        ),
                    )
                await hail.set_to_arrived()

            case HailStatus.ON_GOING:
                trip_id = await self.meter_repository.pair_hail_with_fleet_meter_trip(hail, fleet_hail_update_request.meter_id)
                await hail.set_to_on_going(trip_id)
                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailOnGoingRiderMessage(
                        payload=hail.to_hail_rider_response()),
                )
            case HailStatus.COMPLETED:
                if not hail.is_on_going:
                    await hail.set_to_accepted(
                        driver, hail_config, self.fare_calculation_service
                    )
                    trip_id = await self.meter_repository.pair_hail_with_fleet_meter_trip(hail, fleet_hail_update_request.meter_id)
                    await hail.set_to_on_going(trip_id)
                    await self.web_socket_message_service.trigger_message(
                        {hail.id},
                        HailOnGoingRiderMessage(
                            payload=hail.to_hail_rider_response()),
                    )
                await hail.set_to_completed()
            case HailStatus.TIMED_OUT:
                await hail.set_to_timed_out()
                self.pub_sub_publisher_service.publish_push_notification(
                    PublishPushNotification(
                        event=NotificationTriggerEventType.P11_ORDER_TIME_OUT,
                        recipient_type=NotificationRecipientType.RIDER,
                        recipient_id=hail.user_id,
                    )
                )
            case HailStatus.CANCELLED:
                await hail.set_to_cancelled(
                    HailCharges(
                        cancellation_fee=hail_config.fleet_cancellation_fee)
                )

                rider_response = HailCancelledResponse(
                    hail_id=hail.id,
                    cancellation_fee=(
                        hail.charges.cancellation_fee if hail.charges is not None else 0
                    ),
                    cancellation_fee_breakdown=(
                        hail.charges.cancellation_fee_breakdown if hail.charges is not None else None
                    ),
                )

                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailCancelledMessage(
                        payload=rider_response, timestamp=now),
                )

                await self.cancel_hail_push_notification(hail_id)
            case _:
                raise HailExceptionBuilder().hail_not_updatable(
                    hail_status=hail.status, hail_id=hail_id
                )

        self.logger_service.log(f"Hail {hail.id} updated by fleet")

        return hail.to_hail_rider_response()

    async def cancel_hail_push_notification(self, hail_id: str) -> None:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)
        if hail.matched_driver is None:
            return

        if hail.is_scheduled and hail.is_within_approaching_threshold:
            self.pub_sub_publisher_service.publish_push_notification(
                PublishPushNotification(
                    event=NotificationTriggerEventType.P10_ACCEPTED_ORDER_CANCELLED_BY_DRIVER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                    recipient_type=NotificationRecipientType.RIDER,
                    recipient_id=hail.user_id,
                )
            )
        else:
            self.pub_sub_publisher_service.publish_push_notification(
                PublishPushNotification(
                    event=NotificationTriggerEventType.P6_ACCEPTED_ORDER_CANCELLED_BY_RIDER_GREATER_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                    recipient_type=NotificationRecipientType.RIDER,
                    recipient_id=hail.user_id,
                )
            )

    async def cancel_hail_by_fleet(
        self,
        auth: Auth,
        hail_id: str,
    ) -> HailRiderResponse:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)

        if not hail.can_be_updated:
            raise HailExceptionBuilder().hail_not_updatable(
                hail_status=hail.status, hail_id=hail_id
            )

        await self.hail_repository.set_hail_to_cancelled(
            hail,
            auth,
        )

        await self.cancel_hail_push_notification(hail_id)

        self.logger_service.log(f"Hail {hail.id} cancelled by fleet")

        return hail.to_hail_rider_response()
