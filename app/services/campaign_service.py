from app.contracts.campaigns.applicable_campaigns import ApplicableCampaigns
from app.contracts.campaigns.rule_params import RuleParams
from app.infrastructure.settings import settings
from app.models.auth import Auth
import httpx

from app.infrastructure.settings import settings
from app.utils.http_client import create_http_client

from app.services.logger_service import LoggerService
from app.utils.exceptions.campaign_exception_builder import CampaignExceptionBuilder


class CampaignService:
    """
    Service to interact with dash campaign API
    """

    base_url: str
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.base_url = settings.dash_api_url
        self.logger_service = logger_service

    async def get_applicable_campaigns(
        self,
        auth: Auth,
        rule_params: RuleParams,
    ) -> ApplicableCampaigns:
        async with create_http_client() as client:
            try:
                campaign_query = {
                    "params": rule_params.model_dump(by_alias=True),
                }

                self.logger_service.debug(
                    f"campaign query", campaign_query)

                response = await client.post(
                    f"{self.base_url}/me/campaigns/query-applicable",
                    headers={
                        "Authorization": f"Bearer {auth.token}",
                    },
                    json=campaign_query,
                )

                response.raise_for_status()
                applicable_campaigns_response = response.json()
                return ApplicableCampaigns.model_validate(applicable_campaigns_response)
            except httpx.ReadTimeout as e:
                self.logger_service.warning(
                    "Campaign service timeout",
                    extra={
                        "warning": str(e),
                    },
                )
                raise CampaignExceptionBuilder().timeout()
            except Exception as e:
                self.logger_service.error(
                    "Campaign service failure",
                    extra={
                        "error": str(e),
                    },
                )
                raise CampaignExceptionBuilder().campaign_service_failure()
