from datetime import datetime
import json
from typing import Any
from app.contracts.common.dash_transaction_fees import DashTransactionFees
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.common.pin import Pin
from app.models.discount_rules import DiscountRules
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.entities.hail_configuration import (
    DATE_STRING_FORMAT,
    HailConfiguration,
)
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.services.geo_spacial_service import GeoSpatialService
from app.services.logger_service import LoggerService
from app.utils.conversion_utils import ConversionUtils
from app.utils.datetime_utils import DateTimeUtils
from json_logic import jsonLogic


class FareCalculationService:
    """
    Calculates fare breakdown
    """

    configuration_repository: ConfigurationsRepository
    geo_spacial_service: GeoSpatialService
    logger_service: LoggerService

    def __init__(
        self,
        geo_spacial_service: GeoSpatialService,
        logger_service: LoggerService,
        configuration_repository: ConfigurationsRepository,
    ):
        self.geo_spacial_service = geo_spacial_service
        self.logger_service = logger_service
        self.configuration_repository = configuration_repository

    def calculate_fare(
        self,
        fare: float,
        tunnel_fee: float,
        datetime: datetime,
        origin: Pin,
        destination: Pin,
        hail_config: HailConfiguration,
        vehicle_class: VehicleClass,
        discount_rules: DiscountRules | None = None,
        fleet_booking_fee_markup: str | float | None = None,
        dash_transaction_fees: DashTransactionFees | None = None,
        operating_area: MeterOperatingArea | None = None,
        boost_amount: float = 0.0,
    ) -> FareCalculation:
        self.logger_service.debug(
            "Calculating fare",
            json.loads(
                json.dumps(
                    {
                        "fare": fare,
                        "tunnel_fee": tunnel_fee,
                        "datetime": datetime.isoformat(),
                        "origin": [origin.lat, origin.lng],
                        "destination": [destination.lat, destination.lng],
                        "fleet_booking_fee_markup": fleet_booking_fee_markup,
                        "dash_transaction_fees": dash_transaction_fees,
                        "operating_area": operating_area,
                        "vehicle_class": vehicle_class,
                        "discount_rules": (
                            discount_rules.model_dump()
                            if discount_rules is not None
                            else None
                        ),
                    }
                )
            ),
        )

        origin_area = self.geo_spacial_service.find_lantau_area_by_pin(origin)
        destination_area = self.geo_spacial_service.find_lantau_area_by_pin(destination)

        self.logger_service.debug(
            f"Origin and destination areas",
            {
                "origin_area": origin_area,
                "destination_area": destination_area,
            },
        )

        if dash_transaction_fees is None:
            if operating_area is None:
                dash_transaction_fees = hail_config.dash_fee_configuration.common
            else:
                dash_transaction_fees = (
                    hail_config.dash_fee_configuration.dash_fees.get(
                        operating_area, hail_config.dash_fee_configuration.common
                    )
                )
        self.logger_service.debug(f"Dash transaction fees: {dash_transaction_fees}")

        additional_booking_fee_markup_logic: str | float
        if operating_area == MeterOperatingArea.LANTAU:
            self.logger_service.debug(f"Additional booking fee markup used: Lantau")
            additional_booking_fee_markup_logic = (
                json.loads(hail_config.lantau_additional_booking_fee)
                if isinstance(hail_config.lantau_additional_booking_fee, str)
                else hail_config.lantau_additional_booking_fee
            )
        else:
            self.logger_service.debug(f"Additional booking fee markup used: Common")
            additional_booking_fee_markup_logic = (
                json.loads(hail_config.additional_booking_fee)
                if isinstance(hail_config.additional_booking_fee, str)
                else hail_config.additional_booking_fee
            )

        fleet_booking_fee_markup_logic = (
            (
                json.loads(fleet_booking_fee_markup)
                if isinstance(fleet_booking_fee_markup, str)
                else fleet_booking_fee_markup
            )
            if fleet_booking_fee_markup is not None
            else 0
        )

        dash_booking_fee = float(hail_config.dash_booking_fee)
        sub_total = {
            "+": [
                fare,
                tunnel_fee,
                fleet_booking_fee_markup_logic,
                dash_booking_fee,
                additional_booking_fee_markup_logic,
                {"var": "boost_amount"}, 
            ]
        }
        dash_transaction_fee = {
            "+": [
                {"*": [{"var": "dash_fee_rate"}, sub_total]},
                {"var": "dash_fee_constant"},
            ]
        }
        total = {"+": [sub_total, dash_transaction_fee]}

        third_party_discount_rules = (
            json.loads(discount_rules.discount_rules_third_party)
            if discount_rules
            and isinstance(discount_rules.discount_rules_third_party, str)
            else 0
        )
        dash_discount_rules = (
            json.loads(discount_rules.discount_rules_dash)
            if discount_rules and isinstance(discount_rules.discount_rules_dash, str)
            else 0
        )
        discount = {
            "+": [
                third_party_discount_rules,
                dash_discount_rules,
            ]
        }

        total_discount = {"min": [total, {"*": [discount, -1]}]}
        discounted_total = {
            "max": [
                0,
                {"+": [total, discount]},
            ]
        }

        priceEngine: Any = {
            "if": [
                True,
                [
                    fare,
                    tunnel_fee,
                    dash_booking_fee,
                    fleet_booking_fee_markup_logic,
                    additional_booking_fee_markup_logic,
                    {"var": "boost_amount"},
                    sub_total,
                    dash_transaction_fee,
                    total,
                    total_discount,
                    discounted_total,
                ],
            ]
        }

        hk_datetime = DateTimeUtils.set_to_hk_tz(datetime)

        inputValues = {
            "fare": fare,
            "time_hour": hk_datetime.hour,
            "date": hk_datetime.strftime(DATE_STRING_FORMAT),
            "day_of_week": hk_datetime.weekday(),
            "public_holidays": hail_config.public_holidays,
            "dash_fee_rate": dash_transaction_fees.dash_fee_rate,
            "dash_fee_constant": dash_transaction_fees.dash_fee_constant,
            "origin": origin_area,
            "destination": destination_area,
            "vehicle_class": vehicle_class.value,
            "boost_amount": boost_amount,
        }

        results: Any = jsonLogic(priceEngine, inputValues)

        self.logger_service.debug(f"Price engine results: {results}")
        return FareCalculation(
            estimated_fare_fee=ConversionUtils.float_to_decimal(results[0]),
            estimated_tunnel_fee=ConversionUtils.float_to_decimal(results[1]),
            dash_booking_fee=ConversionUtils.float_to_decimal(results[2]),
            fleet_booking_fee=ConversionUtils.float_to_decimal(results[3]),
            additional_booking_fee=ConversionUtils.float_to_decimal(results[4]),
            boost_amount=ConversionUtils.float_to_decimal(results[5]),
            sub_total=ConversionUtils.float_to_decimal(results[6]),
            dash_transaction_fee=ConversionUtils.float_to_decimal(results[7]),
            total=ConversionUtils.float_to_decimal(results[8]),
            discount=ConversionUtils.float_to_decimal(results[9]),
            discounted_total=ConversionUtils.float_to_decimal(results[10]),
            transaction_fees=dash_transaction_fees,
        )

    def calculate_fare_for_fleet_quote(
        self,
        fare: float,
        hail_config: HailConfiguration,
        fleet_booking_fee_markup: str | float | None = None,
        discount_rules: DiscountRules | None = None,
        dash_transaction_fees: DashTransactionFees | None = None,
    ) -> FareCalculation:
        """
        Simplified fare calculation based only on fare, fleet_booking_fee_markup and discount_rules.
        Uses default values for other parameters.
        """
        self.logger_service.debug(
            "Calculating simplified fare",
            {
                "fare": fare,
                "fleet_booking_fee_markup": fleet_booking_fee_markup,
                "discount_rules": discount_rules.model_dump() if discount_rules is not None else None,
            },
        )

        # Use default transaction fees if not provided
        if dash_transaction_fees is None:
            dash_transaction_fees = hail_config.dash_fee_configuration.dash_fees.get(
                MeterOperatingArea.URBAN, hail_config.dash_fee_configuration.common
            )

        # Parse fleet booking fee markup
        fleet_booking_fee_markup_logic = (
            (
                json.loads(fleet_booking_fee_markup)
                if isinstance(fleet_booking_fee_markup, str)
                else fleet_booking_fee_markup
            )
            if fleet_booking_fee_markup is not None
            else 0
        )

        # Use common additional booking fee
        additional_booking_fee_markup_logic = (
            json.loads(hail_config.additional_booking_fee)
            if isinstance(hail_config.additional_booking_fee, str)
            else hail_config.additional_booking_fee
        )

        # Set tunnel fee to 0
        tunnel_fee = 0

        # Calculate components
        dash_booking_fee = float(hail_config.dash_booking_fee)
        sub_total = {
            "+": [
                fare,
                tunnel_fee,
                fleet_booking_fee_markup_logic,
                dash_booking_fee,
                additional_booking_fee_markup_logic,
            ]
        }
        dash_transaction_fee = {
            "+": [
                {"*": [{"var": "dash_fee_rate"}, sub_total]},
                {"var": "dash_fee_constant"},
            ]
        }
        total = {"+": [sub_total, dash_transaction_fee]}

        # Parse discount rules
        third_party_discount_rules = 0
        dash_discount_rules = 0
        
        if discount_rules:
            if discount_rules.discount_rules_third_party:
                try:
                    third_party_discount_rules = json.loads(discount_rules.discount_rules_third_party)
                except json.JSONDecodeError:
                    self.logger_service.error("Invalid JSON in discount_rules_third_party", {
                        "discount_rules_third_party": discount_rules.discount_rules_third_party
                    })
            
            if discount_rules.discount_rules_dash:
                try:
                    dash_discount_rules = json.loads(discount_rules.discount_rules_dash)
                except json.JSONDecodeError:
                    self.logger_service.error("Invalid JSON in discount_rules_dash", {
                        "discount_rules_dash": discount_rules.discount_rules_dash
                    })

        discount = {
            "+": [
                third_party_discount_rules,
                dash_discount_rules,
            ]
        }

        total_discount = {"min": [total, {"*": [discount, -1]}]}
        discounted_total = {
            "max": [
                0,
                {"+": [total, discount]},
            ]
        }

        # Define price engine
        priceEngine: Any = {
            "if": [
                True,
                [
                    fare,
                    tunnel_fee,
                    dash_booking_fee,
                    fleet_booking_fee_markup_logic,
                    additional_booking_fee_markup_logic,
                    sub_total,
                    dash_transaction_fee,
                    total,
                    total_discount,
                    discounted_total,
                ],
            ]
        }

        # Get current time in HK timezone
        hk_datetime = DateTimeUtils.set_to_hk_tz(datetime.now())

        # Set input values
        inputValues = {
            "fare": fare,
            "time_hour": hk_datetime.hour,
            "date": hk_datetime.strftime(DATE_STRING_FORMAT),
            "day_of_week": hk_datetime.weekday(),
            "public_holidays": hail_config.public_holidays,
            "dash_fee_rate": dash_transaction_fees.dash_fee_rate,
            "dash_fee_constant": dash_transaction_fees.dash_fee_constant,
            "origin": None,
            "destination": None,
        }

        # Calculate results
        results: Any = jsonLogic(priceEngine, inputValues)

        self.logger_service.debug(f"Simplified price engine results: {results}")
        return FareCalculation(
            estimated_fare_fee=ConversionUtils.float_to_decimal(results[0]),
            estimated_tunnel_fee=ConversionUtils.float_to_decimal(results[1]),
            dash_booking_fee=ConversionUtils.float_to_decimal(results[2]),
            fleet_booking_fee=ConversionUtils.float_to_decimal(results[3]),
            additional_booking_fee=ConversionUtils.float_to_decimal(results[4]),
            sub_total=ConversionUtils.float_to_decimal(results[5]),
            dash_transaction_fee=ConversionUtils.float_to_decimal(results[6]),
            total=ConversionUtils.float_to_decimal(results[7]),
            discount=ConversionUtils.float_to_decimal(results[8]),
            discounted_total=ConversionUtils.float_to_decimal(results[9]),
            transaction_fees=dash_transaction_fees,
        )
