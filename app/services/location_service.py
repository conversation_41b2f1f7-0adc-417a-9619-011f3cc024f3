from app.contracts.location.trip_itinerary_estimation import TripItineraryEstimation
from app.infrastructure.settings import settings
from app.models.auth import Auth
import httpx

from app.infrastructure.settings import settings
from app.utils.http_client import create_http_client

from app.models.language import Language
from app.models.localization_language import LocalizationLanguage
from app.services.logger_service import LoggerService
from app.utils.exceptions.location_exception_builder import LocationExceptionBuilder


class LocationService:
    """
    Service to interact with dash location API
    """

    base_url: str
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.base_url = settings.dash_api_url
        self.logger_service = logger_service

    async def get_trip_estimation(
        self,
        origin_place_id: str,
        destination_place_id: str,
        language: LocalizationLanguage,
        auth: Auth,
    ) -> TripItineraryEstimation:

        if origin_place_id == destination_place_id:
            raise LocationExceptionBuilder().origin_and_destination_same()

        async with create_http_client() as client:
            try:
                api_locale: Language
                match language:
                    case LocalizationLanguage.ZH_HK:
                        api_locale = Language.ZH_HK
                    case _:
                        api_locale = Language.EN

                response = await client.get(
                    f"{self.base_url}/me/locations/compute-routes",
                    headers={
                        "Authorization": f"Bearer {auth.token}",
                    },
                    params={
                        "originPlaceId": origin_place_id,
                        "destinationPlaceId": destination_place_id,
                        "language": api_locale,
                    },
                )

                response.raise_for_status()
                route_response = response.json()
                return TripItineraryEstimation.model_validate(route_response)
            except httpx.HTTPStatusError as e:
                route_response = response.json()
                if "code" in route_response:
                    match route_response["code"]:
                        case "LO_006":
                            self.logger_service.error(
                                "Location service route not found",
                                extra={
                                    origin_place_id: origin_place_id,
                                    destination_place_id: destination_place_id,
                                },
                            )
                            raise LocationExceptionBuilder().route_not_found()
                        case _:
                            raise e
                else:
                    raise e
            except Exception as e:
                self.logger_service.error(
                    "Location service failure",
                    extra={
                        "error": e,
                        origin_place_id: origin_place_id,
                        destination_place_id: destination_place_id,
                    },
                )
                raise LocationExceptionBuilder().location_service_failure()
