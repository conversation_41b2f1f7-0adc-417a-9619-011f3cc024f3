import contextvars
from typing import Op<PERSON>
from uuid import uuid4

from app.models.request_context import RequestContext


class CorrelationService:
    __request_context: contextvars.ContextVar[Optional[RequestContext]]

    def __init__(self) -> None:
        self.__request_context: contextvars.ContextVar[Optional[RequestContext]] = (
            contextvars.ContextVar("request_context", default=None)
        )

    def set_request_context(self, request_context: RequestContext) -> None:
        self.__request_context.set(request_context)

    def get_request_context(self) -> Optional[RequestContext]:
        return self.__request_context.get()

    def get_correlation_id(self) -> str:
        request_context = self.get_request_context()
        return request_context.correlation_id if request_context else str(uuid4())

    def set_correlation_id(self, correlation_id: str) -> None:
        request_context = self.get_request_context()
        if request_context:
            request_context.correlation_id = correlation_id
            self.set_request_context(request_context)
        else:
            self.set_request_context(RequestContext(correlation_id=correlation_id))
