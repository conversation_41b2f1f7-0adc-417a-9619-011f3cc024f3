from app.persistence.redis.entities.message import Message
from app.services.correlation_service import CorrelationService
from app.sockets.messages.base_message import BaseMessage


class WebSocketMessageService:
    correlation_service: CorrelationService

    def __init__(self, correlation_service: CorrelationService) -> None:
        self.correlation_service = correlation_service

    async def trigger_message(self, socket_ids: set[str], message: BaseMessage) -> None:
        message.correlation_id = self.correlation_service.get_correlation_id()  
        await Message.trigger(socket_ids, message_data=message)