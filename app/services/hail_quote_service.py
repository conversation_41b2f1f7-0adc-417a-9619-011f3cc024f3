from uuid import uuid4
from app.contracts.common.pin import Pin
from app.contracts.matrix.create_hail_quote_request import CreateHailQuoteRequest
from app.contracts.matrix.create_hail_quote_response import CreateHailQuoteResponse, HailQuote, HailQuoteMetadata
from app.models.auth import Auth
from app.models.hail_platform_type import HailPlatformType
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.repositories.configurations_repository import ConfigurationsRepository
from app.persistence.redis.redis import RedisService
from app.services.fleet_fare_service import FleetFareService
from app.services.geo_spacial_service import GeoSpatialService
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.services.standard_fare_service import StandardFareService
from app.services.vehicles_class_service import VehiclesClassService


class HailQuoteService:
    logger_service: LoggerService
    standard_fare_service: StandardFareService
    configuration_repository: ConfigurationsRepository
    fleet_fare_service: FleetFareService
    location_service: LocationService
    geo_spacial_service: GeoSpatialService
    redis_service: RedisService
    
    def __init__(
        self,
        logger_service: LoggerService,
        standard_fare_service: StandardFareService,
        configuration_repository: ConfigurationsRepository,
        fleet_fare_service: FleetFareService,
        location_service: LocationService,
        geo_spacial_service: GeoSpatialService,
        redis_service: RedisService,
    ) -> None:
        self.logger_service = logger_service
        self.standard_fare_service = standard_fare_service
        self.configuration_repository = configuration_repository
        self.fleet_fare_service = fleet_fare_service
        self.location_service = location_service
        self.geo_spacial_service = geo_spacial_service
        self.redis_service = redis_service

    
    async def get_quotes(
        self,
        auth: Auth,
        request: CreateHailQuoteRequest,
    ) -> CreateHailQuoteResponse:
        if request.platform_type == HailPlatformType.FLEET:
            return await self.redis_service.getCachedOrFetch(
                key=self.generate_hail_cache_key(auth, request),
                fetch=lambda: self.get_quotes_by_fleet(
                    auth=auth,
                    request=request,
                ),
                ttl_seconds=60,
                model_type=CreateHailQuoteResponse,
            )
        else:
            return await self.redis_service.getCachedOrFetch(
                key=self.generate_hail_cache_key(auth, request),
                fetch=lambda: self.get_quotes_by_dash(
                    auth=auth,
                    request=request,
                ),
                ttl_seconds=3,
                model_type=CreateHailQuoteResponse,
            )
            
            
    async def get_quotes_by_dash(
        self,
        auth: Auth,
        request: CreateHailQuoteRequest,
    ) -> CreateHailQuoteResponse:
        trip_estimation = await self.location_service.get_trip_estimation(
            origin_place_id=request.place_ids[0],
            destination_place_id=request.place_ids[1],
            language=request.language,
            auth=auth,
        )

        available_operating_areas: list[MeterOperatingArea] = (
            self.geo_spacial_service.operating_area(
                origin=Pin(
                    lat=trip_estimation.origin_place_details.lat,
                    lng=trip_estimation.origin_place_details.lng,
                ),
                destination=Pin(
                    lat=trip_estimation.destination_place_details.lat,
                    lng=trip_estimation.destination_place_details.lng,
                ),
            )
            or []
        )

        hail_config = await self.configuration_repository.get_hail_configuration()
        vehicle_class_fare_calculations = await self.standard_fare_service.get_fare_calculation_by_vehicle_classes(
            auth=auth,
            place_ids=request.place_ids,
            language=request.language,
            is_double_tunnel_fee=request.is_double_tunnel_fee,
            vehicle_classes=list(VehicleClass),
            price_rules=request.price_rules,
            discount_rules=request.applicable_discounts.to_discount_rules(),
        )
        
        vehicle_class_configs = {
            vehicle_class: hail_config.vehicle_classes[vehicle_class]
            for vehicle_class in VehicleClass
        }
        
        self.logger_service.debug(f"HailQuoteService/get_quotes_by_dash-start", {
            "vehicle_class_configs": vehicle_class_configs,
            "request": request.model_dump(),
        })
        
        return CreateHailQuoteResponse(
            quote_id=str(uuid4()),
            quotes=[
                HailQuote(
                    price_matrix=vehicle_class_fare_calculations[vehicle_class],
                    vehicle_class=vehicle_class.value,
                    vehicle_class_name=vehicle_class_configs[vehicle_class].name_for_language(request.language),
                    vehicle_class_description=vehicle_class_configs[vehicle_class].description_for_language(request.language),
                    metadata=HailQuoteMetadata(
                        seats_count=vehicle_class_configs[vehicle_class].seats_count or 4, 
                        luggage_count=vehicle_class_configs[vehicle_class].luggage_count or 2,
                        vehicle_icon_url=VehiclesClassService.get_vehicle_image_by_operating_areas_and_vehicle_class(
                            operating_areas=available_operating_areas, vehicle_class=vehicle_class
                        )
                    ),
                    operating_area=available_operating_areas,
                )
                for vehicle_class in vehicle_class_fare_calculations
            ]
        )

    async def get_quotes_by_fleet(
        self,
        auth: Auth,
        request: CreateHailQuoteRequest,
    ) -> CreateHailQuoteResponse:
        fare_calculation_map, fleet_quote_response = await self.fleet_fare_service.get_fare_calculation_map(
            auth=auth,
            place_ids=request.place_ids,
            language=request.language,
            price_rules=request.price_rules,
            request_time=request.time,
            discount_rules=request.applicable_discounts.to_discount_rules(),
        )
        
        return CreateHailQuoteResponse(
            quote_id=str(uuid4()),
            quotes=[
                HailQuote(
                    price_matrix=fare_calculation_map[fleet_vehicle_class_option.fleet_vehicle_type],
                    vehicle_class=fleet_vehicle_class_option.fleet_vehicle_type,
                    vehicle_class_name=fleet_vehicle_class_option.vehicle_class_name,
                    vehicle_class_description=fleet_vehicle_class_option.vehicle_class_description,
                    metadata=HailQuoteMetadata(
                        seats_count=fleet_vehicle_class_option.seats_count,
                        luggage_count=fleet_vehicle_class_option.luggage_count,
                        fleet_vehicle_type=fleet_vehicle_class_option.fleet_vehicle_type,
                        fleet_vehicle_id=fleet_vehicle_class_option.fleet_vehicle_id,
                        fleet_partner_key=fleet_vehicle_class_option.fleet_partner_key,
                        fleet_icon_url=fleet_vehicle_class_option.fleet_icon_url,
                        vehicle_icon_url=fleet_vehicle_class_option.vehicle_icon_url,
                    ),
                    operating_area=[MeterOperatingArea.URBAN]
                )
                for fleet_vehicle_class_option in fleet_quote_response.vehicle_class_options
            ]
        )
        
    def generate_hail_cache_key(self, auth: Auth, request: CreateHailQuoteRequest) -> str:
        return f"dash:quote:{':'.join(request.place_ids)}:{request.time.strftime('%Y-%m-%d-%H')}:{request.language.lower()}:{request.is_double_tunnel_fee}:{request.platform_type}:{request.fleet_partner_key}:{request.applicable_discounts.discount_id_dash}:{request.applicable_discounts.discount_id_third_party}:{auth.user_id}"
