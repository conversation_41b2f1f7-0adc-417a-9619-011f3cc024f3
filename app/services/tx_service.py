from app.contracts.tx.hailing_merchant_approaching_destination import HailingMerchantApproachingDestination
from app.contracts.tx.hailing_order_complete import HailingOrderComplete
from app.contracts.tx.hail_order_request_timeout import HailOrderRequestTimeout
from app.contracts.tx.hailing_merchant_accepts_order import HailingMerchantAcceptsOrder
from app.contracts.tx.hailing_merchant_accepts_order_response import (
    HailingMerchantAcceptsOrderResponse,
)
from app.contracts.tx.hailing_merchant_cancels_order import HailingMerchantCancelsOrder
from app.contracts.tx.hailing_merchant_pick_up_confirmed import (
    HailingMerchantPickUpConfirmed,
)
from app.contracts.tx.hailing_user_cancels_order import HailingUser<PERSON>ancelsOrder
from app.contracts.tx.hailing_user_cancels_order_response import (
    HailingUserCancelsOrderResponse,
)
from app.contracts.tx.hailing_user_updates_order import HailingUserUpdatesOrder
from app.contracts.tx.tx_event_base_response import TxEventBaseResponse
from app.infrastructure.settings import settings
from app.models.auth import Auth
import httpx
from app.infrastructure.settings import settings

from app.infrastructure.settings import settings

from app.models.tx_event_type import TxEventType
from app.services.logger_service import LoggerService
from app.utils.exceptions.tx_exception_builder import TxExceptionBuilder
from app.utils.http_client import create_http_client


class TxService:
    """
    Service to interact with dash tx API
    """

    base_url: str
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.base_url = settings.dash_api_url
        self.logger_service = logger_service

    async def add_tx_event(
        self,
        tx_id: str,
        auth: Auth,
        event: (
            HailingMerchantAcceptsOrder
            | HailingUserCancelsOrder
            | HailingMerchantCancelsOrder
            | HailingMerchantPickUpConfirmed
            | HailOrderRequestTimeout
            | HailingUserUpdatesOrder
            | HailingOrderComplete
            | HailingMerchantApproachingDestination
        ),
    ) -> (
        TxEventBaseResponse
        | HailingUserCancelsOrderResponse
        | HailingMerchantAcceptsOrderResponse
    ):
        async with create_http_client() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/me/transactions/{tx_id}/event",
                    headers={
                        "Authorization": f"Bearer {auth.token}",
                    },
                    json=event.model_dump(by_alias=True),
                )
                response.raise_for_status()
                response_dict = response.json()
                event_type = response_dict.get("type", None)

                self.logger_service.debug(
                    f"Event type {event_type} for hail {tx_id}", extra=response_dict
                )

                if event_type not in TxEventType:
                    raise TxExceptionBuilder().event_type_not_valid(
                        event_type=event_type
                    )

                match event_type:
                    case TxEventType.HAILING_USER_CANCELS_ORDER:
                        return HailingUserCancelsOrderResponse.model_validate(
                            response_dict
                        )
                    case TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER:
                        return HailingMerchantAcceptsOrderResponse.model_validate(
                            response_dict
                        )
                    case _:
                        return TxEventBaseResponse.model_validate(response_dict)
            except httpx.HTTPStatusError as e:
                try:
                    tx_event_response = response.json()
                    self.logger_service.error(
                        f"Tx service HTTP error for Tx {tx_id}. Status: {e.response.status_code}",
                        extra={
                            "status_code": e.response.status_code,
                            "response_body": tx_event_response,
                            "request_url": str(e.request.url),
                            "request_body": event.model_dump(by_alias=True)
                        },
                    )
                except Exception:
                    self.logger_service.error(
                        f"Tx service HTTP error for Tx {tx_id}. Status: {e.response.status_code}. Response not JSON.",
                        extra={
                            "status_code": e.response.status_code,
                            "response_text": e.response.text,
                            "request_url": str(e.request.url),
                            "request_body": event.model_dump(by_alias=True)
                        },
                    )
                raise TxExceptionBuilder().tx_service_failure()
            except Exception as e:
                self.logger_service.error(
                    f"Tx service failure for Tx {tx_id}.",
                    extra={
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "request_body": event.model_dump(by_alias=True)
                    },
                )
                raise TxExceptionBuilder().tx_service_failure()
