import logging
from typing import Any
from datetime import datetime, date, time as dtime
from enum import Enum

from app.infrastructure.settings import settings
from google.cloud import logging as google_logging
from google.cloud.logging_v2.handlers.structured_log import StructuredLogHandler

from app.services.correlation_service import CorrelationService


class LoggerService:
    correlation_service: CorrelationService
    logger: logging.Logger

    def __init__(self, correlation_service: CorrelationService) -> None:
        self.correlation_service = correlation_service

        # Disable uvicorn access logging since we have our own HTTP logging middleware
        uvicorn_access_logger = logging.getLogger("uvicorn.access")
        uvicorn_access_logger.disabled = True

        if settings.is_local:
            self.logger = logging.getLogger("uvicorn")
            self.logger.setLevel(logging.DEBUG)
        else:
            client = google_logging.Client()  # type: ignore
            client.setup_logging(log_level=logging.DEBUG)  # type: ignore
            self.logger = logging.getLogger("google_cloud_logger")
            self.logger.propagate = False
            for logger in logging.Logger.manager.loggerDict.values():
                if isinstance(logger, logging.Logger):
                    logger.handlers = []
                    logger.addHandler(StructuredLogHandler())  # type: ignore

    def log(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.info(self.__handle_log(message, extra))

    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.debug(self.__handle_log(message, extra))

    def error(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.error(self.__handle_log(message, extra))

    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.warning(self.__handle_log(message, extra))

    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.exception(self.__handle_log(message, extra))

    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self.logger.info(self.__handle_log(message, extra))

    def log_function_start(self, service_name: str, function_name: str, **kwargs: Any) -> None:
        """Log function start and store start time in request context"""
        request_context = self.correlation_service.get_request_context()
        if request_context:
            full_function_name = f"{service_name}/{function_name}"
            request_context.start_function_timer(full_function_name)
            self.correlation_service.set_request_context(request_context)
        
        self.info(
            f"{service_name}/{function_name}-start",
            kwargs
        )

    def log_function_end(self, service_name: str, function_name: str, **kwargs: Any) -> None:
        """Log function end with execution time from request context"""
        request_context = self.correlation_service.get_request_context()
        execution_time_ms = None
        
        if request_context:
            full_function_name = f"{service_name}/{function_name}"
            execution_time_ms = request_context.get_function_execution_time_ms(full_function_name)
            self.correlation_service.set_request_context(request_context)
        
        log_data = dict(kwargs)
        if execution_time_ms is not None:
            log_data["execute_duration"] = execution_time_ms
            
        self.info(
            f"{service_name}/{function_name}-end",
            log_data
        )
        
    def log_function_end_with_error(self, service_name: str, function_name: str, error: Exception, **kwargs: Any) -> None:
        """Log function end with error and execution time from request context"""
        request_context = self.correlation_service.get_request_context()
        execution_time_ms = None
        
        if request_context:
            full_function_name = f"{service_name}/{function_name}"
            execution_time_ms = request_context.get_function_execution_time_ms(full_function_name)
            self.correlation_service.set_request_context(request_context)
        
        log_data = dict(kwargs)
        if execution_time_ms is not None:
            log_data["execute_duration"] = execution_time_ms
            
        # Add error details to log data
        log_data.update({
            "error_type": type(error).__name__,
            "error_message": str(error),
            "error_module": type(error).__module__,
        })
            
        self.error(
            f"{service_name}/{function_name}-end",
            log_data,
        )

    def set_correlation_id(self, correlation_id: str) -> None:
        self.correlation_service.set_correlation_id(correlation_id)

    def __handle_log(
        self, message: str, extra: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        request_context = self.correlation_service.get_request_context()
        ctx = request_context.model_dump() if request_context else {}
        # Sanitize payload to ensure JSON-serializable for StructuredLogHandler
        json_fields = self.__make_json_safe({
            "cls_context": ctx,
            **(extra or {}),
        })

        return {
            "message": message,
            "json_fields": json_fields,
        }

    def __make_json_safe(self, data: dict[str, Any]) -> dict[str, Any]:
        return {str(k): self.__to_json_safe(v) for k, v in data.items()}

    def __to_json_safe(self, obj: Any) -> Any:
        # Primitives
        if obj is None or isinstance(obj, (str, int, float, bool)):
            return obj

        # Datetime-like
        if isinstance(obj, (datetime, date, dtime)):
            return obj.isoformat()

        # Enums
        if isinstance(obj, Enum):
            return getattr(obj, "value", str(obj))

        # Bytes
        if isinstance(obj, (bytes, bytearray)):
            try:
                return obj.decode("utf-8", errors="replace")
            except Exception:
                return str(obj)

        # Collections
        if isinstance(obj, (list, tuple, set)):
            return [self.__to_json_safe(v) for v in obj]

        if isinstance(obj, dict):
            return {str(k): self.__to_json_safe(v) for k, v in obj.items()}

        # Pydantic v2 model or similar
        try:
            if hasattr(obj, "model_dump") and callable(getattr(obj, "model_dump")):
                return self.__to_json_safe(obj.model_dump())
        except Exception:
            pass

        # Pydantic v1 or dataclass-like
        try:
            if hasattr(obj, "dict") and callable(getattr(obj, "dict")):
                return self.__to_json_safe(obj.dict())
        except Exception:
            pass

        # Fallback to string representation
        try:
            return str(obj)
        except Exception:
            return "<unserializable>"
