import asyncio
from decimal import Decimal
import json
from app.contracts.campaigns.applicable_campaigns import ApplicableCampaigns
from app.contracts.common.pin import Pin
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetOptionMatrixV2
from app.contracts.matrix.hail_option_matrix import (
    Hai<PERSON><PERSON><PERSON><PERSON><PERSON>,
    HailOptionMatrix,
    HailVehicleClassOption,
)
from app.contracts.matrix.hail_option_matrix_v2 import (
    HailOptionMatrixV2,
    HailVehicleClassOptionV2,
)
from app.models.auth import Auth
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.firestore.repositories.firestore_repository import (
    FirestoreRepository,
)
from app.persistence.redis.redis import RedisService
from app.services.campaign_service import CampaignService
from app.services.fare_calculation_service import FareCalculationService
from app.services.fare_estimation_service import FareEstimationService
from app.services.fleet_quote_converter_service import FleetQuoteConverterService
from app.services.fleet_quote_service import FleetQuoteService
from app.services.geo_service import GeoService
from app.services.geo_spacial_service import GeoSpatialService
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.utils.exceptions.location_exception_builder import LocationExceptionBuilder
from app.utils.time_utils import TimeUtils


class OptionMatrixService:
    logger_service: LoggerService
    configuration_repository: ConfigurationsRepository
    firestore_repository: FirestoreRepository
    geo_service: GeoService
    geo_spacial_service: GeoSpatialService
    location_service: LocationService
    fare_estimation_service: FareEstimationService
    redis_service: RedisService
    fare_calculation_service: FareCalculationService
    campaign_service: CampaignService
    fleet_quote_service: FleetQuoteService
    fleet_quote_converter_service: FleetQuoteConverterService

    def __init__(
        self,
        logger_service: LoggerService,
        configuration_repository: ConfigurationsRepository,
        firestore_repository: FirestoreRepository,
        geo_service: GeoService,
        geo_spacial_service: GeoSpatialService,
        location_service: LocationService,
        fare_estimation_service: FareEstimationService,
        redis_service: RedisService,
        fare_calculation_service: FareCalculationService,
        campaign_service: CampaignService,
        fleet_quote_service: FleetQuoteService,
        fleet_quote_converter_service: FleetQuoteConverterService,
    ) -> None:
        self.logger_service = logger_service
        self.configuration_repository = configuration_repository
        self.firestore_repository = firestore_repository
        self.geo_service = geo_service
        self.geo_spacial_service = geo_spacial_service
        self.location_service = location_service
        self.fare_estimation_service = fare_estimation_service
        self.redis_service = redis_service
        self.fare_calculation_service = fare_calculation_service
        self.campaign_service = campaign_service
        self.fleet_quote_service = fleet_quote_service
        self.fleet_quote_converter_service = fleet_quote_converter_service

    async def get_hail_option_matrix(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailOptionMatrix | HailOptionMatrixV2:
        if await self.redis_service.keyExists(
            self.__option_matrix_key(
                version=2,
                create_hail_option_matrix_request=create_hail_option_matrix_request,
            )
        ):
            return await self.get_hail_option_matrix_v2(
                create_hail_option_matrix_request, auth
            )
        elif await self.redis_service.keyExists(
            self.__option_matrix_key(
                version=1,
                create_hail_option_matrix_request=create_hail_option_matrix_request,
            )
        ):
            return await self.get_hail_option_matrix_v1(
                create_hail_option_matrix_request, auth
            )
        else:
            return await self.get_hail_option_matrix_v2(
                create_hail_option_matrix_request, auth
            )

    async def get_hail_option_matrix_v1(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailOptionMatrix:
        return await self.redis_service.getCachedOrFetch(
            key=self.__option_matrix_key(
                version=1,
                create_hail_option_matrix_request=create_hail_option_matrix_request,
            ),
            fetch=lambda: self.__generate_hail_option_matrix(
                create_hail_option_matrix_request=create_hail_option_matrix_request,
                auth=auth,
            ),
            ttl_seconds=TimeUtils.minutes_to_seconds(30),
            model_type=HailOptionMatrix,
        )

    async def get_hail_option_matrix_v2(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailOptionMatrixV2:
        return await self.redis_service.getCachedOrFetch(
            key=self.__option_matrix_key(
                version=2,
                create_hail_option_matrix_request=create_hail_option_matrix_request,
            ),
            fetch=lambda: self.__generate_hail_option_matrix_v2(
                create_hail_option_matrix_request=create_hail_option_matrix_request,
                auth=auth,
            ),
            ttl_seconds=TimeUtils.minutes_to_seconds(30),
            model_type=HailOptionMatrixV2,
        )
        
    async def get_fleet_hail_option_matrix(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailFleetOptionMatrixV2:
        self.logger_service.log(
            f"Get fleet hail option matrix",
            {
                "create_hail_option_matrix_request": create_hail_option_matrix_request.model_dump_json()
            },
        )
        return await self.redis_service.getCachedOrFetch(
            key=self.__option_matrix_key(
                version=2,
                create_hail_option_matrix_request=create_hail_option_matrix_request,
            ),
            fetch=lambda: self.__generate_fleet_hail_option_matrix(
                create_hail_option_matrix_request=create_hail_option_matrix_request,
                auth=auth,
            ),
            ttl_seconds=30,
            model_type=HailFleetOptionMatrixV2,
        )

    def __option_matrix_key(
        self,
        version: int,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
    ) -> str:
        discount_id_dash = (
            create_hail_option_matrix_request.applicable_discounts.discount_id_dash
            if create_hail_option_matrix_request.applicable_discounts
            else ""
        )
        discount_id_third_party = (
            create_hail_option_matrix_request.applicable_discounts.discount_id_third_party
            if create_hail_option_matrix_request.applicable_discounts
            else ""
        )
        campaign_id_dash = (
            create_hail_option_matrix_request.applicable_campaigns.campaign_id_dash
            if create_hail_option_matrix_request.applicable_campaigns
            else ""
        )
        campaign_id_third_party = (
            create_hail_option_matrix_request.applicable_campaigns.campaign_id_third_party
            if create_hail_option_matrix_request.applicable_campaigns
            else ""
        )
        platform_type = create_hail_option_matrix_request.platform_type
        boost_amount = create_hail_option_matrix_request.boost_amount or 0.0
        
        return f"dash:matrix{version}:{':'.join(create_hail_option_matrix_request.place_ids)}:{create_hail_option_matrix_request.time.strftime('%Y-%m-%d-%H')}:{create_hail_option_matrix_request.language.lower()}:{create_hail_option_matrix_request.payment_instrument_type}:{create_hail_option_matrix_request.double_tunnel_fee}:{discount_id_dash}:{discount_id_third_party}:{campaign_id_dash}:{campaign_id_third_party}:{boost_amount}:{platform_type}"

    async def __generate_hail_option_matrix(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailOptionMatrix:
        trip_estimation = await self.location_service.get_trip_estimation(
            origin_place_id=create_hail_option_matrix_request.place_ids[0],
            destination_place_id=create_hail_option_matrix_request.place_ids[1],
            language=create_hail_option_matrix_request.language,
            auth=auth,
        )

        operating_area = self.geo_service.get_operating_area_for_trip(
            origin=trip_estimation.origin_place_details,
            destination=trip_estimation.destination_place_details,
        )
        if operating_area is None:
            raise LocationExceptionBuilder().location_outside_of_service_area()

        is_within_lantau = operating_area == MeterOperatingArea.LANTAU

        self.logger_service.log(
            f"Request is {'within Lantau' if is_within_lantau else 'not within Lantau'}"
        )

        fleets = await self.firestore_repository.fleet_repository().get_fleets()
        hail_config = await self.configuration_repository.get_hail_configuration()

        estimated_base_fare = self.fare_estimation_service.base_fare_estimation(
            google_duration=trip_estimation.duration_seconds,
            google_distance=trip_estimation.distance_meters,
            origin_pin=trip_estimation.origin_place_details,
            destination_pin=trip_estimation.destination_place_details,
            operating_area=(
                MeterOperatingArea.LANTAU
                if is_within_lantau
                else MeterOperatingArea.URBAN
            ),
        )

        estimated_tunnel_fares = (
            (
                self.fare_estimation_service.tunnel_fare_estimation(
                    route=trip_estimation.encoded_polyline, hail_config=hail_config
                )
            )
            if trip_estimation.encoded_polyline
            else []
        )
        estimated_tunnel_fare: Decimal = sum(
            [fare.fee for fare in estimated_tunnel_fares], Decimal(0)
        )

        class_fleet_map: dict[VehicleClass, list[HailSubOption]] = {}

        hail_vehicle_classes: list[HailVehicleClassOption] | None = None
        if operating_area in hail_config.enabled_operating_areas:
            for vehicle_class in [VehicleClass.COMFORT, VehicleClass.LUXURY]:
                fleets_for_class: list[HailSubOption] = []
                for fleet in fleets:
                    if fleet.markup is None or vehicle_class not in fleet.markup:
                        continue

                    fare_calculation = self.fare_calculation_service.calculate_fare(
                        fare=float(estimated_base_fare),
                        tunnel_fee=float(estimated_tunnel_fare),
                        fleet_booking_fee_markup=fleet.markup[vehicle_class],
                        datetime=create_hail_option_matrix_request.time,
                        origin=trip_estimation.origin_place_details,
                        destination=trip_estimation.destination_place_details,
                        operating_area=operating_area,
                        hail_config=hail_config,
                        vehicle_class=vehicle_class,
                    )

                    fleets_for_class.append(
                        HailSubOption(
                            fleet_id=fleet.id,
                            fleet_name=(
                                fleet.name
                                if create_hail_option_matrix_request.is_english
                                else fleet.name_tc
                            ),
                            **fare_calculation.model_dump(),
                        )
                    )

                class_fleet_map[vehicle_class] = fleets_for_class

            vehicle_class_configs = {
                vehicle_class: hail_config.vehicle_classes[vehicle_class]
                for vehicle_class in VehicleClass
            }

            standard_fare_calculation = self.fare_calculation_service.calculate_fare(
                fare=float(estimated_base_fare),
                tunnel_fee=float(estimated_tunnel_fare),
                fleet_booking_fee_markup=0,
                datetime=create_hail_option_matrix_request.time,
                operating_area=operating_area,
                origin=trip_estimation.origin_place_details,
                destination=trip_estimation.destination_place_details,
                hail_config=hail_config,
                vehicle_class=VehicleClass.FOUR_SEATER,
            )

            hail_vehicle_classes = [
                HailVehicleClassOption(
                    vehicle_class=VehicleClass.FOUR_SEATER,
                    vehicle_class_name=(
                        vehicle_class_configs[
                            VehicleClass.FOUR_SEATER
                        ].name_for_language(
                            language=create_hail_option_matrix_request.language
                        )
                    ),
                    vehicle_class_description=(
                        vehicle_class_configs[
                            VehicleClass.FOUR_SEATER
                        ].description_for_language(
                            language=create_hail_option_matrix_request.language
                        )
                    ),
                    image_name=vehicle_class_configs[
                        VehicleClass.FOUR_SEATER
                    ].vehicle_image(operating_area=operating_area),
                    seats_count=vehicle_class_configs[
                        VehicleClass.FOUR_SEATER
                    ].seats_count
                    or 4,
                    luggage_count=vehicle_class_configs[
                        VehicleClass.FOUR_SEATER
                    ].luggage_count
                    or 2,
                    sub_options=[
                        HailSubOption(**standard_fare_calculation.model_dump())
                    ],
                ),
                HailVehicleClassOption(
                    vehicle_class=VehicleClass.FIVE_SEATER,
                    vehicle_class_name=(
                        vehicle_class_configs[
                            VehicleClass.FIVE_SEATER
                        ].name_for_language(
                            language=create_hail_option_matrix_request.language
                        )
                    ),
                    vehicle_class_description=(
                        vehicle_class_configs[
                            VehicleClass.FIVE_SEATER
                        ].description_for_language(
                            language=create_hail_option_matrix_request.language
                        )
                    ),
                    image_name=vehicle_class_configs[
                        VehicleClass.FIVE_SEATER
                    ].vehicle_image(operating_area=operating_area),
                    seats_count=vehicle_class_configs[
                        VehicleClass.FIVE_SEATER
                    ].seats_count
                    or 5,
                    luggage_count=vehicle_class_configs[
                        VehicleClass.FIVE_SEATER
                    ].luggage_count
                    or 2,
                    sub_options=[
                        HailSubOption(**standard_fare_calculation.model_dump())
                    ],
                ),
                *(
                    [
                        HailVehicleClassOption(
                            vehicle_class=VehicleClass.COMFORT,
                            vehicle_class_name=(
                                vehicle_class_configs[
                                    VehicleClass.COMFORT
                                ].name_for_language(
                                    language=create_hail_option_matrix_request.language
                                )
                            ),
                            vehicle_class_description=(
                                vehicle_class_configs[
                                    VehicleClass.COMFORT
                                ].description_for_language(
                                    language=create_hail_option_matrix_request.language
                                )
                            ),
                            image_name=vehicle_class_configs[
                                VehicleClass.COMFORT
                            ].vehicle_image(operating_area=operating_area),
                            seats_count=vehicle_class_configs[
                                VehicleClass.COMFORT
                            ].seats_count
                            or 4,
                            luggage_count=vehicle_class_configs[
                                VehicleClass.COMFORT
                            ].luggage_count
                            or 2,
                            sub_options=class_fleet_map[VehicleClass.COMFORT],
                        ),
                        HailVehicleClassOption(
                            vehicle_class=VehicleClass.LUXURY,
                            vehicle_class_name=(
                                vehicle_class_configs[
                                    VehicleClass.LUXURY
                                ].name_for_language(
                                    language=create_hail_option_matrix_request.language
                                )
                            ),
                            vehicle_class_description=(
                                vehicle_class_configs[
                                    VehicleClass.LUXURY
                                ].description_for_language(
                                    language=create_hail_option_matrix_request.language
                                )
                            ),
                            image_name=vehicle_class_configs[
                                VehicleClass.LUXURY
                            ].vehicle_image(operating_area=operating_area),
                            seats_count=vehicle_class_configs[
                                VehicleClass.LUXURY
                            ].seats_count
                            or 6,
                            luggage_count=vehicle_class_configs[
                                VehicleClass.LUXURY
                            ].luggage_count
                            or 4,
                            sub_options=class_fleet_map[VehicleClass.LUXURY],
                        ),
                    ]
                    if not is_within_lantau
                    else []
                ),
            ]

        return HailOptionMatrix(
            estimated_base_fare=estimated_base_fare,
            operating_area=operating_area,
            distance_meters=trip_estimation.distance_meters,
            duration_seconds=trip_estimation.duration_seconds,
            encoded_polyline=trip_estimation.encoded_polyline,
            origin_place_details=trip_estimation.origin_place_details,
            destination_place_details=trip_estimation.destination_place_details,
            matrix=hail_vehicle_classes if hail_vehicle_classes else [],
        )

    async def __generate_hail_option_matrix_v2(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailOptionMatrixV2:
        trip_estimation, hail_config = await asyncio.gather(
            self.location_service.get_trip_estimation(
                origin_place_id=create_hail_option_matrix_request.place_ids[0],
                destination_place_id=create_hail_option_matrix_request.place_ids[1],
                language=create_hail_option_matrix_request.language,
                auth=auth,
            ),
            self.configuration_repository.get_hail_configuration(),
        )

        applicable_campaigns = (
            create_hail_option_matrix_request.applicable_campaigns
            if create_hail_option_matrix_request.applicable_campaigns
            else ApplicableCampaigns.model_validate({})
        )

        discount_rules = create_hail_option_matrix_request.discount_rules_to_apply

        available_operating_areas: list[MeterOperatingArea] = (
            self.geo_spacial_service.operating_area(
                origin=Pin(
                    lat=trip_estimation.origin_place_details.lat,
                    lng=trip_estimation.origin_place_details.lng,
                ),
                destination=Pin(
                    lat=trip_estimation.destination_place_details.lat,
                    lng=trip_estimation.destination_place_details.lng,
                ),
            )
            or []
        )

        self.logger_service.debug(
            f"Available operating areas: {', '.join([str(area) for area in available_operating_areas])}"
        )

        is_within_lantau = MeterOperatingArea.LANTAU in available_operating_areas
        self.logger_service.log(
            f"Request is {'within Lantau' if is_within_lantau else 'not within Lantau'}"
        )

        estimated_base_fare_by_operating_area = {
            area: self.fare_estimation_service.base_fare_estimation(
                google_duration=trip_estimation.duration_seconds,
                google_distance=trip_estimation.distance_meters,
                origin_pin=trip_estimation.origin_place_details,
                destination_pin=trip_estimation.destination_place_details,
                operating_area=area,
            )
            for area in available_operating_areas
        }

        estimated_tunnel_fares = (
            (
                self.fare_estimation_service.tunnel_fare_estimation(
                    route=trip_estimation.encoded_polyline, hail_config=hail_config
                )
            )
            if trip_estimation.encoded_polyline
            else []
        )
        estimated_tunnel_fare: Decimal = sum(
            [
                (
                    fare.fee * 2
                    if create_hail_option_matrix_request.double_tunnel_fee
                    and fare.is_double_fee_applicable
                    else fare.fee
                )
                for fare in estimated_tunnel_fares
            ],
            Decimal(0),
        )

        operating_area_options: dict[
            MeterOperatingArea, list[HailVehicleClassOptionV2]
        ] = {}

        boost_amount = create_hail_option_matrix_request.boost_amount or 0.0


        for (
            operating_area,
            estimated_base_fare,
        ) in estimated_base_fare_by_operating_area.items():
            class_fleet_map: dict[VehicleClass, list[HailSubOption]] = {}

            if operating_area in hail_config.enabled_operating_areas:
                for vehicle_class in [VehicleClass.COMFORT, VehicleClass.LUXURY]:
                    # options_for_class: list[HailSubOption] = []
                    # if len(fleets) > 0:
                    #     for fleet in fleets:
                    #         if (
                    #             fleet.markup is None
                    #             or vehicle_class not in fleet.markup
                    #         ):
                    #             continue

                    #         fare_calculation = self.fare_calculation_service.calculate_fare(
                    #             fare=float(estimated_base_fare),
                    #             tunnel_fee=float(estimated_tunnel_fare),
                    #             fleet_booking_fee_markup=fleet.markup[vehicle_class],
                    #             datetime=create_hail_option_matrix_request.time,
                    #             origin=trip_estimation.origin_place_details,
                    #             destination=trip_estimation.destination_place_details,
                    #             operating_area=operating_area,
                    #             hail_config=hail_config,
                    #             discount_rules=discount_rules,
                    #             vehicle_class=vehicle_class,
                    #         )

                    #         options_for_class.append(
                    #             HailSubOption(
                    #                 fleet_id=fleet.id,
                    #                 fleet_name=(
                    #                     fleet.name
                    #                     if create_hail_option_matrix_request.is_english
                    #                     else fleet.name_tc
                    #                 ),
                    #                 **fare_calculation.model_dump(),
                    #             )
                    #         )

                    #     class_fleet_map[vehicle_class] = options_for_class
                    # else:
                    class_fleet_map[vehicle_class] = [
                        HailSubOption(
                            fleet_id=None,
                            fleet_name=None,
                            **self.fare_calculation_service.calculate_fare(
                                fare=float(estimated_base_fare),
                                tunnel_fee=float(estimated_tunnel_fare),
                                fleet_booking_fee_markup=0,
                                datetime=create_hail_option_matrix_request.time,
                                origin=trip_estimation.origin_place_details,
                                destination=trip_estimation.destination_place_details,
                                operating_area=operating_area,
                                hail_config=hail_config,
                                discount_rules=discount_rules,
                                vehicle_class=vehicle_class,
                                boost_amount=boost_amount,
                            ).model_dump(),
                        )
                    ]

                vehicle_class_configs = {
                    vehicle_class: hail_config.vehicle_classes[vehicle_class]
                    for vehicle_class in VehicleClass
                }

                standard_fare_calculation = (
                    self.fare_calculation_service.calculate_fare(
                        fare=float(estimated_base_fare),
                        tunnel_fee=float(estimated_tunnel_fare),
                        fleet_booking_fee_markup=0,
                        datetime=create_hail_option_matrix_request.time,
                        operating_area=operating_area,
                        origin=trip_estimation.origin_place_details,
                        destination=trip_estimation.destination_place_details,
                        hail_config=hail_config,
                        discount_rules=discount_rules,
                        vehicle_class=VehicleClass.FOUR_SEATER,
                        boost_amount=boost_amount,
                    )
                )

                hail_vehicle_classes = [
                    HailVehicleClassOptionV2(
                        vehicle_class=VehicleClass.FOUR_SEATER,
                        vehicle_class_name=(
                            vehicle_class_configs[
                                VehicleClass.FOUR_SEATER
                            ].name_for_language(
                                language=create_hail_option_matrix_request.language
                            )
                        ),
                        vehicle_class_description=(
                            vehicle_class_configs[
                                VehicleClass.FOUR_SEATER
                            ].description_for_language(
                                language=create_hail_option_matrix_request.language
                            )
                        ),
                        seats_count=vehicle_class_configs[
                            VehicleClass.FOUR_SEATER
                        ].seats_count
                        or 4,
                        luggage_count=vehicle_class_configs[
                            VehicleClass.FOUR_SEATER
                        ].luggage_count
                        or 2,
                        sub_options=[
                            HailSubOption(**standard_fare_calculation.model_dump())
                        ],
                    ),
                    *(
                        [
                            HailVehicleClassOptionV2(
                                vehicle_class=VehicleClass.COMFORT,
                                vehicle_class_name=(
                                    vehicle_class_configs[
                                        VehicleClass.COMFORT
                                    ].name_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                vehicle_class_description=(
                                    vehicle_class_configs[
                                        VehicleClass.COMFORT
                                    ].description_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                seats_count=vehicle_class_configs[
                                    VehicleClass.COMFORT
                                ].seats_count
                                or 4,
                                luggage_count=vehicle_class_configs[
                                    VehicleClass.COMFORT
                                ].luggage_count
                                or 2,
                                sub_options=class_fleet_map[VehicleClass.COMFORT],
                            ),
                            HailVehicleClassOptionV2(
                                vehicle_class=VehicleClass.LUXURY,
                                vehicle_class_name=(
                                    vehicle_class_configs[
                                        VehicleClass.LUXURY
                                    ].name_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                vehicle_class_description=(
                                    vehicle_class_configs[
                                        VehicleClass.LUXURY
                                    ].description_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                seats_count=vehicle_class_configs[
                                    VehicleClass.LUXURY
                                ].seats_count
                                or 6,
                                luggage_count=vehicle_class_configs[
                                    VehicleClass.LUXURY
                                ].luggage_count
                                or 4,
                                sub_options=class_fleet_map[VehicleClass.LUXURY],
                            ),
                            HailVehicleClassOptionV2(
                                vehicle_class=VehicleClass.FIVE_SEATER,
                                vehicle_class_name=(
                                    vehicle_class_configs[
                                        VehicleClass.FIVE_SEATER
                                    ].name_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                vehicle_class_description=(
                                    vehicle_class_configs[
                                        VehicleClass.FIVE_SEATER
                                    ].description_for_language(
                                        language=create_hail_option_matrix_request.language
                                    )
                                ),
                                seats_count=vehicle_class_configs[
                                    VehicleClass.FIVE_SEATER
                                ].seats_count
                                or 5,
                                luggage_count=vehicle_class_configs[
                                    VehicleClass.FIVE_SEATER
                                ].luggage_count
                                or 2,
                                sub_options=[
                                    HailSubOption(**standard_fare_calculation.model_dump())
                                ],
                            ),
                        ]
                        if operating_area is not MeterOperatingArea.LANTAU
                        and (
                            set(available_operating_areas)
                            & {MeterOperatingArea.URBAN, MeterOperatingArea.NT}
                        )
                        else []
                    ),
                ]

                self.logger_service.log(
                    f"Adding hail vehicle classes for {operating_area}",
                    {"hail_vehicle_classes": len(hail_vehicle_classes)},
                )
                operating_area_options[operating_area] = hail_vehicle_classes

        return HailOptionMatrixV2(
            distance_meters=trip_estimation.distance_meters,
            duration_seconds=trip_estimation.duration_seconds,
            encoded_polyline=trip_estimation.encoded_polyline,
            origin_place_details=trip_estimation.origin_place_details,
            destination_place_details=trip_estimation.destination_place_details,
            operating_area_options=operating_area_options,
            tunnel_fees=estimated_tunnel_fares,
            applicable_campaigns=applicable_campaigns,
            applicable_discounts=create_hail_option_matrix_request.applicable_discounts,
        )

    async def __generate_fleet_hail_option_matrix(
        self,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
        auth: Auth,
    ) -> HailFleetOptionMatrixV2:
        # Get trip estimation, fleet quote response, and hail configuration concurrently
        trip_estimation, fleet_quote_response, hail_config = await asyncio.gather(
            self.location_service.get_trip_estimation(
                origin_place_id=create_hail_option_matrix_request.place_ids[0],
                destination_place_id=create_hail_option_matrix_request.place_ids[1],
                language=create_hail_option_matrix_request.language,
                auth=auth,
            ),
            self.fleet_quote_service.get_fleet_quote(
                auth=auth,
                price_ids=create_hail_option_matrix_request.place_ids,
                time=create_hail_option_matrix_request.time,
                language=create_hail_option_matrix_request.language,
            ),
            self.configuration_repository.get_hail_configuration(),
        )
        
        applicable_campaigns = (
            create_hail_option_matrix_request.applicable_campaigns
            if create_hail_option_matrix_request.applicable_campaigns
            else ApplicableCampaigns.model_validate({})
        )

        # Convert fleet quote response to vehicle class options with price calculations
        fleet_vehicle_class_options = self.fleet_quote_converter_service.convert_fleet_quote_to_vehicle_class_options(
            fleet_quote_response=fleet_quote_response,
            language=LocalizationLanguage(create_hail_option_matrix_request.language),
            discount_rules=create_hail_option_matrix_request.discount_rules_to_apply,
            datetime=create_hail_option_matrix_request.time,
            origin=trip_estimation.origin_place_details,
            destination=trip_estimation.destination_place_details,
            hail_config=hail_config,
        )

        matrix =  HailFleetOptionMatrixV2(
            fleet_vehicle_class_options=fleet_vehicle_class_options,
            distance_meters=trip_estimation.distance_meters,
            duration_seconds=trip_estimation.duration_seconds,
            encoded_polyline=trip_estimation.encoded_polyline,
            origin_place_details=trip_estimation.origin_place_details,
            destination_place_details=trip_estimation.destination_place_details,
            tunnel_fees=[],
            applicable_campaigns=applicable_campaigns,
            applicable_discounts=create_hail_option_matrix_request.applicable_discounts,
        )
        
        self.logger_service.log(
            "Fleet Hail Option Matrix",
            extra={"matrix": json.loads(matrix.model_dump_json())},
        )
        
        return matrix

    async def clear_option_matrix_cache(
        self,
        version: int,
        create_hail_option_matrix_request: CreateHailOptionMatrixRequest,
    ) -> None:
        """
        Clear the option matrix cache for the given request
        """
        cache_key = self.__option_matrix_key(version, create_hail_option_matrix_request)
        # Use the redis_service that's already available in this class
        if await self.redis_service.keyExists(cache_key):
            # We need to use the redis instance directly since RedisService doesn't have delete
            from app.persistence.redis.redis import redis
            await redis.delete(cache_key)
