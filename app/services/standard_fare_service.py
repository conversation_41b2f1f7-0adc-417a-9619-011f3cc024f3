from datetime import datetime
from decimal import Decimal
import json
from typing import Any

from json_logic import jsonLogic
from app.contracts.common.dash_transaction_fees import DashTransactionFees
from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.common.trip_itinerary import TripItinerary
from app.contracts.location.trip_itinerary_estimation import TripItineraryEstimation
from app.models.auth import Auth
from app.models.discount_rules import DiscountRules
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.price_rule import PriceRule, FeeType
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.entities.hail_configuration import DATE_STRING_FORMAT, HailConfiguration
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.services.geo_spacial_service import GeoSpatialService
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.services.fare_estimation_service import FareEstimationService
from app.services.geo_service import GeoService

from app.utils.conversion_utils import ConversionUtils
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.location_exception_builder import LocationExceptionBuilder


class StandardFareService:
    logger_service: LoggerService
    location_service: LocationService
    geo_service: GeoService
    fare_estimation_service: FareEstimationService
    configuration_repository: ConfigurationsRepository
    geo_spacial_service: GeoSpatialService

    def __init__(
        self,
        logger_service: LoggerService,
        location_service: LocationService,
        geo_service: GeoService,
        configuration_repository: ConfigurationsRepository,
        fare_estimation_service: FareEstimationService,
        geo_spacial_service: GeoSpatialService,
    ) -> None:
        self.logger_service = logger_service
        self.location_service = location_service
        self.geo_service = geo_service
        self.configuration_repository = configuration_repository
        self.fare_estimation_service = fare_estimation_service
        self.geo_spacial_service = geo_spacial_service

    async def get_estimated_base_fare(
        self,
        trip_estimation: TripItineraryEstimation,
        operating_area: MeterOperatingArea,
    ) -> Decimal:
        is_within_lantau = operating_area == MeterOperatingArea.LANTAU

        estimated_base_fare = self.fare_estimation_service.base_fare_estimation(
            google_duration=trip_estimation.duration_seconds,
            google_distance=trip_estimation.distance_meters,
            origin_pin=trip_estimation.origin_place_details,
            destination_pin=trip_estimation.destination_place_details,
            operating_area=(
                MeterOperatingArea.LANTAU
                if is_within_lantau
                else MeterOperatingArea.URBAN
            ),
        )

        return estimated_base_fare

    async def get_estimated_tunnel_fare(
        self,
        trip_estimation: TripItineraryEstimation,
        is_double_tunnel_fee: bool = False,
    ) -> Decimal:
        self.logger_service.debug(f"StandardFareService/get_estimated_tunnel_fare-start", {
            "trip_estimation": trip_estimation.model_dump(),
            "is_double_tunnel_fee": is_double_tunnel_fee,
        })

        hail_config = await self.configuration_repository.get_hail_configuration()

        estimated_tunnel_fares = (
            (
                self.fare_estimation_service.tunnel_fare_estimation(
                    route=trip_estimation.encoded_polyline, hail_config=hail_config
                )
            )
            if trip_estimation.encoded_polyline
            else []
        )
        estimated_tunnel_fare: Decimal = sum(
            [
                (
                    fare.fee * 2
                    if is_double_tunnel_fee
                    and fare.is_double_fee_applicable
                    else fare.fee
                )
                for fare in estimated_tunnel_fares
            ],
            Decimal(0),
        )
        return estimated_tunnel_fare

    def get_standard_input_values(
        self,
        trip_estimation: TripItineraryEstimation,
        estimated_base_fare: Decimal,
        hail_config: HailConfiguration,
        dash_transaction_fees: DashTransactionFees,
        boost_amount: float = 0.0,
        vehicle_class: VehicleClass = VehicleClass.FOUR_SEATER,
    ) -> dict[str, Any]:

        hk_datetime = DateTimeUtils.set_to_hk_tz(datetime.now())
        time_of_day = hk_datetime.hour
        current_time = hk_datetime.date()

        origin_area = self.geo_spacial_service.find_lantau_area_by_pin(
            trip_estimation.origin_place_details)
        destination_area = self.geo_spacial_service.find_lantau_area_by_pin(
            trip_estimation.destination_place_details)

        inputValues = {
            "fare": float(estimated_base_fare),
            "time_hour": time_of_day,
            "date": current_time.strftime(DATE_STRING_FORMAT),
            "day_of_week": current_time.weekday(),
            "public_holidays": hail_config.public_holidays,
            "dash_fee_rate": dash_transaction_fees.dash_fee_rate,
            "dash_fee_constant": dash_transaction_fees.dash_fee_constant,
            "boost_amount": boost_amount,
            "origin": origin_area,
            "destination": destination_area,
            "vehicle_class": vehicle_class.value,
        }

        return inputValues

    def get_standard_price_engine(
        self,
        fare: float,
        tunnel_fee: float,
        hail_config: HailConfiguration,
        operating_area: MeterOperatingArea,
        discount_rules: DiscountRules | None = None,
    ) -> Any:

        dash_booking_fee = float(hail_config.dash_booking_fee)
        if operating_area == MeterOperatingArea.LANTAU:
            additional_booking_fee_markup_logic = (
                json.loads(hail_config.lantau_additional_booking_fee)
                if isinstance(hail_config.lantau_additional_booking_fee, str)
                else hail_config.lantau_additional_booking_fee
            )
        else:
            additional_booking_fee_markup_logic = (
                json.loads(hail_config.additional_booking_fee)
                if isinstance(hail_config.additional_booking_fee, str)
                else hail_config.additional_booking_fee
            )

        sub_total = {
            "+": [
                fare,
                tunnel_fee,
                dash_booking_fee,
                additional_booking_fee_markup_logic,
                {"var": "boost_amount"},
            ]
        }

        dash_transaction_fee = {
            "+": [
                {"*": [{"var": "dash_fee_rate"}, sub_total]},
                {"var": "dash_fee_constant"},
            ]
        }

        total = {"+": [sub_total, dash_transaction_fee]}

        third_party_discount_rules = (
            json.loads(discount_rules.discount_rules_third_party)
            if discount_rules
            and isinstance(discount_rules.discount_rules_third_party, str)
            else 0
        )
        dash_discount_rules = (
            json.loads(discount_rules.discount_rules_dash)
            if discount_rules and isinstance(discount_rules.discount_rules_dash, str)
            else 0
        )
        discount = {
            "+": [
                third_party_discount_rules,
                dash_discount_rules,
            ]
        }

        total_discount = {"min": [total, {"*": [discount, -1]}]}
        discounted_total = {
            "max": [
                0,
                {"+": [total, discount]},
            ]
        }

        priceEngine: Any = {
            "if": [
                True,
                [
                    fare,
                    tunnel_fee,
                    dash_booking_fee,
                    additional_booking_fee_markup_logic,
                    {"var": "boost_amount"},
                    sub_total,
                    dash_transaction_fee,
                    total,
                    total_discount,
                    discounted_total,
                ],
            ]
        }

        return priceEngine

    def apply_price_rules_sequentially(
        self,
        initial_fare_calculation: FareCalculation,
        price_rules: list[PriceRule],
        input_values: dict[str, Any],
    ) -> FareCalculation:
        """
        Apply price rules one by one to the initial fare calculation.
        Each price rule is applied sequentially, updating the fare calculation.
        """
        # Create a new FareCalculation object with the same values to avoid modifying the original
        current_fare_calculation = FareCalculation(
            estimated_fare_fee=initial_fare_calculation.estimated_fare_fee,
            estimated_tunnel_fee=initial_fare_calculation.estimated_tunnel_fee,
            fleet_booking_fee=initial_fare_calculation.fleet_booking_fee,
            dash_booking_fee=initial_fare_calculation.dash_booking_fee,
            additional_booking_fee=initial_fare_calculation.additional_booking_fee,
            boost_amount=initial_fare_calculation.boost_amount,
            sub_total=initial_fare_calculation.sub_total,
            dash_transaction_fee=initial_fare_calculation.dash_transaction_fee,
            total=initial_fare_calculation.total,
            discount=initial_fare_calculation.discount,
            discounted_total=initial_fare_calculation.discounted_total,
            transaction_fees=initial_fare_calculation.transaction_fees,
        )

        for price_rule in price_rules:
            try:
                # Parse the JSON rule from the price rule
                rule_logic = (
                    json.loads(price_rule.json_rule)
                    if isinstance(price_rule.json_rule, str)
                    else price_rule.json_rule
                )

                # Update input values with current fare calculation values for the next rule application
                updated_input_values = {
                    **input_values,
                    "fare": float(current_fare_calculation.estimated_fare_fee),
                    "tunnel_fee": float(current_fare_calculation.estimated_tunnel_fee),
                    "dash_booking_fee": float(current_fare_calculation.dash_booking_fee),
                    "additional_booking_fee": float(current_fare_calculation.additional_booking_fee),
                    "boost_amount": float(current_fare_calculation.boost_amount),
                    "sub_total": float(current_fare_calculation.sub_total),
                    "dash_transaction_fee": float(current_fare_calculation.dash_transaction_fee),
                    "total": float(current_fare_calculation.total),
                    "discount": float(current_fare_calculation.discount),
                    "discounted_total": float(current_fare_calculation.discounted_total),
                }

                # Apply the price rule logic
                rule_result = jsonLogic(rule_logic, updated_input_values)

                # Update the fare calculation based on the fare type
                if price_rule.fee_type == FeeType.FARE_ADJUSTMENT and isinstance(rule_result, list):
                    current_fare_calculation.fixed_price = ConversionUtils.float_to_decimal(
                        rule_result[0])
                    current_fare_calculation.fare_type = rule_result[1]
                elif price_rule.fee_type == FeeType.ADDITIONAL_BOOKING_FEE and isinstance(rule_result, (int, float)):
                    current_fare_calculation.additional_booking_fee = ConversionUtils.float_to_decimal(
                        rule_result)
                elif price_rule.fee_type == FeeType.DASH_TRANSACTION_FEE and isinstance(rule_result, (int, float)):
                    current_fare_calculation.dash_transaction_fee = ConversionUtils.float_to_decimal(
                        rule_result)
                elif price_rule.fee_type == FeeType.DASH_BOOKING_FEE and isinstance(rule_result, (int, float)):
                    current_fare_calculation.dash_booking_fee = ConversionUtils.float_to_decimal(
                        rule_result)

                # Recalculate dependent values after applying the rule
                fare = current_fare_calculation.fixed_price or current_fare_calculation.estimated_fare_fee

                current_fare_calculation.sub_total = (
                    fare +
                    current_fare_calculation.estimated_tunnel_fee +
                    current_fare_calculation.dash_booking_fee +
                    current_fare_calculation.additional_booking_fee +
                    current_fare_calculation.boost_amount
                )

                current_fare_calculation.total = current_fare_calculation.sub_total + \
                    current_fare_calculation.dash_transaction_fee

                current_fare_calculation.discounted_total = max(
                    Decimal(0),
                    current_fare_calculation.total + current_fare_calculation.discount
                )

                self.logger_service.debug(f"StandardFareService/apply_price_rules_sequentially", {
                    "price_rule_fee_type": price_rule.fee_type.value,
                    "rule_result": rule_result,
                    "updated_fare_calculation": current_fare_calculation.model_dump(),
                })

            except Exception as e:
                self.logger_service.error(f"StandardFareService/apply_price_rules_sequentially-error", {
                    "price_rule": price_rule.model_dump(),
                    "error": str(e),
                })
                # Continue with the next rule if one fails
                continue

        return current_fare_calculation

    async def get_min_max_fare_calculation_by_vehicle_classes(
        self,
        auth: Auth,
        place_ids: list[str],
        language: LocalizationLanguage,
        price_rules: list[PriceRule],
        is_double_tunnel_fee: bool = False,
        boost_amount: float = 0.0,
        discount_rules: DiscountRules | None = None,
        vehicle_classes: list[VehicleClass] = list(VehicleClass),
    ) -> tuple[FareCalculation, FareCalculation]:
        fare_calculations = await self.get_fare_calculation_by_vehicle_classes(
            auth=auth,
            place_ids=place_ids,
            language=language,
            price_rules=price_rules,
            is_double_tunnel_fee=is_double_tunnel_fee,
            boost_amount=boost_amount,
            discount_rules=discount_rules,
            vehicle_classes=vehicle_classes
        )
        min_fare = min(fare_calculations.values(), key=lambda fare: fare.total)
        max_fare = max(fare_calculations.values(), key=lambda fare: fare.total)
        return min_fare, max_fare

    async def get_fare_calculation_by_vehicle_classes(
        self,
        auth: Auth,
        place_ids: list[str],
        language: LocalizationLanguage,
        price_rules: list[PriceRule],
        is_double_tunnel_fee: bool = False,
        boost_amount: float = 0.0,
        discount_rules: DiscountRules | None = None,
        vehicle_classes: list[VehicleClass] = list(VehicleClass),
    ) -> dict[VehicleClass, FareCalculation]:
        self.logger_service.debug(f"StandardFareService/get_fare_calculation_by_vehicle_classes-start", {
            "place_ids": place_ids,
            "language": language,
            "is_double_tunnel_fee": is_double_tunnel_fee,
            "boost_amount": boost_amount,
            "discount_rules": discount_rules.model_dump() if discount_rules else None,
            "vehicle_classes": [vehicle_class.value for vehicle_class in vehicle_classes],
            "price_rule": price_rules,
        })
        fare_calculations: dict[VehicleClass, FareCalculation] = {}
        for vehicle_class in vehicle_classes:
            fare_calculations[vehicle_class] = await self.get_fare_calculation(
                auth=auth,
                place_ids=place_ids,
                language=language,
                is_double_tunnel_fee=is_double_tunnel_fee,
                boost_amount=boost_amount,
                price_rules=price_rules,
                discount_rules=discount_rules,
                vehicle_class=vehicle_class,
            )
        return fare_calculations

    def compute_fare_calculation(
        self,
        trip_estimation: TripItineraryEstimation,
        estimated_base_fare: Decimal,
        estimated_tunnel_fare: Decimal,
        hail_config: HailConfiguration,
        dash_transaction_fees: DashTransactionFees,
        operating_area: MeterOperatingArea,
        price_rules: list[PriceRule],
        discount_rules: DiscountRules | None = None,
        boost_amount: float = 0.0,
        vehicle_class: VehicleClass = VehicleClass.FOUR_SEATER,
    ) -> FareCalculation:
        inputValues = self.get_standard_input_values(
            trip_estimation,
            estimated_base_fare,
            hail_config,
            dash_transaction_fees,
            boost_amount,
            vehicle_class
        )

        priceEngine = self.get_standard_price_engine(
            fare=float(estimated_base_fare),
            tunnel_fee=float(estimated_tunnel_fare),
            hail_config=hail_config,
            operating_area=operating_area,
            discount_rules=discount_rules,
        )

        initial_results: Any = jsonLogic(priceEngine, inputValues)

        # Create initial FareCalculation object from jsonLogic results
        initial_fare_calculation = FareCalculation(
            estimated_fare_fee=ConversionUtils.float_to_decimal(
                initial_results[0]),
            estimated_tunnel_fee=ConversionUtils.float_to_decimal(
                initial_results[1]),
            fleet_booking_fee=Decimal(0),
            dash_booking_fee=ConversionUtils.float_to_decimal(
                initial_results[2]),
            additional_booking_fee=ConversionUtils.float_to_decimal(
                initial_results[3]),
            boost_amount=ConversionUtils.float_to_decimal(initial_results[4]),
            sub_total=ConversionUtils.float_to_decimal(initial_results[5]),
            dash_transaction_fee=ConversionUtils.float_to_decimal(
                initial_results[6]),
            total=ConversionUtils.float_to_decimal(initial_results[7]),
            discount=ConversionUtils.float_to_decimal(initial_results[8]),
            discounted_total=ConversionUtils.float_to_decimal(
                initial_results[9]),
            transaction_fees=dash_transaction_fees,
        )

        # Apply price rules sequentially to get final fare calculation
        fare_calculation = self.apply_price_rules_sequentially(
            initial_fare_calculation=initial_fare_calculation,
            price_rules=price_rules,
            input_values=inputValues,
        )

        return fare_calculation

    async def get_fare_calculation(
        self,
        auth: Auth,
        place_ids: list[str],
        language: LocalizationLanguage,
        price_rules: list[PriceRule],
        is_double_tunnel_fee: bool = False,
        boost_amount: float = 0.0,
        discount_rules: DiscountRules | None = None,
        vehicle_class: VehicleClass = VehicleClass.FOUR_SEATER,
    ) -> FareCalculation:
        self.logger_service.debug(f"StandardFareService/get_fare_calculation-start", {
            "place_ids": place_ids,
            "language": language,
            "boost_amount": boost_amount,
            "discount_rules": discount_rules.model_dump() if discount_rules else None,
            "price_rules": price_rules,
        })
        trip_estimation = await self.location_service.get_trip_estimation(
            origin_place_id=place_ids[0],
            destination_place_id=place_ids[1],
            language=language,
            auth=auth,
        )
        operating_area = self.geo_service.get_operating_area_for_trip(
            origin=trip_estimation.origin_place_details,
            destination=trip_estimation.destination_place_details,
        )

        if operating_area is None:
            raise LocationExceptionBuilder().location_outside_of_service_area()

        hail_config = await self.configuration_repository.get_hail_configuration()
        estimated_base_fare = await self.get_estimated_base_fare(trip_estimation, operating_area)
        estimated_tunnel_fare = await self.get_estimated_tunnel_fare(
            trip_estimation,
            is_double_tunnel_fee
        )

        dash_transaction_fees = (
            hail_config.dash_fee_configuration.dash_fees.get(
                operating_area, hail_config.dash_fee_configuration.common
            )
        )

        fare_calculation = self.compute_fare_calculation(
            trip_estimation,
            estimated_base_fare,
            estimated_tunnel_fare,
            hail_config,
            dash_transaction_fees,
            operating_area,
            price_rules,
            discount_rules,
            boost_amount,
            vehicle_class,
        )

        return fare_calculation
