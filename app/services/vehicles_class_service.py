from enum import StrEnum
from app.models.meter_operating_area import MeterOperatingArea
from app.models.vehicle_class import VehicleClass


class MultiMeterOperatingArea(StrEnum):
    NT = "NT"
    URBAN = "URBAN"
    LANTAU = "LANTAU"
    NT_AND_URBAN = "NT_AND_URBAN"
    URBAN_AND_LANTAU = "URBAN_AND_LANTAU"


class VehiclesClassService:
    image_map_by_operating_area_and_vehicle_class: dict[
        MultiMeterOperatingArea, dict[VehicleClass, str]
    ] = {
        MultiMeterOperatingArea.URBAN: {
            VehicleClass.FOUR_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban.png?alt=media&token=b3892f98-63f2-4d2f-9114-7d9b809378f4",
            VehicleClass.FIVE_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban.png?alt=media&token=b3892f98-63f2-4d2f-9114-7d9b809378f4",
            VehicleClass.COMFORT: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fcomfort_urban.png?alt=media&token=e062dec4-80b5-41fe-9b47-a689addda986",
            VehicleClass.LUXURY: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fluxury_urban.png?alt=media&token=85fab3be-c7c4-4281-8443-4ae7efefb177",
        },
        MultiMeterOperatingArea.NT: {
            VehicleClass.FOUR_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_nt.png?alt=media&token=c67b0d0d-e426-44b7-9023-c762dc48b95b",
            VehicleClass.FIVE_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_nt.png?alt=media&token=c67b0d0d-e426-44b7-9023-c762dc48b95b",
            VehicleClass.COMFORT: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fcomfort_nt.png?alt=media&token=38de2869-7a73-4276-8254-a8a9853e02b7",
            VehicleClass.LUXURY: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fluxury_nt.png?alt=media&token=b176907e-6cdb-46ae-a900-0962d340d3f0",
        },
        MultiMeterOperatingArea.LANTAU: {
            VehicleClass.FOUR_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_lantau.png?alt=media&token=5ca0ec6c-2f36-4edf-8282-45e31b5956da",
            VehicleClass.FIVE_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_lantau.png?alt=media&token=5ca0ec6c-2f36-4edf-8282-45e31b5956da",
            VehicleClass.COMFORT: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fcomfort_lantau.png?alt=media&token=064313b0-bd00-440e-9b08-2669be677456",
            VehicleClass.LUXURY: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fluxury_lantau.png?alt=media&token=091afc95-c99b-4929-b159-642591571eb3"
        },
        MultiMeterOperatingArea.NT_AND_URBAN: {
            VehicleClass.FOUR_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban_nt.png?alt=media&token=aec73cac-bac8-41f5-84a7-7b2271a561b2",
            VehicleClass.FIVE_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban_nt.png?alt=media&token=aec73cac-bac8-41f5-84a7-7b2271a561b2",
            VehicleClass.COMFORT: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fcomfort_urban_nt.png?alt=media&token=2e4dff6c-b976-49c5-bd1a-3f5686a53aa5",
            VehicleClass.LUXURY: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fluxury_urban_nt.png?alt=media&token=55f0979f-0e4c-4ed5-873b-d029a3ac2c6e"
        },
        MultiMeterOperatingArea.URBAN_AND_LANTAU: {
            VehicleClass.FOUR_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban_lantau.png?alt=media&token=cc0e9a50-3e25-45a6-84e9-f6b423e2ea5f",
            VehicleClass.FIVE_SEATER: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fstandard_urban_lantau.png?alt=media&token=cc0e9a50-3e25-45a6-84e9-f6b423e2ea5f",
            VehicleClass.COMFORT: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fcomfort_urban_lantau.png?alt=media&token=c6159162-814e-438d-820f-d7925090e520",
            VehicleClass.LUXURY: "https://firebasestorage.googleapis.com/v0/b/dash-prod-a345a.appspot.com/o/vehicle_models%2Fluxury_urban_lantau.png?alt=media&token=c707bd6c-21b1-4e20-a5c6-a0db4d2437e7"
        }
    }

    @staticmethod
    def get_vehicle_image_by_operating_areas_and_vehicle_class(
        operating_areas: list[MeterOperatingArea], vehicle_class: VehicleClass
    ) -> str | None:
        operating_area: MultiMeterOperatingArea
        if len(operating_areas) == 1:
            operating_area = MultiMeterOperatingArea(operating_areas[0].value)
        elif MeterOperatingArea.URBAN in operating_areas and MeterOperatingArea.LANTAU in operating_areas:
            operating_area = MultiMeterOperatingArea.URBAN_AND_LANTAU
        elif MeterOperatingArea.URBAN in operating_areas and MeterOperatingArea.NT in operating_areas:
            operating_area = MultiMeterOperatingArea.NT_AND_URBAN

        return VehiclesClassService.image_map_by_operating_area_and_vehicle_class.get(
            operating_area, {}
        ).get(vehicle_class)
