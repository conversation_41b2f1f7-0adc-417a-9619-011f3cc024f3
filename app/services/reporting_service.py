import sentry_sdk
from sentry_sdk.integrations.argv import ArgvIntegration
from sentry_sdk.integrations.atexit import AtexitIntegration
from sentry_sdk.integrations.dedupe import DedupeIntegration
from sentry_sdk.integrations.modules import ModulesIntegration
from sentry_sdk.integrations.stdlib import StdlibIntegration
from sentry_sdk.integrations.threading import ThreadingIntegration

# from sentry_sdk.integrations.excepthook import ExcepthookIntegration
from app.infrastructure.settings import settings


class ReportingService:

    def initialize(self) -> None:
        """
        Initialize the reporting SDKs.
        """

        if not settings.is_local:
            sentry_sdk.init(
                dsn=settings.sentry_dsn,
                traces_sample_rate=0.5 if settings.is_production else 0.2,
                profiles_sample_rate=0.5 if settings.is_production else 0.2,
                _experiments={
                    "continuous_profiling_auto_start": True,
                },
                environment=settings.env,
                default_integrations=False,
                integrations=[
                    ArgvIntegration(),
                    AtexitIntegration(),
                    DedupeIntegration(),
                    ModulesIntegration(),
                    StdlibIntegration(),
                    ThreadingIntegration(),
                ],
            )
