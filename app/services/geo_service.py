import time
from shapely.geometry import Point, Polygon
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Tuple, Set
from app.contracts.common.pin import Pin
from app.models.meter_operating_area import MeterOperatingArea
from app.models.regions import Region
from app.services.logger_service import LoggerService

DriverLocation = Tuple[str, Tuple[float, float]]

HK_SAR_POLYGON_COORDS: list[tuple[float, float]] = [
    (114.0296439, 22.5095587),
    (113.944804, 22.5087289),
    (113.9341727, 22.4507761),
    (113.8451975, 22.3504254),
    (113.8146963, 22.1960674),
    (114.2980947, 22.1420172),
    (114.4299307, 22.3904758),
    (114.340866, 22.5555403),
    (114.1619948, 22.5634666),
    (114.090927, 22.5384179),
    (114.0296439, 22.5095587),
]
REG<PERSON>_POLYGON_COORDS: dict[Region, list[tuple[float, float]]] = {
    Region.HK_ISLAND: [
        (114.109323, 22.27347487526304),
        (114.204617, 22.18916953912558),
        (114.265329, 22.20263602285363),
        (114.258061, 22.27087754667184),
        (114.213956, 22.29537693052391),
        (114.196398, 22.29630914860556),
        (114.187092, 22.28718628774916),
        (114.170363, 22.28619205890749),
        (114.148189, 22.29513607986719),
        (114.125871, 22.28805456538764),
        (114.109323, 22.27347487526304),
    ],
    Region.NEW_TERRITORIES: [
        (114.0087605, 22.3615054),
        (114.1409398, 22.4221358),
        (114.1951848, 22.4227705),
        (114.2284871, 22.3948395),
        (114.2401601, 22.3287985),
        (114.2583562, 22.3297513),
        (114.2792989, 22.3046604),
        (114.2823888, 22.2585957),
        (114.4207478, 22.2592312),
        (114.4020014, 22.5908323),
        (113.9817743, 22.5470821),
        (113.883584, 22.3967041),
        (113.9295893, 22.3490822),
        (114.0087605, 22.3615054),
    ],
    Region.LANTAU: [
        (113.8273145, 22.1850197),
        (114.0219785, 22.2152175),
        (114.0302182, 22.2959251),
        (114.0817166, 22.3178416),
        (114.0453244, 22.3537263),
        (113.8386441, 22.3305452),
        (113.8273145, 22.1850197),
    ],
}


class GeoService:
    __hk_sar_polygon: Polygon
    __region_polygons: dict[Region, Polygon]
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.logger_service = logger_service
        self.__hk_sar_polygon = Polygon(HK_SAR_POLYGON_COORDS)
        self.__region_polygons = {}
        for region, coords in REGION_POLYGON_COORDS.items():
            self.__region_polygons[region] = Polygon(coords)

    def __buffer_in_degrees(self, lat: float, radius_km: float) -> Tuple[float, float]:
        """
        Function to calculate buffer in degrees (approximate)
        """

        from math import cos, radians

        # Approximate radius of the Earth in km
        earth_radius_km = 6371.0

        # Convert km to degrees
        lat_radius = radius_km / earth_radius_km * (180.0 / 3.14159)
        lng_radius = (
            radius_km / (earth_radius_km * cos(radians(lat))) * (180.0 / 3.14159)
        )

        return lat_radius, lng_radius

    def __find_matching_region_polygon(
        self, point: Point
    ) -> tuple[Region, Polygon] | None:
        """
        Find the polygon if any that contains the given point
        """
        for region, polygon in self.__region_polygons.items():
            if polygon.contains(point):
                self.logger_service.debug(f"Point is in {region}")
                return (region, polygon)
        return None

    def __is_within_hk_sar(self, origin: Pin, destination: Pin) -> bool:
        """
        Check if the origin and destination are within the HK SAR
        """
        origin_point = Point(origin.lng, origin.lat)
        destination_point = Point(destination.lng, destination.lat)

        return self.__hk_sar_polygon.contains(
            origin_point
        ) and self.__hk_sar_polygon.contains(destination_point)

    def get_operating_area_for_trip(
        self, origin: Pin, destination: Pin
    ) -> MeterOperatingArea | None:
        """
        Get the operating area for the trip based on the origin and destination
        """
        if self.is_within_region(
            origin=origin,
            destination=destination,
            region=Region.LANTAU,
        ):
            return MeterOperatingArea.LANTAU
        elif self.is_within_region(
            origin=origin,
            destination=destination,
            region=Region.NEW_TERRITORIES,
        ):
            return MeterOperatingArea.NT
        elif self.__is_within_hk_sar(origin=origin, destination=destination):
            return MeterOperatingArea.URBAN
        else:
            return None

    def is_within_region(self, origin: Pin, destination: Pin, region: Region) -> bool:
        """
        Check if the origin and destination are within the same region
        """
        origin_point = Point(origin.lng, origin.lat)
        destination_point = Point(destination.lng, destination.lat)

        origin_region_polygon = self.__find_matching_region_polygon(origin_point)
        destination_region_polygon = self.__find_matching_region_polygon(
            destination_point
        )

        if origin_region_polygon is None or destination_region_polygon is None:
            return False

        return (
            origin_region_polygon[0] == region
            and destination_region_polygon[0] == region
        )

    def filter_drivers_by_region(
        self, start: Pin, driver_locations: List[DriverLocation], radius_km: float
    ) -> Set[str]:
        # Calculate the buffer in degrees
        _, lng_radius = self.__buffer_in_degrees(start.lat, radius_km)
        center_point = Point(start.lng, start.lat)

        # Create a buffer around the center point
        buffer_region = center_point.buffer(lng_radius, resolution=16)

        # Create a Polygon object
        starting_region_polygon = self.__find_matching_region_polygon(center_point)

        if starting_region_polygon is None:
            return set()

        # Compute the intersection between the buffer and the polygon
        intersection_region = buffer_region.intersection(starting_region_polygon[1])

        # Function to check if a point is inside the intersection region
        def check_point(driver_location: DriverLocation) -> str | None:
            return (
                driver_location[0]
                if intersection_region.contains(
                    Point(driver_location[1][0], driver_location[1][1])
                )
                else None
            )

        start_time = time.time()

        inside_points = set()

        # Use ThreadPoolExecutor to check points in parallel
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_point = {
                executor.submit(check_point, user_location): user_location
                for user_location in driver_locations
            }

            # Collect results as they complete
            for future in as_completed(future_to_point):
                result = future.result()
                if result:
                    inside_points.add(result)

        end_time = time.time()
        elapsed_time = end_time - start_time
        self.logger_service.debug(
            f"Time taken for point-region intersection: {elapsed_time * 1000} ms"
        )

        return inside_points
