from google.cloud import pubsub_v1  # type: ignore

from app.contracts.base_contract import BaseContract
from app.contracts.pub_sub.publish_push_notification import PublishPushNotification
from app.contracts.tx.hail_order_request_timeout import HailOrderRequestTimeout
from app.infrastructure.settings import settings
from app.models.heartbeat_processing import HeartbeatProcessing
from app.models.pub_sub_topic import PubSubTopic
from app.services.logger_service import LoggerService
from dash_proto.beam import hail_pb2  # type: ignore
from google.protobuf.json_format import MessageToJson


class PubSubPublisherService:
    publisher: pubsub_v1.PublisherClient
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.publisher = pubsub_v1.PublisherClient()
        self.logger_service = logger_service

    def publish_push_notification(
        self, publish_push_notification: PublishPushNotification
    ) -> None:
        self.__publish_contract(
            PubSubTopic.PUSH_NOTIFICATION_PROCESSING,
            publish_push_notification,
        )

    def publish_hail_order_request_timeout(
        self, order_request_timeout: HailOrderRequestTimeout
    ) -> None:
        self.__publish_contract(
            PubSubTopic.ADD_TX_EVENT_FOR_SYSTEM,
            order_request_timeout,
        )

    def publish_hail_bronze_topic(self, hail_bronze: hail_pb2.Hail) -> None:
        self.__publish_json(
            PubSubTopic.HAIL_BRONZE_TOPIC,
            MessageToJson(
                hail_bronze,
                preserving_proto_field_name=True,
            ),
        )

    def publish_heartbeat(self, heartbeat_processing: HeartbeatProcessing) -> None:
        heartbeat_json = heartbeat_processing.model_dump_json(by_alias=True)
        self.__publish_json(
            PubSubTopic.HEARTBEAT_CHANGE_PROCESSING,
            heartbeat_json,
        )

    def __publish_contract(self, topic: PubSubTopic, message: BaseContract) -> None:
        topic_path = self.publisher.topic_path(settings.project_id, topic)
        message_json = message.model_dump_json(by_alias=True)
        self.logger_service.log(
            f"Publishing message to {topic_path}", {"message": message_json}
        )
        future = self.publisher.publish(topic_path, data=message_json.encode("utf-8"))
        self.logger_service.debug(f"Published message result: {future.result()}")

    def __publish_json(self, topic: PubSubTopic, message: str) -> None:
        topic_path = self.publisher.topic_path(settings.project_id, topic)
        self.logger_service.log(
            f"Publishing message to {topic_path}", {"message": message}
        )
        future = self.publisher.publish(topic_path, data=message.encode("utf-8"))
        self.logger_service.debug(f"Published message result: {future.result()}")
