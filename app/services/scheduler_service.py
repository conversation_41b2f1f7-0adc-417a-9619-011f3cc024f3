import logging
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.persistence.redis.repositories.hail_repository import HailRepository


class SchedulerService:
    scheduler: AsyncIOScheduler
    hail_service: HailRepository

    def __init__(self, hail_service: HailRepository):
        self.hail_service = hail_service
        self.scheduler = AsyncIOScheduler()
        self.__register_jobs()
        app_scheduler_loggers = {
            "apscheduler.scheduler",
            "apscheduler.executors.default",
        }
        for logger in app_scheduler_loggers:
            logging.getLogger(logger).propagate = False
            logging.getLogger(logger).setLevel(logging.WARNING)

    def __register_jobs(self) -> None:
        self.scheduler.add_job(
            self.hail_service.notify_active_hails_of_driver_pin,
            IntervalTrigger(seconds=5),
        )

    def start(self) -> None:
        self.scheduler.start()

    def shutdown(self) -> None:
        self.scheduler.shutdown()
