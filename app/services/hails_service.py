import asyncio
from datetime import datetime
import json
import traceback
from app.contracts.campaigns.rule_params import RuleParams
from app.contracts.common.hail_charges import CancellationFeeBreakdown
from app.contracts.hails.hail_cancallation_fee_response import HailCancellationFeeResponse
from app.contracts.hails.hail_response import HailResponse
from app.services.merchant_campaign_service import MerchantCampaignService
from app.services.web_socket_message_service import WebSocketMessageService
from app.utils.datetime_utils import DateTimeUtils
from fastapi import (
    BackgroundTasks,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.websockets import WebSocketState
from pydantic import ValidationError
from websockets import ConnectionClosed
from app.contracts.hails.hail_boost_update_response import BoostUpdateResponse
from app.contracts.hails.hail_id import HailId
from app.contracts.hails.driver_hail_accepted_request import DriverHailAcceptedRequest
from app.contracts.hails.driver_hail_cancelled_request import DriverHailCancelledRequest
from app.contracts.hails.driver_hail_completed_request import DriverHailCompletedRequest
from app.contracts.hails.driver_hail_on_going_request import DriverHailOnGoingRequest
from app.contracts.hails.hail_driver_response import HailDriverResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.contracts.hails.rider_hail_boost_request import RiderHailBoostRequest
from app.contracts.hails.rider_hail_cancelled_request import RiderHailCancelledRequest
from app.contracts.hails.service_hail_completed_request import (
    ServiceHailCompletedRequest,
)
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from app.contracts.matrix.hail_fleet_option_martix_v2 import HailFleetOptionMatrixV2
from app.contracts.matrix.hail_option_matrix_v2 import HailOptionMatrixV2
from app.contracts.pub_sub.publish_push_notification import PublishPushNotification
from app.contracts.tx.hail_order_request_timeout import HailOrderRequestTimeout
from app.contracts.tx.hailing_merchant_pick_up_confirmed import (
    HailingMerchantPickUpConfirmed,
    HailingMerchantPickUpConfirmedContent,
)
from app.contracts.tx.hailing_user_updates_order import HailingUserUpdatesOrder
from app.models.auth import Auth
from app.models.hail_status import HailStatus
from app.models.meter_operating_area import MeterOperatingArea
from app.models.notification_recipient_type import NotificationRecipientType
from app.models.notification_trigger_event_type import NotificationTriggerEventType
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.firestore.repositories.firestore_repository import (
    FirestoreRepository,
)
from app.persistence.firestore.repositories.meter_repository import MeterRepository
from app.contracts.hails.create_hail_request import (
    CreateHailRequest,
)
from app.persistence.redis.entities.driver import Driver
from app.persistence.redis.entities.hail import Hail
from app.persistence.redis.redis import RedisService
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.services.campaign_service import CampaignService
from app.services.fare_calculation_service import FareCalculationService
from app.services.option_matrix_service import (
    OptionMatrixService,
)
from app.services.pub_sub_publisher_service import PubSubPublisherService
from app.services.tx_service import TxService
from app.services.logger_service import LoggerService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.sockets.messages.driver_eta_message import DriverEtaMessage
from app.sockets.messages.driver_heartbeat_message import DriverHeartbeatMessage
from app.sockets.messages.exception_message import ExceptionMessage
from app.sockets.messages.hail_boost_success_message import HailBoostSuccessMessage
from app.sockets.messages.hail_expired_message import HailExpiredMessage
from app.sockets.messages.hail_favorite_drivers_unavailable import (
    HailFavoriteDriversUnavailableMessage,
)
from app.services.routing_service import RoutingService
from app.sockets.messages.hail_not_available_message import HailNotAvailableMessage
from app.sockets.socket_service import SocketService
from app.utils.datetime_utils import DateTimeUtils
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.dash_exception_builder import DashExceptionBuilder
from app.utils.exceptions.driver_exception_builder import DriverExceptionBuilder
from app.utils.exceptions.hail_exception_builder import HailExceptionBuilder
from fastapi_controllers import Controller
from dependency_injector.wiring import inject

from app.utils.exceptions.meter_exception_builder import MeterExceptionBuilder


class HailsService(Controller):
    hail_repository: HailRepository
    driver_repository: DriverRepository
    configuration_repository: ConfigurationsRepository
    meter_repository: MeterRepository
    tx_service: TxService
    logger_service: LoggerService
    pub_sub_publisher_service: PubSubPublisherService
    option_matrix_service: OptionMatrixService
    redis_service: RedisService
    socket_service: SocketService
    firestore_repository: FirestoreRepository
    fare_calculation_service: FareCalculationService
    campaign_service: CampaignService
    routing_service: RoutingService
    web_socket_message_service: WebSocketMessageService

    @inject
    def __init__(
        self,
        hail_repository: HailRepository,
        driver_repository: DriverRepository,
        configuration_repository: ConfigurationsRepository,
        meter_repository: MeterRepository,
        tx_service: TxService,
        logger_service: LoggerService,
        pub_sub_publisher_service: PubSubPublisherService,
        option_matrix_service: OptionMatrixService,
        redis_service: RedisService,
        socket_service: SocketService,
        firestore_repository: FirestoreRepository,
        fare_calculation_service: FareCalculationService,
        campaign_service: CampaignService,
        routing_service: RoutingService,
        merchant_campaign_service: MerchantCampaignService,
        web_socket_message_service: WebSocketMessageService,
    ) -> None:
        self.hail_repository = hail_repository
        self.driver_repository = driver_repository
        self.configuration_repository = configuration_repository
        self.meter_repository = meter_repository
        self.tx_service = tx_service
        self.logger_service = logger_service
        self.pub_sub_publisher_service = pub_sub_publisher_service
        self.option_matrix_service = option_matrix_service
        self.redis_service = redis_service
        self.socket_service = socket_service
        self.firestore_repository = firestore_repository
        self.fare_calculation_service = fare_calculation_service
        self.campaign_service = campaign_service
        self.routing_service = routing_service
        self.merchant_campaign_service = merchant_campaign_service
        self.web_socket_message_service = web_socket_message_service

    async def get_hails_for_riders(
        self,
        user_id: str,
    ) -> list[HailRiderResponse]:
        """
        Get all the hails for the user that are yet to occur
        """
        hails = await self.hail_repository.get_hails_for_user(user_id)
        return [hail.to_hail_rider_response() for hail in hails]

    async def get_hails_for_drivers(
        self, driver: Driver | None = None
    ) -> list[HailDriverResponse]:
        """
        Get all hails matched with a driver
        """

        if driver is None:
            return []

        statuses = (
            {HailStatus.ACCEPTED, HailStatus.APPROACHING, HailStatus.CANCELLED}
            if driver.meter.is_dash_meter
            else {
                HailStatus.ACCEPTED,
                HailStatus.APPROACHING,
                HailStatus.ON_GOING,
                HailStatus.CANCELLED,
            }
        )
        hails = await self.hail_repository.get_matched_hails_for_driver(
            driver, statuses
        )

        return [hail.to_matched_driver_hail_response() for hail in hails]

    async def create_hail_request(
        self,
        auth: Auth,
        create_hail_request: CreateHailRequest,
        background_tasks: BackgroundTasks,
        user_id: str,
    ) -> HailResponse:
        options_matrix: HailOptionMatrixV2 | HailFleetOptionMatrixV2
        now = DateTimeUtils.utc_now_with_tz()

        rule_params = RuleParams.from_create_hail_request(create_hail_request)

        applicable_campaigns = await self.campaign_service.get_applicable_campaigns(
            auth=auth,
            rule_params=rule_params
        )

        applicable_merchant_campaign = await self.merchant_campaign_service.get_applicable_merchant_campaign(
            auth=auth,
            rule_params=rule_params
        )

        if create_hail_request.platform_type == "FLEET":
            options_matrix = await self.option_matrix_service.get_fleet_hail_option_matrix(
                create_hail_option_matrix_request=CreateHailOptionMatrixRequest(
                    place_ids=[
                        itinerary.place_id for itinerary in create_hail_request.itinerary
                    ],
                    time=create_hail_request.time,
                    language=create_hail_request.language,
                    payment_instrument_type=create_hail_request.payment_details.card_type,
                    applicable_discounts=create_hail_request.applicable_discounts,
                    applicable_campaigns=applicable_campaigns,
                    platform_type="FLEET",  # Add this to ensure correct type handling
                ),
                auth=auth,
            )
        else:
            options_matrix = await self.option_matrix_service.get_hail_option_matrix_v2(
                create_hail_option_matrix_request=CreateHailOptionMatrixRequest(
                    place_ids=[
                        itinerary.place_id for itinerary in create_hail_request.itinerary
                    ],
                    time=create_hail_request.time,
                    language=create_hail_request.language,
                    payment_instrument_type=create_hail_request.payment_details.card_type,
                    double_tunnel_fee=create_hail_request.double_tunnel_fee,
                    applicable_discounts=create_hail_request.applicable_discounts,
                    applicable_campaigns=applicable_campaigns,
                    platform_type="DASH",  # Add this to ensure correct type handling
                ),
                auth=auth,
            )

        existing_hail = await self.hail_repository.get_hail_by_id(
            create_hail_request.tx_id
        )
        if existing_hail is not None:
            raise HailExceptionBuilder().hail_already_exists(create_hail_request.tx_id)

        hail = await Hail.from_create_hail_request(
            user_id=user_id,
            create_hail_request=create_hail_request,
            phone_number=auth.phone_number,
            options_matrix=options_matrix,
            applicable_merchant_campaign=applicable_merchant_campaign,
        )

        hail_config = await self.configuration_repository.get_hail_configuration()
        filtered_driver_count_by_radius = (
            await self.driver_repository.get_driver_count_by_radius(
                hail,
                *hail_config.driver_search.km_radii_for_online_drivers_search,
            )
        )
        await hail.set_driver_count_by_radius(filtered_driver_count_by_radius)

        favorite_drivers_online_ids: set[str] = set()
        if create_hail_request.prioritize_favorite_drivers:
            favorite_driver_ids = (
                await self.firestore_repository.user_favorite_driver_repository(
                    user_id
                ).get_user_favorite_drivers()
            )

            if len(favorite_driver_ids) > 0:
                favorite_drivers_online_ids = (
                    await self.get_favorite_drivers_online_for_hail(
                        hail, favorite_driver_ids
                    )
                )
                favorite_drivers_online = (
                    await self.driver_repository.get_drivers_by_ids(
                        favorite_drivers_online_ids
                    )
                )
                await hail.set_favorite_drivers_online(favorite_drivers_online_ids)

                await self.driver_repository.set_drivers_for_hail(
                    hail, favorite_drivers_online, timestamp=now,
                )
            else:
                await hail.set_favorite_drivers_online(set())

        num_favorite_drivers_online = (
            len(favorite_drivers_online_ids)
            if favorite_drivers_online_ids is not None
            else 0
        )
        if hail.is_scheduled and hail.is_dash_hail:
            background_tasks.add_task(
                self.find_matching_drivers_for_scheduled_hail,
                hail,
                num_favorite_drivers_online,
                timestamp=now,
            )
        elif hail.is_live and hail.is_dash_hail:
            background_tasks.add_task(
                self.find_matching_drivers_for_live_hail,
                hail,
                num_favorite_drivers_online
            )

        self.logger_service.log(
            f"HAIL CREATED: {hail.id} requested by {hail.user_id}",
            {"hail": json.loads(hail.model_dump_json())},
        )

        self.pub_sub_publisher_service.publish_hail_bronze_topic(
            hail.to_hail_proto())

        return hail.to_hail_response()

    async def update_hail_by_riders(
        self,
        auth: Auth,
        hail_id: str,
        update_hail_request: RiderHailCancelledRequest,
    ) -> HailRiderResponse:
        async def update_hail_status() -> HailRiderResponse:
            hail = await self.hail_repository.get_hail_by_id(hail_id)

            if hail is None:
                raise HailExceptionBuilder().hail_not_found(hail_id)

            if not hail.can_be_updated:
                raise HailExceptionBuilder().hail_not_updatable(
                    hail_status=hail.status, hail_id=hail_id
                )

            if hail.user_id != auth.user_id:
                raise AuthExceptionBuilder().auth_forbidden_resource()

            match hail.status:
                case HailStatus.PENDING | HailStatus.ACCEPTED | HailStatus.APPROACHING | HailStatus.ARRIVED:
                    if update_hail_request.status == HailStatus.CANCELLED:
                        charges = await self.hail_repository.set_hail_to_cancelled(
                            hail,
                            auth,
                        )

                        if hail.matched_driver is not None:
                            if hail.is_live:
                                if charges.cancellation_fee > 0:
                                    self.pub_sub_publisher_service.publish_push_notification(
                                        PublishPushNotification(
                                            event=NotificationTriggerEventType.RT5_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LATE,
                                            recipient_type=NotificationRecipientType.MERCHANT,
                                            recipient_id=hail.matched_driver.driver_id,
                                        )
                                    )
                                else:
                                    self.pub_sub_publisher_service.publish_push_notification(
                                        PublishPushNotification(
                                            event=NotificationTriggerEventType.RT4_ACCEPTED_ORDER_CANCELLED_BY_RIDER_QUICKLY,
                                            recipient_type=NotificationRecipientType.MERCHANT,
                                            recipient_id=hail.matched_driver.driver_id,
                                        )
                                    )
                            elif hail.is_scheduled:
                                if hail.is_within_approaching_threshold:
                                    self.pub_sub_publisher_service.publish_push_notification(
                                        PublishPushNotification(
                                            event=NotificationTriggerEventType.P8_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                                            recipient_type=NotificationRecipientType.MERCHANT,
                                            recipient_id=hail.matched_driver.driver_id,
                                        )
                                    )
                                else:
                                    self.pub_sub_publisher_service.publish_push_notification(
                                        PublishPushNotification(
                                            event=NotificationTriggerEventType.P6_ACCEPTED_ORDER_CANCELLED_BY_RIDER_GREATER_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                                            recipient_type=NotificationRecipientType.MERCHANT,
                                            recipient_id=hail.matched_driver.driver_id,
                                        )
                                    )
                case _:
                    raise HailExceptionBuilder().hail_not_updatable(
                        hail_status=hail.status, hail_id=hail_id
                    )

            self.logger_service.debug(
                f"Hail {hail.id} updated by {auth.user_id}")

            return hail.to_hail_rider_response()

        return await self.redis_service.with_lock(
            f"dash:hail:{hail_id}", update_hail_status
        )

    async def update_hail_boost(
        self,
        auth: Auth,
        hail_id: str,
        boost_request: RiderHailBoostRequest,
        timestamp: datetime,
    ) -> BoostUpdateResponse:
        async def update_boost() -> BoostUpdateResponse:
            # Step 1: Get and validate hail
            hail = await self._get_and_validate_hail_for_boost(hail_id, auth)

            # Step 1.5: Validate boost request
            if boost_request.boost_amount is None or boost_request.boost_amount <= 0:
                raise HailExceptionBuilder().invalid_boost_amount(
                    "Boost amount is required and must be greater than 0"
                )

            # Step 2: Calculate boost amounts
            current_boost, new_boost_to_add, new_total_boost = await self._calculate_boost_amounts(
                hail, boost_request.boost_amount
            )

            # # Step 3: Remove hail from drivers (before making changes)
            # await self._remove_hail_from_drivers(hail)

            # Step 4: Update hail with new boost
            await self._update_hail_with_boost(hail, current_boost, new_total_boost, auth)

            # Step 5: Get updated hail and notify rider
            updated_hail = await self.hail_repository.get_hail_by_id(hail_id)
            if updated_hail is None:
                raise HailExceptionBuilder().hail_not_found(hail_id)

            await self._notify_rider_of_boost_success(hail_id, updated_hail)

            # Step 6: Re-add hail to drivers with new boost
            await self._add_hail_to_drivers(updated_hail, timestamp)

            # Step 7: Return response
            return BoostUpdateResponse(
                total_boost_amount=new_total_boost,
                added_boost_amount=new_boost_to_add,
                updated_at=updated_hail.updated_at,
                message=f"Added ${new_boost_to_add:.2f} boost. Total boost: ${new_total_boost:.2f}"
            )

        return await self.redis_service.with_lock(f"dash:hail:{hail_id}", update_boost)

    async def _get_and_validate_hail_for_boost(self, hail_id: str, auth: Auth) -> Hail:
        """Get hail and validate it can be boosted"""
        hail = await self.hail_repository.get_hail_by_id(hail_id)

        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)

        if not hail.can_be_updated:
            raise HailExceptionBuilder().hail_not_updatable(
                hail_status=hail.status, hail_id=hail_id
            )

        if hail.user_id != auth.user_id:
            raise AuthExceptionBuilder().auth_forbidden_resource()

        if hail.status != HailStatus.PENDING:
            raise HailExceptionBuilder().hail_not_updatable(
                hail_status=hail.status, hail_id=hail_id
            )

        return hail

    async def _calculate_boost_amounts(self, hail: Hail, requested_boost: float) -> tuple[float, float, float]:
        """Calculate current, added, and total boost amounts with limit validation"""
        current_boost = hail.boost_amount or 0.0
        new_total_boost = current_boost + requested_boost

        hail_config = await self.configuration_repository.get_hail_configuration()

        MAX_BOOST_LIMIT = float(hail_config.max_boost_limit)

        # Strict validation - reject if total would exceed limit
        if new_total_boost > MAX_BOOST_LIMIT:
            raise HailExceptionBuilder().boost_limit_exceeded(
                current_boost=current_boost,
                max_limit=MAX_BOOST_LIMIT
            )

        return current_boost, requested_boost, new_total_boost

    async def _remove_hail_from_drivers(self, hail: Hail, timestamp: datetime) -> None:
        """Remove hail from drivers before making changes"""
        await self.hail_repository.remove_hail_from_expansion_sets(hail)

        driver_ids_for_hail = await self.driver_repository.get_drivers_for_hail(hail.id)
        if len(driver_ids_for_hail) > 0:
            await self.web_socket_message_service.trigger_message(
                driver_ids_for_hail,
                HailNotAvailableMessage(
                    payload=hail.id, timestamp=timestamp),
            )
            self.logger_service.log(
                f"Removed old hail from {len(driver_ids_for_hail)} drivers before boost update"
            )

        await self.driver_repository.remove_drivers_for_hail(hail.id)

    async def _update_hail_with_boost(
        self, hail: Hail, old_boost: float, new_total_boost: float, auth: Auth
    ) -> None:
        """Update hail object with new boost amount and recalculated fares"""
        # Clear old cache
        await self.option_matrix_service.clear_option_matrix_cache(
            version=2,
            create_hail_option_matrix_request=CreateHailOptionMatrixRequest(
                place_ids=[itinerary.place_id for itinerary in hail.itinerary],
                time=hail.time,
                language=hail.language,
                payment_instrument_type=hail.payment_details.card_type,
                double_tunnel_fee=hail.double_tunnel_fee,
                applicable_discounts=hail.applicable_discounts,
                boost_amount=old_boost,
            ),
        )

        # Update boost amount
        hail.boost_amount = new_total_boost
        hail.updated_at = DateTimeUtils.utc_now_with_tz()

        # Recalculate with new boost
        options_matrix = await self.option_matrix_service.get_hail_option_matrix_v2(
            create_hail_option_matrix_request=CreateHailOptionMatrixRequest(
                place_ids=[itinerary.place_id for itinerary in hail.itinerary],
                time=hail.time,
                language=hail.language,
                payment_instrument_type=hail.payment_details.card_type,
                double_tunnel_fee=hail.double_tunnel_fee,
                applicable_discounts=hail.applicable_discounts,
                boost_amount=new_total_boost,
            ),
            auth=auth,
        )

        # Update fare calculations
        min_max_fare_calculations = options_matrix.min_max_fare_calculations(
            hail.vehicle_class_fleet_preferences.preference_matrix
        )
        if min_max_fare_calculations is None:
            raise HailExceptionBuilder().no_hail_options_available()

        hail.min_max_fare_calculations_str = json.dumps(
            [calc.model_dump_json() for calc in min_max_fare_calculations]
        )

        await hail.save()

        # Update SQL transaction with complete updated hail data (non-blocking)
        try:
            self.logger_service.debug(
                f"💰 Sending TX update for boost on hail {hail.id}",
                extra={
                    "hail_id": hail.id,
                    "boost_amount": new_total_boost,
                    "user_id": auth.user_id
                }
            )

            await self.tx_service.add_tx_event(
                tx_id=hail.id,
                auth=auth,
                event=HailingUserUpdatesOrder(
                    content=hail.to_hail_rider_response().model_dump(mode='json', by_alias=True)
                )
            )

            self.logger_service.debug(
                f"✅ TX update sent successfully for hail {hail.id}")

        except Exception as e:
            self.logger_service.error(
                f"❌ Failed to send TX update for hail {hail.id}: {str(e)}",
                extra={"error": str(e), "hail_id": hail.id}
            )

    async def _notify_rider_of_boost_success(self, hail_id: str, updated_hail: Hail) -> None:
        """Send socket message to rider about successful boost"""
        await self.web_socket_message_service.trigger_message(
            socket_ids={hail_id},
            message=HailBoostSuccessMessage(
                payload=updated_hail.to_hail_rider_response())
        )

    async def _add_hail_to_drivers(self, updated_hail: Hail, timestamp: datetime) -> None:
        """Add updated hail back to drivers"""
        hail_config = await self.configuration_repository.get_hail_configuration()

        await self.hail_repository.add_hail_to_operating_area_set(updated_hail)

        if updated_hail.is_live:
            await self.hail_repository.add_live_hail_to_geo_set(updated_hail)

            if MeterOperatingArea.LANTAU in updated_hail.operating_areas:
                await self.driver_repository.set_viable_drivers_for_hail_by_operating_area(
                    hail=updated_hail, operating_areas={
                        MeterOperatingArea.LANTAU}, timestamp=timestamp
                )

            if {MeterOperatingArea.URBAN, MeterOperatingArea.NT} & updated_hail.operating_areas:
                max_radius = hail_config.driver_search.max_search_radius_km
                filtered_drivers = await self.driver_repository.get_filtered_drivers_within_radius(
                    updated_hail, max_radius
                )
                await self.driver_repository.set_drivers_for_hail(updated_hail, filtered_drivers, timestamp)

        elif updated_hail.is_scheduled:
            await self.driver_repository.set_viable_drivers_for_hail_by_operating_area(
                hail=updated_hail, operating_areas=updated_hail.operating_areas, timestamp=timestamp
            )

    async def update_hail_by_drivers(
        self,
        auth: Auth,
        hail_id: str,
        update_hail_request: (
            DriverHailAcceptedRequest
            | DriverHailOnGoingRequest
            | DriverHailCancelledRequest
            | DriverHailCompletedRequest
        ),
        background_tasks: BackgroundTasks,
        timestamp: datetime,
    ) -> HailDriverResponse:
        async def update_hail_status() -> HailDriverResponse:

            hail = await self.hail_repository.get_hail_by_id(hail_id)

            if hail is None:
                raise HailExceptionBuilder().hail_not_found(hail_id)

            if not hail.can_be_updated:
                raise HailExceptionBuilder().hail_not_updatable(
                    hail_status=hail.status, hail_id=hail_id
                )

            driver_id = auth.phone_number
            driver = await self.driver_repository.get_driver_by_id(driver_id)
            if driver is None:
                raise DriverExceptionBuilder().driver_not_found(driver_id=driver_id)

            match hail.status:
                case HailStatus.PENDING:
                    if update_hail_request.status == HailStatus.ACCEPTED:
                        await self.hail_repository.set_hail_to_accepted(
                            hail, driver, timestamp, auth, background_tasks
                        )

                        if hail.is_within_approaching_threshold:
                            await self.hail_repository.set_hail_to_approaching(
                                hail, timestamp, driver=driver, auth=auth, background_tasks=background_tasks
                            )

                            self.pub_sub_publisher_service.publish_push_notification(
                                PublishPushNotification(
                                    event=NotificationTriggerEventType.RT3_ORDER_ACCEPTED_BY_DRIVER,
                                    recipient_type=NotificationRecipientType.RIDER,
                                    recipient_id=hail.user_id,
                                )
                            )

                case HailStatus.ACCEPTED | HailStatus.APPROACHING:
                    if not hail.is_matched_with_driver(driver=driver):
                        raise HailExceptionBuilder().hail_not_updatable(
                            hail_status=hail.status,
                            hail_id=hail_id,
                        )

                    if update_hail_request.status == HailStatus.ON_GOING:
                        await self.__pair_hail_with_trip(hail, driver, auth, background_tasks)
                    elif update_hail_request.status == HailStatus.CANCELLED:
                        await asyncio.gather(
                            *[
                                self.hail_repository.set_hail_to_pending(
                                    hail, auth, background_tasks),
                            ]
                        )
                        num_of_favorite_drivers_to_wait_for = 0
                        background_tasks.add_task(
                            self.find_matching_drivers_for_live_hail,
                            hail,
                            num_of_favorite_drivers_to_wait_for
                        )

                        if hail.is_live:
                            self.pub_sub_publisher_service.publish_push_notification(
                                PublishPushNotification(
                                    event=NotificationTriggerEventType.RT6_ACCEPTED_ORDER_CANCELLED_BY_DRIVER,
                                    recipient_type=NotificationRecipientType.RIDER,
                                    recipient_id=hail.user_id,
                                )
                            )
                        elif hail.is_scheduled and hail.is_within_approaching_threshold:
                            self.pub_sub_publisher_service.publish_push_notification(
                                PublishPushNotification(
                                    event=NotificationTriggerEventType.P10_ACCEPTED_ORDER_CANCELLED_BY_DRIVER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                                    recipient_type=NotificationRecipientType.RIDER,
                                    recipient_id=hail.user_id,
                                )
                            )

                        hail_config = (
                            await self.configuration_repository.get_hail_configuration()
                        )
                        return hail.to_hail_driver_response(
                            driver=driver,
                            hail_config=hail_config,
                            fare_calculation_service=self.fare_calculation_service,
                        )
                case HailStatus.ON_GOING:
                    if not hail.is_matched_with_driver(driver=driver):
                        raise HailExceptionBuilder().hail_not_updatable(
                            hail_status=hail.status,
                            hail_id=hail_id,
                        )

                    if update_hail_request.status == HailStatus.COMPLETED:
                        await self.hail_repository.set_hail_to_completed(hail, auth, background_tasks)

                case _:
                    raise HailExceptionBuilder().hail_not_updatable(
                        hail_status=hail.status,
                        hail_id=hail_id,
                    )

            return hail.to_matched_driver_hail_response()

        return await self.redis_service.with_lock(
            f"dash:hail:{hail_id}", update_hail_status
        )

    async def update_hail_by_service(
        self, auth: Auth, hail_id: str, update_hail_request: ServiceHailCompletedRequest, background_tasks: BackgroundTasks
    ) -> Hail:
        """
        Update hail status by other backend service
        """

        async def update_hail_status() -> Hail:

            hail = await self.hail_repository.get_hail_by_id(hail_id)

            if hail is None:
                raise HailExceptionBuilder().hail_not_found(hail_id)

            if not hail.can_be_updated:
                raise HailExceptionBuilder().hail_not_updatable(
                    hail_status=hail.status, hail_id=hail_id
                )

            match hail.status:
                case HailStatus.ON_GOING:
                    if update_hail_request.status == HailStatus.COMPLETED:
                        await self.hail_repository.set_hail_to_completed(hail, auth, background_tasks)
                case _:
                    raise HailExceptionBuilder().hail_not_updatable(
                        hail_status=hail.status,
                        hail_id=hail_id,
                    )

            return hail

        return await self.redis_service.with_lock(
            f"dash:hail:{hail_id}", update_hail_status
        )

    async def update_active_hails(self) -> None:
        """
        Scheduled task to update the status of hails and trigger jobs
        """
        now = DateTimeUtils.utc_now_with_tz()

        # HANDLE ACTIVE MATCHED HAILS
        active_hails = await self.hail_repository.get_all_active_hails()
        self.logger_service.debug(f"Active hails: {len(active_hails)}")

        if len(active_hails) > 0:
            await asyncio.gather(
                *[
                    self.hail_repository.set_hail_to_approaching(
                        hail, timestamp=now
                    )
                    for hail in active_hails
                    if hail.is_accepted
                ]
            )
            await self.hail_repository.replace_active_hail_set(
                {hail.id for hail in active_hails}
            )

        # HANDLE PENDING HAILS
        pending_and_cancelled_hails = await self.hail_repository.get_hails_by_status(
            HailStatus.PENDING, HailStatus.CANCELLED
        )

        pending_hail_ids: set[str] = set()
        expiring_hail_ids: set[str] = set()

        if len(pending_and_cancelled_hails) > 0:
            hail_config = await self.configuration_repository.get_hail_configuration()

            for hail in pending_and_cancelled_hails:
                if hail.is_pending:
                    if hail.is_past_timeout(hail_config.time_triggers):
                        await self.hail_repository.set_hail_to_timed_out(hail, timestamp=now)

                        self.pub_sub_publisher_service.publish_hail_order_request_timeout(
                            HailOrderRequestTimeout(transaction_id=hail.id)
                        )
                        if hail.is_live:
                            self.pub_sub_publisher_service.publish_push_notification(
                                PublishPushNotification(
                                    event=NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
                                    recipient_type=NotificationRecipientType.RIDER,
                                    recipient_id=hail.user_id,
                                )
                            )
                        elif hail.is_scheduled:
                            self.pub_sub_publisher_service.publish_push_notification(
                                PublishPushNotification(
                                    event=NotificationTriggerEventType.P11_ORDER_TIME_OUT,
                                    recipient_type=NotificationRecipientType.RIDER,
                                    recipient_id=hail.user_id,
                                )
                            )

                    elif (
                        hail.is_within_unmatched_scheduled_hail_threshold
                        and NotificationTriggerEventType.P5_30_MINUTES_BEFORE_PICK_UP_TIME_WHILE_NOT_ACCEPTED
                        not in hail.notifications_sent
                    ):
                        self.pub_sub_publisher_service.publish_push_notification(
                            PublishPushNotification(
                                event=NotificationTriggerEventType.P5_30_MINUTES_BEFORE_PICK_UP_TIME_WHILE_NOT_ACCEPTED,
                                recipient_type=NotificationRecipientType.RIDER,
                                recipient_id=hail.user_id,
                            )
                        )
                        await hail.add_sent_notification(
                            NotificationTriggerEventType.P5_30_MINUTES_BEFORE_PICK_UP_TIME_WHILE_NOT_ACCEPTED
                        )

                    pending_hail_ids.add(hail.id)
                elif hail.is_cancelled:
                    if hail.is_past_timeout(hail_config.time_triggers):
                        await self.hail_repository.delete_one(hail.id)

                        if hail.matched_driver is not None:
                            await self.web_socket_message_service.trigger_message(
                                {hail.matched_driver.driver_id},
                                HailExpiredMessage(
                                    payload=HailId(hail_id=hail.id)
                                ),
                            )

                        expiring_hail_ids.add(hail.id)

        self.logger_service.debug(f"Pending hails: {pending_hail_ids}")
        self.logger_service.debug(f"Expiring hails: {expiring_hail_ids}")

    async def rider_hail_websocket(
        self,
        user_id: str,
        websocket: WebSocket,
        hail_id: str,
    ) -> None:
        is_connected = False
        try:
            hail = await self.hail_repository.get_hail_by_id(hail_id)

            if (
                hail is None
                or not hail.is_created_by_user(user_id)
                or not hail.is_available_for_socket
            ):
                await websocket.close()
                raise AuthExceptionBuilder().auth_forbidden_resource()

            await self.socket_service.connect(hail_id, websocket)
            self.logger_service.log(
                f"Hail rider connected with hail ID {hail_id}")

            if hail.is_approaching and hail.matched_driver is not None:
                driver = await self.driver_repository.get_driver_by_id(
                    hail.matched_driver.driver_id
                )
                if driver is not None and driver.heart_beat is not None:
                    await self.socket_service.send_personal_message(
                        socket_id=hail_id,
                        data=DriverHeartbeatMessage(payload=driver.heart_beat),
                    )

                    async def send_message(msg: DriverEtaMessage) -> None:
                        await self.socket_service.send_personal_message(
                            socket_id=hail_id,
                            data=msg
                        )

                    await self.routing_service.send_eta_update(
                        driver_location=driver.heart_beat,
                        destination=hail.itinerary[0],
                        send_message=send_message,
                        hail_id=hail_id
                    )

            is_connected = websocket.client_state == WebSocketState.CONNECTED

            while is_connected and websocket.client_state == WebSocketState.CONNECTED:
                await websocket.receive()

            is_connected = False
            await self.socket_service.disconnect(hail_id, websocket=websocket)

        except (WebSocketDisconnect, ConnectionClosed) as e:
            is_connected = False
            await self.socket_service.disconnect(hail_id, websocket=websocket)
            self.logger_service.log(
                f"Rider disconnected from hail socket {hail_id}")
        except BaseHailException as e:
            self.logger_service.warning(
                f"[{e.status_code}] {e.code} - {e.detail}")
            await self.socket_service.send_personal_message(
                hail_id,
                ExceptionMessage(
                    payload={**e.to_response()},
                ),
            )
        except ValidationError as e:
            self.logger_service.warning("Validation error")
            validation_exception = BaseHailException.from_validation_error(e)
            await self.socket_service.send_personal_message(
                hail_id,
                ExceptionMessage(
                    payload={
                        **validation_exception.to_response(),
                    },
                ),
            )
        except Exception as exc:
            unexpected_exception = DashExceptionBuilder().unexpected_error()
            exception_str = str(exc)
            self.logger_service.error(
                f"[{unexpected_exception.status_code}] {unexpected_exception.code} - {exception_str}",
                extra={
                    "hail": hail_id,
                    "exception": exception_str,
                    "traceback": traceback.format_exc(),
                },
            )
            await self.socket_service.send_personal_message(
                hail_id,
                ExceptionMessage(
                    payload=unexpected_exception.to_response(
                        {"hail": hail_id}),
                ),
            )
            is_connected = False
            await self.socket_service.disconnect(hail_id, websocket=websocket)

    async def get_hail_cancellation_fee(self, hail_id: str) -> HailCancellationFeeResponse:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)
        hail_config = await self.configuration_repository.get_hail_configuration()
        return HailCancellationFeeResponse(cancellation_fee=hail.cancellation_fee(hail_config))

    async def cancel_hail_by_admin(
        self,
        hail_id: str,
        auth: Auth,
        cancellation_fee: float = 0.0,
        cancellation_fee_breakdown: CancellationFeeBreakdown | None = None,
    ) -> HailRiderResponse:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)

        if not hail.can_be_updated:
            raise HailExceptionBuilder().hail_not_updatable(
                hail_status=hail.status, hail_id=hail_id
            )

        await self.hail_repository.set_hail_to_cancelled(
            hail,
            auth,
            is_admin_cancelled=True,
            cancellation_fee=cancellation_fee,
            cancellation_fee_breakdown=cancellation_fee_breakdown,
        )

        await self.cancel_hail_push_notification(hail_id)

        self.logger_service.log(f"Hail {hail.id} cancelled by admin")

        return hail.to_hail_rider_response()

    async def cancel_hail_push_notification(self, hail_id: str) -> None:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)
        if hail.matched_driver is None:
            return

        if hail.is_scheduled and hail.is_within_approaching_threshold:
            self.pub_sub_publisher_service.publish_push_notification(
                PublishPushNotification(
                    event=NotificationTriggerEventType.P10_ACCEPTED_ORDER_CANCELLED_BY_DRIVER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                    recipient_type=NotificationRecipientType.RIDER,
                    recipient_id=hail.user_id,
                )
            )
        else:
            self.pub_sub_publisher_service.publish_push_notification(
                PublishPushNotification(
                    event=NotificationTriggerEventType.P6_ACCEPTED_ORDER_CANCELLED_BY_RIDER_GREATER_THAN_15_MINUTES_BEFORE_PICK_UP_TIME,
                    recipient_type=NotificationRecipientType.RIDER,
                    recipient_id=hail.user_id,
                )
            )

    async def get_hail_by_drivers(self, auth: Auth, hail_id: str) -> HailDriverResponse:
        hail = await self.hail_repository.get_hail_by_id(hail_id)
        if hail is None:
            raise HailExceptionBuilder().hail_not_found(hail_id)
        hail_config = await self.configuration_repository.get_hail_configuration()
        driver = await self.driver_repository.get_driver_by_id(auth.driver_id)
        if driver is None:
            raise DriverExceptionBuilder().driver_not_found(auth.driver_id)
        return hail.to_hail_driver_response(
            driver=driver, hail_config=hail_config, fare_calculation_service=self.fare_calculation_service)

    async def __pair_hail_with_trip(
        self, hail: Hail, driver: Driver, auth: Auth, background_tasks: BackgroundTasks
    ) -> None:
        trip_id: str
        if bool(driver.meter.is_dash_meter):
            active_trip = await self.meter_repository.get_active_meter_trip(driver)
            if active_trip is None:
                raise MeterExceptionBuilder().active_meter_trip_not_found(
                    driver.license_plate
                )

            if active_trip.user is not None:
                raise MeterExceptionBuilder().active_meter_trip_is_already_paired(
                    driver.license_plate, active_trip.id
                )

            trip_id, _ = await asyncio.gather(
                self.meter_repository.pair_hail_with_existing_meter_trip(
                    hail, active_trip.id
                ),
                self.hail_repository.set_hail_to_on_going(
                    hail, active_trip.id, driver, auth, background_tasks
                )
            )

        else:
            trip_id = await self.meter_repository.pair_hail_with_new_meter_trip(hail)
            await asyncio.gather(
                self.hail_repository.set_hail_to_on_going(
                    hail, trip_id, driver, auth, background_tasks)
            )
        self.logger_service.log(
            f"Hail {hail.id} paired with /meter/{driver.license_plate}/trips/{trip_id}"
        )

    async def find_matching_drivers_for_live_hail(
        self, hail: Hail, num_favorite_drivers_online: int
    ) -> None:
        hail_config = await self.configuration_repository.get_hail_configuration()

        await self.driver_repository.remove_drivers_for_hail(hail.id)

        if hail.prioritize_favorite_drivers and num_favorite_drivers_online > 0:

            timeout_seconds = (
                hail_config.favorite_drivers.live_hail_timeout_seconds
                if hail_config.favorite_drivers
                else 60
            )
            await asyncio.sleep(timeout_seconds)

            latest_hail = await self.hail_repository.get_hail_by_id(hail.id)
            if latest_hail is None or not latest_hail.is_pending:
                return

            else:
                await self.web_socket_message_service.trigger_message(
                    {hail.id},
                    HailFavoriteDriversUnavailableMessage(
                        payload=HailId(hail_id=hail.id)
                    ),
                )

        await self.hail_repository.add_hail_to_operating_area_set(hail)
        await self.hail_repository.add_live_hail_to_geo_set(hail)

        if MeterOperatingArea.LANTAU in hail.operating_areas:
            now = DateTimeUtils.utc_now_with_tz()
            self.logger_service.debug(
                f"Searching for drivers by operating area: {MeterOperatingArea.LANTAU}",
                {"hail_id": hail.id},
            )
            await self.driver_repository.set_viable_drivers_for_hail_by_operating_area(
                hail=hail, operating_areas={MeterOperatingArea.LANTAU}, timestamp=now
            )

        if {MeterOperatingArea.URBAN, MeterOperatingArea.NT} & hail.operating_areas:
            self.logger_service.debug(
                f"Searching for drivers by expanding radius",
                {"hail_id": hail.id},
            )
            hail_is_pending = hail.is_pending
            km_radius = hail_config.driver_search.default_search_radius_km

            while (
                hail_is_pending
                and km_radius <= hail_config.driver_search.max_search_radius_km
            ):
                if km_radius <= hail_config.driver_search.max_search_radius_km:
                    updated_hail = await self.hail_repository.get_hail_by_id(hail.id)
                    hail_is_pending = updated_hail is not None and updated_hail.is_pending
                    if not hail_is_pending:
                        break

                    filtered_drivers = await self.driver_repository.get_filtered_drivers_within_radius(
                        hail, km_radius)

                    now = DateTimeUtils.utc_now_with_tz()

                    await self.driver_repository.check_drivers_and_set_for_hail(
                        hail, filtered_drivers, timestamp=now
                    )

                    if (
                        len(filtered_drivers)
                        < hail_config.driver_search.min_drivers_for_search_radius
                    ):
                        self.logger_service.debug(
                            f"Minimum number of drivers ({hail_config.driver_search.min_drivers_for_search_radius}) not found. Expanding search immediately"
                        )
                        km_radius += 1
                        continue

                    self.logger_service.debug(
                        f"Waiting {hail_config.driver_search.seconds_pause_between_expansions} seconds before expanding search area"
                    )
                    await asyncio.sleep(
                        hail_config.driver_search.seconds_pause_between_expansions
                    )
                    km_radius += 1
                else:
                    self.logger_service.debug(
                        f"Search for hail {hail.id} has reached the maximum search radius"
                    )

            if not hail_is_pending:
                self.logger_service.debug(
                    f"Hail {hail.id} is no longer pending, notifying drivers of unavailability"
                )
                await self.hail_repository.notify_drivers_of_unavailable_hail(hail, timestamp=DateTimeUtils.utc_now_with_tz())
                return

    async def find_matching_drivers_for_scheduled_hail(
        self, hail: Hail, num_favorite_drivers_online: int, timestamp: datetime
    ) -> None:
        if hail.prioritize_favorite_drivers and num_favorite_drivers_online > 0:
            hail_config = await self.configuration_repository.get_hail_configuration()

            timeout_seconds = (
                hail_config.favorite_drivers.scheduled_hail_timeout_seconds
                if hail_config.favorite_drivers
                else 300
            )
            await asyncio.sleep(timeout_seconds)

            latest_hail = await self.hail_repository.get_hail_by_id(hail.id)
            if latest_hail is None or not latest_hail.is_pending:
                return

        await self.hail_repository.add_hail_to_operating_area_set(hail)

        await self.driver_repository.set_viable_drivers_for_hail_by_operating_area(
            hail=hail, operating_areas=hail.operating_areas, timestamp=timestamp
        )

    async def get_favorite_drivers_online_for_hail(
        self, hail: Hail, favorite_driver_ids: set[str]
    ) -> set[str]:
        """
        Notify favorite drivers of a hail with a timeout before continuing
        """
        filtered_favorite_drivers = (
            await self.driver_repository.get_viable_drivers_for_hail_by_filters(
                hail, favorite_driver_ids
            )
        )

        self.logger_service.log(
            f"Favorite drivers to receive hail {hail.id}: {[driver.id for driver in filtered_favorite_drivers]}"
        )

        favorite_drivers_online = {
            driver.id for driver in filtered_favorite_drivers if driver.is_available
        }

        if len(favorite_drivers_online) < 1:
            self.logger_service.log(
                f"No favorite drivers online for hail {hail.id}")

        return favorite_drivers_online
