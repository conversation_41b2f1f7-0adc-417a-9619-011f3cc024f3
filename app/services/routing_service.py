from typing import Optional, Dict, Any, Callable, Awaitable
from app.services.gcp_auth_service import GcpAuthService
import httpx
from pydantic import BaseModel, ValidationError
from app.services.logger_service import LoggerService
from app.contracts.common.pin import Pin
from app.infrastructure.settings import settings
from app.utils.http_client import create_http_client
from app.sockets.messages.driver_eta_message import DriverEtaMessage
# Simplified Pydantic models for Valhalla response (for ETA extraction)


class _ValhallaTripSummary(BaseModel):
    time: float    # estimated time in seconds
    length: float  # estimated distance in km


class _ValhallaTrip(BaseModel):
    summary: _ValhallaTripSummary


class _ValhallaRouteResponse(BaseModel):
    trip: _ValhallaTrip


class RoutingService:
    """
    Service for handling routing calculations using Valhalla
    """
    logger_service: LoggerService
    gcp_auth_service: GcpAuthService
    routing_url: str

    def __init__(self, logger_service: LoggerService, gcp_auth_service: GcpAuthService) -> None:
        self.logger_service = logger_service
        self.gcp_auth_service = gcp_auth_service
        self.routing_url = settings.valhalla_url
   
    async def get_route(
        self,
        origin: Pin,
        destination: Pin,
        costing: str = "auto",
        units: str = "kilometers"
    ) -> Optional[_ValhallaRouteResponse]:
        """
        Get route between two points using Valhalla
        """
        request_payload = {
            "locations": [
                {"lat": origin.lat, "lon": origin.lng},
                {"lat": destination.lat, "lon": destination.lng}
            ],
            "costing": costing,
            "directions_options": {"units": units}
        }

        self.logger_service.log_function_start(
            "RoutingService", "get_route", 
            extra={"request_payload": request_payload}
        )

        token = await self.gcp_auth_service.get_id_token(self.routing_url)
        if not token:
            self.logger_service.error(
                "Failed to get GCP id token for Valhalla request",
                extra={
                    "origin": [origin.lat, origin.lng],
                    "destination": [destination.lat, destination.lng]
                }
            )
            return None
        

        try:
            async with create_http_client() as client:
                response = await client.post(
                    f"{self.routing_url}/route",
                    headers={
                        "Authorization": f"Bearer {token}",
                    },
                    json=request_payload,
                )

                if response.status_code >= 400:
                    self.logger_service.error(
                        f"Valhalla HTTP error: {response.status_code}",
                        extra={
                            "status_code": response.status_code,
                            "response_text": response.text,
                            "origin": [origin.lat, origin.lng],
                            "destination": [destination.lat, destination.lng]
                        }
                    )
                    return None
                
                self.logger_service.log_function_end(
                    "RoutingService", "get_route", 
                    extra={"response": response.json()}
                )

                return _ValhallaRouteResponse.model_validate(response.json())

        except httpx.TimeoutException as e:
            self.logger_service.error(
                f"Valhalla request timed out: {str(e)}",
                extra={
                    "origin": [origin.lat, origin.lng],
                    "destination": [destination.lat, destination.lng],
                }
            )
            return None
        except httpx.RequestError as e:
            self.logger_service.error(
                f"Valhalla request failed: {str(e)}",
                extra={
                    "origin": [origin.lat, origin.lng],
                    "destination": [destination.lat, destination.lng],
                    "error_type": type(e).__name__
                }
            )
            return None
        except ValidationError as e:
            self.logger_service.error(
                f"Valhalla response validation error: {str(e)}",
                extra={
                    "origin": [origin.lat, origin.lng],
                    "destination": [destination.lat, destination.lng],
                    "response_data": response.text if 'response' in locals() else None
                }
            )
            return None
        except Exception as e:
            self.logger_service.error(
                f"Unexpected error getting route from Valhalla: {str(e)}",
                extra={
                    "origin": [origin.lat, origin.lng],
                    "destination": [destination.lat, destination.lng],
                    "error_type": type(e).__name__
                }
            )
            return None

    def get_eta_from_route(self, route: _ValhallaRouteResponse) -> Optional[float]:
        """
        Extract ETA from Valhalla route response
        """
        try:
            return route.trip.summary.time
        except Exception as e:
            self.logger_service.error(
                f"Error extracting ETA from route: {str(e)}",
                extra={"error_type": type(e).__name__}
            )
            return None

    def get_distance_from_route(
        self, route: _ValhallaRouteResponse
    ) -> Optional[float]:
        """
        Extract distance from Valhalla route response
        """
        try:
            return route.trip.summary.length
        except Exception as e:
            self.logger_service.error(
                f"Error extracting distance from route: {str(e)}",
                extra={"error_type": type(e).__name__}
            )
            return None

    async def get_driver_eta(
        self,
        driver_location: Pin,
        destination: Pin
    ) -> Optional[float]:
        """
        Get estimated arrival time for a driver
        """
        try:
            valhalla_response = await self.get_route(
                origin=driver_location,
                destination=destination
            )
            if valhalla_response is None:
                return None
            return self.get_eta_from_route(valhalla_response)
        except Exception as e:
            self.logger_service.error(
                f"Error calculating driver ETA: {str(e)}",
                extra={
                    "driver_location": [driver_location.lat, driver_location.lng],
                    "destination": [destination.lat, destination.lng],
                    "error_type": type(e).__name__
                }
            )
            return None

    async def send_eta_update(
        self,
        driver_location: Pin,
        destination: Pin,
        send_message: Callable[[DriverEtaMessage], Awaitable[None]],
        hail_id: str
    ) -> None:
        """
        Common method to calculate and send ETA updates.

        Args:
            driver_location: Current location of the driver
            destination: Destination location
            send_message: Async function to send the ETA message
            hail_id: ID of the hail for logging purposes
        """
        eta = await self.get_driver_eta(
            driver_location=driver_location,
            destination=destination
        )
        self.logger_service.debug(
            f"Hail {hail_id}: Calculated ETA is {eta} seconds.")
        if eta is not None:
            await send_message(DriverEtaMessage(payload=eta))
