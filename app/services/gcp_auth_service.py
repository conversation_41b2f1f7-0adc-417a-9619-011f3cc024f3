import asyncio
import time
from typing import <PERSON><PERSON>, <PERSON>ple

from google.auth.exceptions import GoogleAuthError
from google.auth.transport.requests import Request
import google.auth
from google.auth import compute_engine

from app.services.logger_service import LoggerService


class GcpAuthService:
    """Service for retrieving and caching Google Cloud authentication tokens."""
    
    def __init__(self, logger_service: LoggerService):
        self.logger_service = logger_service
        # Default token lifetime is 1 hour, but we'll refresh a bit earlier
        self._token_lifetime_seconds = 3500  # ~58 minutes
        self._id_token: Optional[str] = None
        self._id_token_expiry: float = 0

    async def get_id_token(self, aud_url: str) -> Optional[str]:
        """
        https://cloud.google.com/docs/authentication/token-types#id-validation
        Get ID Token for Google Cloud service.
        Returns a cached token if it's still valid, otherwise fetches a new one.
        
        Args:
            aud_url: The full URL of your Cloud service (e.g., "https://your-service-xxxx.run.app").
        """
        # Return cached ID token if it's still valid
        if self._id_token and time.time() < self._id_token_expiry:
            return self._id_token
            
        try:
            request = Request() # type: ignore

            credentials = compute_engine.IDTokenCredentials( # type: ignore
                request=request, target_audience=aud_url, use_metadata_identity_endpoint=True
            )
            
            await asyncio.to_thread(lambda: credentials.refresh(request)) # type: ignore
            
            self._id_token = credentials.token
            self._id_token_expiry = time.time() + self._token_lifetime_seconds
            
            return self._id_token
            
        except GoogleAuthError as e:
            self.logger_service.error(
                f"Google authentication error fetching ID Token: {str(e)}",
                extra={"error_type": type(e).__name__}
            )
            return None
        
        except Exception as e:
            self.logger_service.error(
                f"Failed to get ID Token for Cloud Run: {str(e)}",
                extra={"error_type": type(e).__name__}
            )
            return None