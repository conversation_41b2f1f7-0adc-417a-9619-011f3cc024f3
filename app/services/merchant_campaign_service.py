from app.contracts.campaigns.rule_params import RuleParams
from app.contracts.merchant_campaigns.merchant_campaign import MerchantCampaign
from app.infrastructure.settings import settings
from app.models.auth import Auth
from app.services.logger_service import LoggerService
from app.utils.exceptions.merchant_campaign_exception_builder import MerchantCampaignExceptionBuilder
from app.utils.http_client import create_http_client
import httpx


class MerchantCampaignService:
    base_url: str
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.base_url = settings.dash_api_url
        self.logger_service = logger_service

    async def get_applicable_merchant_campaign(
        self,
        auth: Auth,
        rule_params: RuleParams,
    ) -> MerchantCampaign:
        async with create_http_client() as client:
            try:
                campaign_query = {
                    "params": rule_params.model_dump(by_alias=True),
                }

                self.logger_service.debug(
                    f"merchant campaign query", campaign_query)

                response = await client.post(
                    f"{self.base_url}/merchants/campaigns/query-applicable",
                    headers={
                        "Authorization": f"Bearer {auth.token}",
                    },
                    json=campaign_query,
                )

                response.raise_for_status()
                applicable_merchant_campaign_response = response.json()
                return MerchantCampaign.model_validate(applicable_merchant_campaign_response)
            except httpx.ReadTimeout as e:
                self.logger_service.warning(
                    "Merchant Campaign service timeout",
                    extra={
                        "warning": str(e),
                    },
                )
                raise MerchantCampaignExceptionBuilder().timeout()
            except Exception as e:
                self.logger_service.error(
                    "Merchant Campaign service failure",
                    extra={
                        "error": str(e),
                    },
                )
                raise MerchantCampaignExceptionBuilder().merchant_campaign_service_failure()
