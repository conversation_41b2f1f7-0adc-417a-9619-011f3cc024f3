import json
import firebase_admin
from firebase_admin import credentials, App, firestore_async, firestore
from google.cloud import storage
from google.cloud.storage import Bucket
from app.infrastructure.settings import settings
from google.cloud.firestore_v1.async_client import AsyncClient
from google.cloud.firestore_v1.client import Client


class FirebaseService:
    app: App
    db: Client
    asyncDb: AsyncClient
    bucket: Bucket

    def __init__(self) -> None:
        firebase_config = settings.firebase_config
        cred: credentials.Certificate | credentials.ApplicationDefault

        if firebase_config:
            firebase_config_json = json.loads(firebase_config)
            cred = credentials.Certificate(firebase_config_json)
        else:
            try:
                cert_file = open("firebase_local.json")
                firebase_config = json.load(cert_file)
                cred = credentials.Certificate(firebase_config)
            except FileNotFoundError:
                # Fallback to the default way if FIREBASE_CONFIG is not set
                cred = credentials.ApplicationDefault()

        hailing_bucket = f"{settings.project_id}-hailing"

        self.app = firebase_admin.initialize_app(
            cred, {"storageBucket": hailing_bucket}
        )
        self.db = firestore.client(self.app)
        self.asyncDb = firestore_async.client(self.app)
        self.bucket = storage.Client(credentials=cred.get_credential()).bucket(
            hailing_bucket
        )
