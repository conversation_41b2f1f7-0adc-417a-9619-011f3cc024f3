# T = TypeVar("T")


# class SagaCoordinator:
#     """
#     Saga coordinating service to handle the registration, execution and compensation of steps in a saga.
#     Use this to achieve transactional consistency across multiple services in a distributed system.
#     """

#     logger_service: LoggerService
#     steps: list[Callable[..., Awaitable]]
#     compensations: list[Callable[..., Awaitable]]

#     def __init__(self, logger_service: LoggerService):
#         self.logger_service = logger_service
#         self.steps = []
#         self.compensations = []

#     def add_step(
#         self,
#         step: Callable[..., Awaitable[T]],
#         compensation: Callable[..., Awaitable[T]],
#     ) -> None:
#         """
#         Add a step to the saga. Steps are executed in the order they are added.
#         """
#         self.steps.append(step)
#         self.compensations.append(compensation)

#     async def execute(self) -> None:
#         """
#         Execute all steps in the saga. If any step fails, compensate all previous steps.
#         """
#         for i, step in enumerate(self.steps):
#             try:
#                 await step()
#             except Exception as e:
#                 self.logger_service.error(f"Step {i} failed: {e}")
#                 await self.__compensate(i)
#                 raise

#     async def __compensate(self, failed_step_index: int) -> None:
#         """
#         Compensate all steps from the failed step index to the first step.
#         """
#         for i in range(failed_step_index, -1, -1):
#             try:
#                 await self.compensations[i]()
#             except Exception as e:
#                 self.logger_service.error(f"Compensation for step {i} failed: {e}")
