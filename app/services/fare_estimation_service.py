from datetime import datetime, timedelta, timezone
from decimal import Decimal
import os
import pickle
import tempfile
from typing import Any
from sklearn.neural_network import MLPRegressor
import yaml
from app.contracts.common.pin import Pin
from app.contracts.matrix.tunnel_fee import TunnelFee
from app.models.meter_operating_area import MeterOperatingArea
from app.models.taxi_fare_table import TaxiFareTable
from app.persistence.firestore.entities.hail_configuration import HailConfiguration

from app.services.firebase_service import FirebaseService
import geojson
from shapely.geometry import shape, Point

from app.services.geo_spacial_service import GeoSpatialService
from app.services.logger_service import LoggerService
from app.utils.conversion_utils import ConversionUtils


class FareEstimationService:
    """FareEstimationService provides methods to estimate taxi base fare."""

    geo_spacial_service: GeoSpatialService

    logger_service: LoggerService

    fare_table_urban_2024: TaxiFareTable
    """ Taxi fare table for urban taxi in 2024, according to government regulations."""

    fare_table_lantau_2024: TaxiFareTable
    """Taxi fare table for Lantau taxi in 2024, according to government regulations."""

    fare_table_nt_2024: TaxiFareTable
    """Taxi fare table for New Territories taxi in 2024, according to government regulations."""

    fare_table_urban_2021: TaxiFareTable
    """Taxi fare table for urban taxi in 2021, according to government regulations."""

    fare_table_lantau_2021: TaxiFareTable
    """Taxi fare table for Lantau taxi in 2021, according to government regulations."""

    fare_table_nt_2021: TaxiFareTable
    """Taxi fare table for New Territories taxi in 2021, according to government regulations."""

    model_urban: MLPRegressor
    """Neural network model for Urban taxi fare estimation."""

    model_lantau: MLPRegressor
    """Neural network model for Lantau taxi fare estimation."""

    config_urban: dict[str, Any]
    """Model Configuration for model_urban"""

    config_lantau: dict[str, Any]
    """Model Configuration for model_lantau"""

    geojson_data: dict[str, Any]
    """Geojson data for Hong Kong zones."""

    def __init__(
        self,
        firebase_service: FirebaseService,
        logger_service: LoggerService,
        geo_spacial_service: GeoSpatialService,
    ):
        self.logger_service = logger_service
        self.geo_spacial_service = geo_spacial_service

        self.fare_table_urban_2024 = TaxiFareTable(29, 2.1, 1.4, 102.5)
        self.fare_table_lantau_2024 = TaxiFareTable(24, 1.9, 1.6, 195)
        self.fare_table_nt_2024 = TaxiFareTable(25.5, 1.9, 1.4, 82.5)
        self.fare_table_urban_2021 = TaxiFareTable(27, 1.9, 1.3, 102.5)
        self.fare_table_lantau_2021 = TaxiFareTable(22, 1.7, 1.5, 195)
        self.fare_table_nt_2021 = TaxiFareTable(23.5, 1.7, 1.3, 82.5)

        dir = tempfile.gettempdir()
        if not os.path.exists(os.path.join(dir, "fare_estimation")):
            os.mkdir(os.path.join(dir, "fare_estimation"))

        model_urban_file_blob = "fare_estimation/model_red.pkl"
        model_lantau_file_blob = "fare_estimation/model_blue.pkl"
        config_urban_file_blob = "fare_estimation/config_red.yaml"
        config_lantau_file_blob = "fare_estimation/config_blue.yaml"
        geojson_file_blob = "fare_estimation/hk_geojson.json"

        model_urban_file = os.path.join(dir, model_urban_file_blob)
        model_lantau_file = os.path.join(dir, model_lantau_file_blob)
        config_urban_file = os.path.join(dir, config_urban_file_blob)
        config_lantau_file = os.path.join(dir, config_lantau_file_blob)
        geojson_file = os.path.join(dir, geojson_file_blob)

        self.logger_service.debug(
            f"Google storage fare estimation files download start"
        )

        firebase_service.bucket.blob(model_urban_file_blob).download_to_filename(
            model_urban_file
        )
        firebase_service.bucket.blob(model_lantau_file_blob).download_to_filename(
            model_lantau_file
        )
        firebase_service.bucket.blob(config_urban_file_blob).download_to_filename(
            config_urban_file
        )
        firebase_service.bucket.blob(config_lantau_file_blob).download_to_filename(
            config_lantau_file
        )
        firebase_service.bucket.blob(geojson_file_blob).download_to_filename(
            geojson_file
        )

        self.logger_service.debug(f"Google storage fare estimation files download end")

        self.model_urban = self.load_model(model_urban_file)
        self.model_lantau = self.load_model(model_lantau_file)
        self.config_urban = self.load_config(config_urban_file)
        self.config_lantau = self.load_config(config_lantau_file)

        with open(geojson_file) as f:
            self.geojson_data = geojson.load(f)

    def load_config(self, config_path: str) -> Any:
        with open(config_path, "r") as file:
            config = yaml.safe_load(file)
        return config

    def load_model(self, model_file: str) -> MLPRegressor:
        with open(model_file, "rb") as f:
            model = pickle.load(f)
        return model

    def get_zone_id(self, lat: float, lng: float) -> float:
        """Determine the zone ID based on latitude and longitude using geojson data."""
        point = Point(lng, lat)
        for i, feature in enumerate(self.geojson_data["features"]):
            polygon = shape(feature["geometry"])
            if polygon.contains(point):
                zone = float(i)
                return zone
        else:
            return -1.0

    def get_adjusted_fare(
        self, distance: float, taxi_type: MeterOperatingArea
    ) -> float:
        """
        Calculate the fare difference between 2024 and 2021.
        This is because model was trained on data with fare rates from 2021.
        For accurate fare estimation, we need to adjust the fare based on the difference between 2024 and 2021 fare rates.
        """
        if taxi_type == MeterOperatingArea.LANTAU:
            return self.fare_table_lantau_2024.get_fare_from_distance(
                distance
            ) - self.fare_table_lantau_2021.get_fare_from_distance(distance)
        elif taxi_type == MeterOperatingArea.URBAN:
            return self.fare_table_urban_2024.get_fare_from_distance(
                distance
            ) - self.fare_table_urban_2021.get_fare_from_distance(distance)
        elif taxi_type == MeterOperatingArea.NT:
            return 0
        else:
            raise NotImplementedError(
                f"Invalid operating area {taxi_type}. Currently only support LANTAU, URBAN and NT"
            )

    def base_fare_estimation(
        self,
        google_duration: int,
        google_distance: int,
        origin_pin: Pin,
        destination_pin: Pin,
        operating_area: MeterOperatingArea,
    ) -> Decimal:
        """Estimate the base fare"""

        current_time = datetime.now(timezone.utc) + timedelta(hours=8)
        time_of_day = current_time.hour + current_time.minute / 60
        origin_zone = self.get_zone_id(origin_pin.lat, origin_pin.lng)
        destination_zone = self.get_zone_id(destination_pin.lat, destination_pin.lng)
        distance = google_distance / 100

        data = {
            "API_duration": google_duration,
            "API_distance": distance,
            "origin_lat": origin_pin.lat,
            "origin_lng": origin_pin.lng,
            "destination_lat": destination_pin.lat,
            "destination_lng": destination_pin.lng,
            "time_of_day": time_of_day,
            "origin_zone": origin_zone,
            "destination_zone": destination_zone,
        }

        estimation: float
        if operating_area == MeterOperatingArea.LANTAU:
            features = self.config_lantau["required_features"]
            x = [[data[feature] for feature in features]]
            y = self.model_lantau.predict(x)[0]
            overestimate_factor = 1.03
            output = y * overestimate_factor + self.get_adjusted_fare(
                google_distance, MeterOperatingArea.LANTAU
            )
            estimation = output
        elif operating_area == MeterOperatingArea.URBAN:
            features = self.config_urban["required_features"]
            x = [[data[feature] for feature in features]]
            y = self.model_urban.predict(x)[0]
            overestimate_factor = 1.05
            output = y * overestimate_factor + self.get_adjusted_fare(
                google_distance, MeterOperatingArea.URBAN
            )
            estimation = output
        else:
            estimation = self.fare_table_nt_2024.get_fare_from_distance(google_distance)

        rounded_estimation = ConversionUtils.float_to_decimal(estimation)
        self.logger_service.log(
            f"Estimated base fare for {operating_area}: {rounded_estimation}"
        )
        return rounded_estimation

    def tunnel_fare_estimation(
        self,
        route: str,
        hail_config: HailConfiguration,
    ) -> list[TunnelFee]:
        """Estimate the tunnel fare based on the route taken"""
        tunnel_names: list[str] = self.geo_spacial_service.get_tunnels_crossed(route)
        return [
            TunnelFee(tunnel=tunnel, fee=hail_config.tunnel_fare[tunnel])
            for tunnel in tunnel_names
        ]
