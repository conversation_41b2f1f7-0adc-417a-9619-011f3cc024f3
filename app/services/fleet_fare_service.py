import asyncio
from datetime import datetime
from decimal import Decimal

from app.contracts.common.fare_calculation import FareCalculation
from app.contracts.fleetQuote.fleet_quote_response import FleetQuoteResponse
from app.models.auth import Auth
from app.models.discount_rules import DiscountRules
from app.models.localization_language import LocalizationLanguage
from app.models.meter_operating_area import MeterOperatingArea
from app.models.price_rule import PriceRule
from app.models.vehicle_class import VehicleClass
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.services import fleet_quote_converter_service
from app.services.fleet_quote_service import FleetQuoteService
from app.services.geo_spacial_service import GeoSpatialService
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.services.fare_estimation_service import FareEstimationService
from app.services.geo_service import GeoService
from app.services.standard_fare_service import StandardFareService


class FleetFareService:
    logger_service: LoggerService
    location_service: LocationService
    geo_service: GeoService
    fare_estimation_service: FareEstimationService
    configuration_repository: ConfigurationsRepository
    geo_spacial_service: GeoSpatialService
    fleet_quote_service: FleetQuoteService
    standard_fare_service: StandardFareService
    
    def __init__(
        self,
        logger_service: LoggerService,
        location_service: LocationService,
        geo_service: GeoService,
        configuration_repository: ConfigurationsRepository,
        fare_estimation_service: FareEstimationService,
        geo_spacial_service: GeoSpatialService,
        fleet_quote_service: FleetQuoteService,
        standard_fare_service: StandardFareService,
    ) -> None:
        self.logger_service = logger_service
        self.location_service = location_service
        self.geo_service = geo_service
        self.configuration_repository = configuration_repository
        self.fare_estimation_service = fare_estimation_service
        self.geo_spacial_service = geo_spacial_service
        self.fleet_quote_service = fleet_quote_service
        self.standard_fare_service = standard_fare_service

    async def get_fare_calculation_map(
        self,
        auth: Auth,
        place_ids: list[str],
        language: LocalizationLanguage,
        request_time: datetime,
        price_rules: list[PriceRule],
        boost_amount: float = 0.0,
        discount_rules: DiscountRules | None = None,
    ) -> tuple[dict[str, FareCalculation], FleetQuoteResponse]:
        self.logger_service.debug(f"FleetFareService/get_fare_calculation-start", {
            "place_ids": place_ids,
            "language": language,
            "request_time": request_time,
            "boost_amount": boost_amount,
            "discount_rules": discount_rules.model_dump() if discount_rules else None,
        })
        
        fare_calculations = {}
        
        # Get trip estimation, fleet quote response, and hail configuration concurrently
        trip_estimation, fleet_quote_response, hail_config = await asyncio.gather(
            self.location_service.get_trip_estimation(
                origin_place_id=place_ids[0],
                destination_place_id=place_ids[1],
                language=language,
                auth=auth,
            ),
            self.fleet_quote_service.get_fleet_quote(
                auth=auth,
                price_ids=place_ids,
                time=request_time,
                language=language,
            ),
            self.configuration_repository.get_hail_configuration(),
        )
        
        for vehicle_class_option in fleet_quote_response.vehicle_class_options:
            fare_calculations[vehicle_class_option.fleet_vehicle_type] = self.standard_fare_service.compute_fare_calculation(
                trip_estimation,
                vehicle_class_option.base_fare,
                estimated_tunnel_fare=Decimal(0),
                hail_config=hail_config,
                dash_transaction_fees=hail_config.dash_fee_configuration.common,
                operating_area= MeterOperatingArea.URBAN,
                price_rules=price_rules,
                discount_rules=discount_rules,
                boost_amount=boost_amount,
                vehicle_class=VehicleClass.FOUR_SEATER,
            )
        
        return (fare_calculations, fleet_quote_response)
    
    async def get_min_max_fare_calculation_by_fleet_vehicle_type(
        self,
        auth: Auth,
        place_ids: list[str],
        language: LocalizationLanguage,
        request_time: datetime,
        fleet_vehicle_type: str,
        price_rules: list[PriceRule],
        boost_amount: float = 0.0,
        discount_rules: DiscountRules | None = None,
    ) -> tuple[FareCalculation, FareCalculation]:
        fare_calculation_map, _ = await self.get_fare_calculation_map(
            auth=auth,
            place_ids=place_ids,
            language=language,
            request_time=request_time,
            price_rules=price_rules,
            boost_amount=boost_amount,
            discount_rules=discount_rules,
        )
        ## find fare by fleet vehicle type
        fare_calculation = fare_calculation_map[fleet_vehicle_type]
        min_fare = fare_calculation
        max_fare = fare_calculation
        
        return (min_fare, max_fare)
