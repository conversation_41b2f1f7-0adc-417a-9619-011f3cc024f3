from datetime import datetime

from app.contracts.hails.hail_cancelled_response import HailCancelledResponse
from app.contracts.hails.hail_rider_response import HailRiderResponse
from app.sockets.message_code import MessageCode
from app.sockets.messages.base_message import BaseMessage


class HailCancelledForDriverMessage(BaseMessage):
    message: MessageCode = MessageCode.HAIL_CANCELLED
    payload: HailCancelledResponse
    timestamp: datetime
