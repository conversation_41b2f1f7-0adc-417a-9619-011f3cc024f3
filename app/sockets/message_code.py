from enum import StrEnum


class MessageCode(StrEnum):
    EXCEPTION = "EXCEPTION"

    QUERY_EXPANDED = "QUERY_EXPANDED"

    HAIL_PENDING_DRIVER = "HAIL_PENDING_DRIVER"
    HAIL_PENDING_RIDER = "HAIL_PENDING_RIDER"
    HAIL_ACCEPTED = "HAIL_ACCEPTED"
    HAIL_NOT_AVAILABLE = "HAIL_NOT_AVAILABLE"
    HAIL_CANCELLED = "HAIL_CANCELLED"
    HAIL_APPROACHING_RIDER = "HAIL_APPROACHING_RIDER"
    HAIL_APPROACHING_DRIVER = "HAIL_APPROACHING_DRIVER"
    HAIL_ON_GOING_RIDER = "HAIL_ON_GOING_RIDER"
    HAIL_EXPIRED = "HAIL_EXPIRED"
    HAIL_TIMED_OUT = "HAIL_TIMED_OUT"
    HAIL_FAVORITE_DRIVERS_OFFLINE = "HAIL_FAVORITE_DRIVERS_OFFLINE"
    HAIL_FAVORITE_DRIVERS_UNAVAILABLE = "HAIL_FAVORITE_DRIVERS_UNAVAILABLE"
    HAIL_DRIVERS_NOTIFIED = "HAIL_DRIVERS_NOTIFIED"
    HAIL_DRIVER_ETA = "HAIL_DRIVER_ETA"
    HAIL_BOOST_SUCCESS = "HAIL_BOOST_SUCCESS"

    DRIVER_HEARTBEAT = "DRIVER_HEARTBEAT"
    DRIVER_SESSION_START = "DRIVER_SESSION_START"
    DRIVER_SESSION_CREATED = "DRIVER_SESSION_CREATED"
    DRIVER_PENDING_HAILS = "DRIVER_PENDING_HAILS"
    DRIVER_ACK = "DRIVER_ACK"
