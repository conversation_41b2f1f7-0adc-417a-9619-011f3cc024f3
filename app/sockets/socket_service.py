import asyncio
import j<PERSON>
import uuid
from fastapi import WebSocket
from typing import Dict

from fastapi.websockets import WebSocketState
from websockets import ConnectionClosedError

from app.infrastructure.settings import settings
from app.services.logger_service import LoggerService
from app.sockets.messages.base_message import BaseMessage


class SocketService:
    """
    Manage active websocket connections
    """

    logger_service: LoggerService
    active_connections: Dict[str, set[WebSocket]]
    instance_id: str

    def __init__(self, logger_service: LoggerService):
        self.logger_service = logger_service
        self.active_connections: Dict[str, set[WebSocket]] = {}
        self.instance_id = str(uuid.uuid4())

    def socket_id_is_active(self, socket_id: str | None) -> bool:
        return socket_id is not None and socket_id in self.active_connections

    def socket_id_has_connections(self, socket_id: str) -> bool:
        return (
            self.socket_id_is_active(socket_id)
            and len(self.active_connections[socket_id]) > 0
        )

    async def connect(self, socket_id: str, websocket: WebSocket) -> None:
        await websocket.accept()
        self.active_connections[socket_id] = {
            *(
                self.active_connections[socket_id]
                if socket_id in self.active_connections
                else {}
            ),
            websocket,
        }
        self.__log_socket_connections(socket_id)

    async def disconnect(self, socket_id: str, websocket: WebSocket) -> None:
        if self.socket_id_is_active(socket_id):
            if len(self.active_connections[socket_id]) == 1:
                del self.active_connections[socket_id]
            elif websocket in self.active_connections[socket_id]:
                self.active_connections[socket_id].remove(websocket)

            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.close()
                except ConnectionClosedError as e:
                    self.logger_service.warning(
                        f"WebSocket connection already closed: {e}"
                    )
                except Exception as e:
                    self.logger_service.warning(
                        f"Error closing websocket connection: {e}"
                    )
        self.__log_socket_connections(socket_id)

    async def send_personal_message(self, socket_id: str, data: BaseMessage) -> None:
        if self.socket_id_is_active(socket_id):
            json_data = json.loads(data.model_dump_json(by_alias=True))
            self.logger_service.log_function_start(
                "socket_service", "send_personal_message", extra={
                    "message": json_data,
                }
            )
            sent_messages = await asyncio.gather(
                *(
                    socket.send_json(json_data)
                    for socket in self.active_connections[socket_id]
                    if socket.client_state == WebSocketState.CONNECTED
                ),
                return_exceptions=True,
            )

            failed_messages = [
                str(message)
                for message in sent_messages
                if isinstance(message, BaseException)
            ]
            failed_messages_count = len(failed_messages)
            successful_messages_count = len(
                sent_messages) - failed_messages_count

            if failed_messages_count > 0:
                self.logger_service.log_function_end(
                    "socket_service", "send_personal_message", extra={
                        "message": json_data,
                        "successful": successful_messages_count,
                        "failed": failed_messages_count,
                        "errors": failed_messages,
                    }
                )
            else:
                self.logger_service.log_function_end(
                    "socket_service", "send_personal_message", extra={
                        "message": json_data,
                        "successful": successful_messages_count,
                    }
                )

    async def broadcast_to_group(
        self, socket_ids: list[str] | set[str], data: BaseMessage
    ) -> None:
        # Limit concurrent sends to 3 at a time
        semaphore = asyncio.Semaphore(settings.websocket_max_concurrent_sends)

        async def _send_with_limit(sid: str) -> None:
            async with semaphore:
                await self.send_personal_message(sid, data)

        await asyncio.gather(
            *(_send_with_limit(socket_id) for socket_id in socket_ids),
            return_exceptions=True,
        )

    def __log_socket_connections(self, socket_id: str) -> None:
        self.logger_service.debug(
            f"[Instance {self.instance_id}] Socket id {socket_id} has {len(self.active_connections.get(socket_id, {}))} total connections."
        )
