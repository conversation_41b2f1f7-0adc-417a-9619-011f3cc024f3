from dependency_injector.wiring import Provide
from fastapi import Depends
from app.infrastructure.container import Container
from app.persistence.redis.pub_sub.message_subscriber import MessageSubscriber
from app.persistence.redis.pub_sub.redis_pub_sub_service import RedisPubSubService

from app.services.logger_service import LoggerService
from app.services.scheduler_service import SchedulerService


class LifeCycle:
    logger_service: LoggerService
    pub_sub_service: RedisPubSubService
    message_subscriber: MessageSubscriber
    scheduler_service: SchedulerService

    def __init__(
        self,
        logger_service: LoggerService = Depends(Provide[Container.logger_service]),
        redis_pub_sub_service: RedisPubSubService = Depends(
            Provide[Container.redis_pub_sub_service]
        ),
        message_subscriber: MessageSubscriber = Depends(
            Provide[Container.message_subscriber]
        ),
        scheduler_service: SchedulerService = Depends(
            Provide[Container.scheduler_service]
        ),
    ):
        self.logger_service = logger_service
        self.pub_sub_service = redis_pub_sub_service
        self.message_subscriber = message_subscriber
        self.scheduler_service = scheduler_service

    async def start_services(self) -> None:
        await self.pub_sub_service.add_subscribers([self.message_subscriber])
        self.pub_sub_service.start()
        self.scheduler_service.start()
        self.logger_service.log("Lifecycle services started")

    async def stop_services(self) -> None:
        await self.pub_sub_service.unsubscribe()
        self.scheduler_service.shutdown()
        self.logger_service.log("Lifecycle services stopped")
