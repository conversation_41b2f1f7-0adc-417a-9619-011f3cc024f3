from typing import Optional
from fastapi import Request
from app.infrastructure.settings import settings
from app.models.auth import Auth
from app.models.role import Role
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder



class HttpXApiKeyAuth:
    def __init__(self, auto_error: bool = False):
        self.auto_error = auto_error

    async def __call__(self, request: Request) -> Optional[Auth]:
        x_api_key = request.headers.get("X-API-Key")
        if x_api_key is None:
            if self.auto_error:
                raise AuthExceptionBuilder().auth_invalid_authorization_code()
            return None

        if x_api_key != settings.x_api_key:
            if self.auto_error:
                raise AuthExceptionBuilder().auth_invalid_token()
            return None

        return Auth(
            role=Role.DEFAULT,
            user_id="X-API-Key",
            phone_number="X-API-Key",
            token="X-API-Key",
        )
