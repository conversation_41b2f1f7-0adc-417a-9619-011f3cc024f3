import os
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status, Request
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from google.auth.transport import requests as grequests
from google.oauth2 import id_token
from typing import Optional
from app.infrastructure.settings import settings

class GcpIdAuth(HTTPBearer):
    def __init__(self, auto_error: bool = False):
        super(GcpId<PERSON><PERSON>, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request) -> Optional[HTTPAuthorizationCredentials]:
        credentials = await super(GcpIdAuth, self).__call__(request)
        if not credentials: 
            if self.auto_error:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Missing Authorization Bearer header."
                )
            return None
        
        try:
            id_token.verify_oauth2_token(
                credentials.credentials, grequests.Request(), audience=settings.google_auth_audience_name # type: ignore
            )
            return credentials
        
        except ValueError as e:
            if self.auto_error:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Token validation failed: {e}"
                )
            else: 
                return None

