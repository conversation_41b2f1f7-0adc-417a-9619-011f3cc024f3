import logging
from fastapi import Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.infrastructure.settings import settings
from app.models.auth import Auth
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder

from google.auth.transport.requests import Request as GoogleRequest
from google.oauth2 import id_token


class HttpServiceAuth(HTTPBearer):
    def __init__(self, auto_error: bool = False):
        super(HttpServiceAuth, self).__init__(auto_error=auto_error)

    async def __call__(self, request: Request) -> Auth:  # type: ignore
        credentials = await super(HttpServiceAuth, self).__call__(request)
        if credentials:
            logging.info(
                f"HTTP SERVICE AUTH: {credentials.scheme} {credentials.credentials}"
            )
            if not credentials.scheme == "Bearer":
                logging.warning("HTTP AUTH: Invalid scheme")
                raise AuthExceptionBuilder().auth_invalid_scheme()

            try:
                id_token.verify_oauth2_token(  # type: ignore
                    credentials.credentials,
                    GoogleRequest(),  # type: ignore
                    audience=settings.project_id,
                )
            except Exception as e:
                logging.warning(f"HTTP AUTH: Failed to validate JW<PERSON> token {str(e)}")
                raise AuthExceptionBuilder().auth_invalid_token()
        else:
            raise AuthExceptionBuilder().auth_invalid_authorization_code()
