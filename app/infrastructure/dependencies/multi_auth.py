from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials


from app.infrastructure.dependencies.gcp_id_auth import GcpIdAuth
from app.infrastructure.dependencies.http_x_api_key_auth import HttpXA<PERSON><PERSON>ey<PERSON><PERSON>
from app.models.auth import Auth

def multi_auth_dependency(
    http_xapi_auth: Auth = Depends(HttpXApi<PERSON>eyAuth(auto_error=False)),
    gcp_id_auth: HTTPAuthorizationCredentials = Depends(GcpIdAuth(auto_error=False)),
) -> Auth | HTTPAuthorizationCredentials:
    if http_xapi_auth:
        return http_xapi_auth
    elif gcp_id_auth:
        return gcp_id_auth
    else:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

