from fastapi import HTTPException, WebSocket, status, WebSocketException
import logging
from app.models.auth import Auth
from app.models.role import Role
from app.infrastructure.settings import settings

AUTH_HEADER = "Authorization"


class SocketAuth:

    def __init__(self, required_role: Role | None = None):
        self.required_role = required_role

    async def __call__(self, websocket: WebSocket) -> Auth:
        authorization = websocket.headers.get(AUTH_HEADER)

        if authorization:
            scheme, credentials = authorization.split(" ")
            if not scheme == "Bearer":
                logging.warning("WEBSOCKET AUTH: Invalid scheme")
                raise WebSocketException(
                    code=status.WS_1008_POLICY_VIOLATION,
                )

            try:
                auth = Auth.from_jwt_token(credentials)
            except HTTPException as e:
                logging.warning(
                    "WEBSOCKET AUTH: Failed to validate JW<PERSON> token for websocket connection"
                )
                raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION)

            if (
                settings.is_local is False
                and self.required_role is not None
                and auth.role != self.required_role
            ):
                logging.warning(
                    f"WEBSOCKET AUTH: Unauthorized access to websocket with role {auth.role} for user {auth.user_id}"
                )
                raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION)

            return auth
        else:
            raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION)
