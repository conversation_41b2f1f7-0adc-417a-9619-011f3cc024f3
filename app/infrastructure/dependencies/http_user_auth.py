import logging
from fastapi import Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from app.models.auth import Auth
from app.models.role import Role
from app.utils.exceptions.auth_exception_builder import AuthExceptionBuilder
from app.infrastructure.settings import settings


class HttpUserAuth(HTTPBearer):
    def __init__(self, auto_error: bool = False, required_role: Role | None = None):
        super(HttpUserAuth, self).__init__(auto_error=auto_error)
        self.required_role = required_role

    async def __call__(self, request: Request) -> Auth:  # type: ignore
        credentials = await super(HttpUserAuth, self).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                logging.warning("HTTP AUTH: Invalid scheme")
                raise AuthExceptionBuilder().auth_invalid_scheme()

            try:
                auth = Auth.from_jwt_token(credentials.credentials)
            except Exception as e:
                logging.warning("HTTP AUTH: Failed to validate JW<PERSON> token")
                raise AuthExceptionBuilder().auth_invalid_authorization_code()

            if (
                settings.is_local is False
                and self.required_role is not None
                and auth.role != self.required_role
            ):
                logging.warning(
                    f"HTTP AUTH: Unauthorized access with role {auth.role} for user {auth.user_id}"
                )
                raise AuthExceptionBuilder().auth_insufficient_role(
                    required_role=self.required_role
                )

            request.state.auth = auth

            return auth
        else:
            raise AuthExceptionBuilder().auth_invalid_authorization_code()
