from fastapi import Depends, FastAPI

from fastapi import Depends
from dependency_injector.wiring import Provide
from app.infrastructure.container import Container
from app.infrastructure.exception_interceptor import ExceptionInterceptor
from app.infrastructure.middlewares.collapse_exec_groups_middleware import (
    CollapseExecGroupsMiddleware,
)
from app.infrastructure.middlewares.http_logging_middleware import HttpLoggingMiddleware
from app.services.correlation_service import CorrelationService
from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError
from app.infrastructure.container import Container
from app.infrastructure.middlewares.request_context_middleware import (
    RequestContextMiddleware,
)

from app.services.logger_service import LoggerService
from app.utils.exceptions.base_exception import BaseHailException


class AppConfiguration:
    logger_service: LoggerService
    correlation_service: CorrelationService
    exception_interceptor: ExceptionInterceptor

    def __init__(
        self,
        logger_service: LoggerService = Depends(Provide[Container.logger_service]),
        correlation_service: CorrelationService = Depends(
            Provide[Container.correlation_service]
        ),
        exception_interceptor: ExceptionInterceptor = Depends(
            Provide[Container.exception_interceptor]
        ),
    ) -> None:
        self.logger_service = logger_service
        self.correlation_service = correlation_service
        self.exception_interceptor = exception_interceptor

    def configure_app(self, app: FastAPI) -> None:
        app.exception_handler(BaseHailException)(
            self.exception_interceptor.base_hail_exception_handler
        )
        app.exception_handler(RequestValidationError)(
            self.exception_interceptor.validation_exception_handler
        )
        app.exception_handler(Exception)(
            self.exception_interceptor.unexpected_exception_handler
        )

        # Note: Middleware execution order is LIFO (Last In, First Out)
        # So the last added middleware runs first
        app.add_middleware(
            HttpLoggingMiddleware, logger_service=self.logger_service
        )
        app.add_middleware(
            CollapseExecGroupsMiddleware,
            exception_interceptor=self.exception_interceptor,
        )
        app.add_middleware(
            RequestContextMiddleware, correlation_service=self.correlation_service
        )
