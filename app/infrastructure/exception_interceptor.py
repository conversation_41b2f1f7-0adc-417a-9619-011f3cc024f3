from typing import Any

from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
import sentry_sdk
from app.services.logger_service import LoggerService
from app.utils.exceptions.base_exception import BaseHailException
from app.utils.exceptions.dash_exception_builder import DashEx<PERSON><PERSON>uilder
from app.utils.exceptions.validation_exception_builder import ValidationExceptionBuilder
import traceback


class ExceptionInterceptor:
    logger_service: LoggerService

    def __init__(self, logger_service: LoggerService) -> None:
        self.logger_service = logger_service

    async def base_hail_exception_handler(
        self, _: Any, exc: BaseHailException
    ) -> JSONResponse:
        self.logger_service.warning(
            f"[{exc.status_code}] {exc.code} - {exc.description} - {exc.detail}"
        )
        return self.__handle_exception(exc)

    async def validation_exception_handler(
        self, _: Any, exc: RequestValidationError
    ) -> JSONResponse:
        validation_exception = ValidationExceptionBuilder().invalid_data(exc)
        self.logger_service.warning(
            f"[{validation_exception.status_code}] {validation_exception.code}",
            extra={"exception": exc.errors()},
        )
        return self.__handle_exception(validation_exception)

    async def unexpected_exception_handler(
        self, request: Request, exc: Exception
    ) -> JSONResponse:
        unexpected_exception = DashExceptionBuilder().unexpected_error()
        exception_str = str(exc)
        self.logger_service.error(
            f"[{unexpected_exception.status_code}] {unexpected_exception.code} - {exception_str}",
            extra={"exception": exception_str, "traceback": traceback.format_exc()},
        )

        sentry_sdk.capture_exception(
            exc,
            user=(
                {
                    "id": request.state.auth.user_id,
                    "username": request.state.auth.phone_number,
                }
                if request.state and hasattr(request.state, "auth")
                else None
            ),
        )
        return self.__handle_exception(unexpected_exception)

    def __handle_exception(self, exc: BaseHailException) -> JSONResponse:
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.to_response(),
        )
