from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    env: str = ""
    redis_om_url: str = ""
    dash_api_url: str = ""
    project_id: str = ""
    google_identity_key: str = ""
    sentry_dsn: str = ""
    firebase_config: str | None = None
    default_radius: float = 1
    default_unit_of_distance: str = "km"
    max_search_radius: float = 3
    routing_url: str = "https://valhalla-************.asia-east2.run.app"
    x_api_key: str = ""
    google_auth_audience_name: str = ""

    valhalla_url: str = ""
    websocket_max_concurrent_sends: int = 5
    # HTTP client timeout settings (in seconds)
    http_timeout: float = 30.0
    model_config = SettingsConfigDict(env_file=".env")

    @property
    def is_production(self) -> bool:
        return self.env == "PROD"

    @property
    def is_qa(self) -> bool:
        return self.env == "QA"

    @property
    def is_development(self) -> bool:
        return self.env == "DEV"

    @property
    def is_development_2(self) -> bool:
        return self.env == "DEV2"

    @property
    def is_local(self) -> bool:
        return self.env == "LOCAL"

    @property
    def allow_testing(self) -> bool:
        return settings.is_development or settings.is_development_2 or settings.is_local


settings = Settings()
