from app.services.fleet_fare_service import FleetFareService
from app.services.gcp_auth_service import GcpAuthService
from app.services.merchant_campaign_service import MerchantCampaignService
from dependency_injector import containers, providers

from app.infrastructure.exception_interceptor import ExceptionInterceptor
from app.persistence.firestore.repositories.configurations_repository import (
    ConfigurationsRepository,
)
from app.persistence.firestore.repositories.firestore_repository import (
    FirestoreRepository,
)
from app.persistence.firestore.repositories.meter_repository import MeterRepository
from app.persistence.firestore.repositories.vehicle_repository import VehicleRepository
from app.persistence.redis.pub_sub.message_subscriber import MessageSubscriber
from app.persistence.redis.redis import RedisService
from app.persistence.redis.repositories.driver_repository import DriverRepository
from app.persistence.redis.repositories.message_repository import MessageRepository
from app.persistence.redis.repositories.tracking_repository import TrackingRepository
from app.services.campaign_service import CampaignService
from app.services.correlation_service import CorrelationService
from app.services.fare_calculation_service import FareCalculationService
from app.services.firebase_service import FirebaseService
from app.services.fleet_hails_service import FleetHailsService
from app.services.fleet_quote_converter_service import FleetQuoteConverterService
from app.services.fleet_quote_service import FleetQuoteService
from app.services.geo_service import GeoService
from app.persistence.redis.repositories.hail_repository import HailRepository
from app.services.geo_spacial_service import GeoSpatialService
from app.services.hail_quote_service import HailQuoteService
from app.services.hails_service import HailsService
from app.services.hails_service_v3 import HailsV3Service
from app.services.location_service import LocationService
from app.services.logger_service import LoggerService
from app.persistence.redis.pub_sub.redis_pub_sub_service import RedisPubSubService
from app.services.routing_service import RoutingService
from app.services.option_matrix_service import (
    OptionMatrixService,
)
from app.services.pub_sub_publisher_service import PubSubPublisherService
from app.services.scheduler_service import SchedulerService
from app.services.standard_fare_service import StandardFareService
from app.services.tx_service import TxService
from app.services.fare_estimation_service import FareEstimationService
from app.sockets.socket_service import SocketService
from app.services.web_socket_message_service import WebSocketMessageService


class Container(containers.DeclarativeContainer):
    correlation_service = providers.ThreadSafeSingleton(CorrelationService)
    logger_service = providers.ThreadSafeSingleton(
        LoggerService, correlation_service=correlation_service
    )
    exception_interceptor = providers.Factory(
        ExceptionInterceptor,
        logger_service=logger_service,
    )

    redis_service = providers.Factory(
        RedisService, logger_service=logger_service)
    redis_pub_sub_service = providers.ThreadSafeSingleton(
        RedisPubSubService, logger_service=logger_service
    )
    firebase_service = providers.ThreadSafeSingleton(FirebaseService)

    geo_spacial_service = providers.ThreadSafeSingleton(
        GeoSpatialService,
        firebase_service=firebase_service,
        logger_service=logger_service,
    )

    firestore_repository = providers.Factory(
        FirestoreRepository,
        logger_service=logger_service,
        firebase_service=firebase_service,
    )

    vehicle_repository = providers.Factory(
        VehicleRepository,
        firebase_service=firebase_service,
        logger_service=logger_service,
    )
    configuration_repository = providers.Factory(
        ConfigurationsRepository,
        firebase_service=firebase_service,
        logger_service=logger_service,
        redis_service=redis_service,
    )
    fare_calculation_service = providers.Factory(
        FareCalculationService,
        configuration_repository=configuration_repository,
        logger_service=logger_service,
        geo_spacial_service=geo_spacial_service,
    )

    pub_sub_publisher_service = providers.Factory(
        PubSubPublisherService, logger_service=logger_service
    )
    
    web_socket_message_service = providers.ThreadSafeSingleton(
        WebSocketMessageService, correlation_service=correlation_service
    )

    driver_repository = providers.Factory(
        DriverRepository,
        logger_service=logger_service,
        configuration_repository=configuration_repository,
        fare_calculation_service=fare_calculation_service,
        pub_sub_publisher_service=pub_sub_publisher_service,
        web_socket_message_service=web_socket_message_service,
    )

    meter_repository = providers.Factory(
        MeterRepository,
        firebase_service=firebase_service,
        logger_service=logger_service,
    )
    geo_service = providers.Factory(
        GeoService,
        logger_service=logger_service,
    )

    location_service = providers.Factory(
        LocationService,
        logger_service=logger_service,
    )
    tx_service = providers.Factory(TxService, logger_service=logger_service)
    socket_service = providers.ThreadSafeSingleton(
        SocketService, logger_service=logger_service
    )

    tracking_repository = providers.Factory(
        TrackingRepository,
        logger_service=logger_service,
    )

    pub_sub_publisher_service = providers.Factory(
        PubSubPublisherService, logger_service=logger_service
    )

    gcp_auth_service = providers.Factory(
        GcpAuthService,
        logger_service=logger_service,
    )

    routing_service = providers.Factory(
        RoutingService,
        logger_service=logger_service,
        gcp_auth_service=gcp_auth_service,
    )

    hail_repository = providers.Factory(
        HailRepository,
        logger_service=logger_service,
        driver_repository=driver_repository,
        configuration_repository=configuration_repository,
        socket_service=socket_service,
        fare_calculation_service=fare_calculation_service,
        pub_sub_publisher_service=pub_sub_publisher_service,
        routing_service=routing_service,
        tx_service=tx_service,
        web_socket_message_service=web_socket_message_service,
    )

    message_repository = providers.Factory(
        MessageRepository,
        logger_service=logger_service,
    )

    message_subscriber = providers.ThreadSafeSingleton(
        MessageSubscriber,
        logger_service=logger_service,
        socket_service=socket_service,
        message_repository=message_repository,
    )

    scheduler_service = providers.ThreadSafeSingleton(
        SchedulerService, hail_repository)

    fare_estimation_service = providers.ThreadSafeSingleton(
        FareEstimationService,
        geo_spacial_service=geo_spacial_service,
        firebase_service=firebase_service,
        logger_service=logger_service,
    )

    campaign_service = providers.Factory(
        CampaignService, logger_service=logger_service)

    fleet_quote_service = providers.Factory(
        FleetQuoteService, logger_service=logger_service)

    fleet_hails_service = providers.Factory(
        FleetHailsService,
        hail_repository=hail_repository,
        driver_repository=driver_repository,
        configuration_repository=configuration_repository,
        meter_repository=meter_repository,
        logger_service=logger_service,
        fare_calculation_service=fare_calculation_service,
        pub_sub_publisher_service=pub_sub_publisher_service,
        web_socket_message_service=web_socket_message_service,
    )

    fleet_quote_converter_service = providers.Factory(
        FleetQuoteConverterService,
        logger_service=logger_service,
        fare_calculation_service=fare_calculation_service,
    )

    merchant_campaign_service = providers.Factory(
        MerchantCampaignService,
        logger_service=logger_service)

    option_matrix_service = providers.Factory(
        OptionMatrixService,
        logger_service=logger_service,
        configuration_repository=configuration_repository,
        firestore_repository=firestore_repository,
        geo_service=geo_service,
        geo_spacial_service=geo_spacial_service,
        location_service=location_service,
        fare_estimation_service=fare_estimation_service,
        redis_service=redis_service,
        fare_calculation_service=fare_calculation_service,
        campaign_service=campaign_service,
        fleet_quote_service=fleet_quote_service,
        fleet_quote_converter_service=fleet_quote_converter_service,
    )

    hails_service = providers.Factory(
        HailsService,
        hail_repository=hail_repository,
        driver_repository=driver_repository,
        configuration_repository=configuration_repository,
        meter_repository=meter_repository,
        tx_service=tx_service,
        logger_service=logger_service,
        pub_sub_publisher_service=pub_sub_publisher_service,
        option_matrix_service=option_matrix_service,
        redis_service=redis_service,
        socket_service=socket_service,
        firestore_repository=firestore_repository,
        fare_calculation_service=fare_calculation_service,
            web_socket_message_service=web_socket_message_service,
        campaign_service=campaign_service,
        routing_service=routing_service,
        merchant_campaign_service=merchant_campaign_service,
    )
    
    standard_fare_service = providers.Factory(
        StandardFareService,
        logger_service=logger_service,
        location_service=location_service,
        geo_service=geo_service,
        configuration_repository=configuration_repository,
        fare_estimation_service=fare_estimation_service,
        geo_spacial_service=geo_spacial_service,
    )
    
    
    fleet_fare_service = providers.Factory(
        FleetFareService,
        logger_service=logger_service,
        location_service=location_service,
        geo_service=geo_service,
        configuration_repository=configuration_repository,
        fare_estimation_service=fare_estimation_service,
        geo_spacial_service=geo_spacial_service,
        fleet_quote_service=fleet_quote_service,
        standard_fare_service=standard_fare_service,
    )
    
    hail_quote_service = providers.Factory(
        HailQuoteService,
        logger_service=logger_service,
        standard_fare_service=standard_fare_service,
        configuration_repository=configuration_repository,
        fleet_fare_service=fleet_fare_service,
        location_service=location_service,
        geo_spacial_service=geo_spacial_service,
        redis_service=redis_service,
    )

    hails_v3_service = providers.Factory(
        HailsV3Service,
        logger_service=logger_service,
        standard_fare_service=standard_fare_service,
        fleet_fare_service=fleet_fare_service,
        driver_repository=driver_repository,
        configuration_repository=configuration_repository,
        location_service=location_service,
        firestore_repository=firestore_repository,
        pub_sub_publisher_service=pub_sub_publisher_service,
        hails_service=hails_service,
        fare_estimation_service=fare_estimation_service,
        geo_spacial_service=geo_spacial_service,
        redis_service=redis_service,
        hail_quote_service=hail_quote_service,
        hail_repository=hail_repository,
        routing_service=routing_service,
    )
