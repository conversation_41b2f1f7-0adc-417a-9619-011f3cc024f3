from uuid import uuid4
from fastapi import Request, Response
from app.models.request_context import Request<PERSON>ontext
from app.services.correlation_service import CorrelationService
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGIApp


class RequestContextMiddleware(BaseHTTPMiddleware):
    correlation_service: CorrelationService

    def __init__(self, app: ASGIApp, correlation_service: CorrelationService) -> None:
        super().__init__(app)
        self.correlation_service = correlation_service

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        correlation_id = request.headers.get("x-correlation-id", str(uuid4()))

        self.correlation_service.set_request_context(
            RequestContext(correlation_id=correlation_id)
        )

        response = await call_next(request)

        response.headers["x-correlation-id"] = correlation_id

        return response
