from typing import Dict, Any, Callable, Awaitable, Union
from fastapi.concurrency import iterate_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from starlette.requests import Request
from starlette.responses import Response, StreamingResponse
import json
import time

from app.services.logger_service import LoggerService


class HttpLoggingMiddleware(BaseHTTPMiddleware):
    logger_service: LoggerService

    def __init__(self, app: ASGIApp, logger_service: LoggerService) -> None:
        super().__init__(app)
        self.logger_service = logger_service

    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        # Skip logging for WebSocket requests
        if request.scope.get("type") == "websocket":
            return await call_next(request)
            
        # Record start time for execution tracking
        start_time = time.time()
        
        resp_json: Dict[str, Any] = {}
        status_code = 500
        error_message = None

        # Parse request body
        try:
            req_body = await request.body()
            req_data = req_body.decode() if req_body else ""
            req_json = json.loads(req_data) if req_data else {}
        except Exception as e:
            req_json = req_data if 'req_data' in locals() else ""
            self.logger_service.warning(
                f"Error parsing request body: {str(e)}")

        # Process request and handle errors
        try:
            response = await call_next(request)
            status_code = response.status_code

            # Try to capture response body for logging
            try:
                res_body = [section async for section in response.body_iterator]  # type: ignore
                response.body_iterator = iterate_in_threadpool(iter(res_body))  # type: ignore
                
                # Try to parse response as JSON
                response_text = res_body[0].decode()
                try:
                    resp_json = json.loads(response_text) if response_text else {}
                except json.JSONDecodeError:
                    # If it's not valid JSON, store as string
                    resp_json = {"response_body": response_text}

            except Exception as e:
                resp_json = {
                    "error": f"Could not parse response body: {str(e)}"}

        except Exception as e:
            error_message = str(e)
            self.logger_service.error(f"Error in call_next: {error_message}")
            raise

        finally:
            # Calculate execution time in milliseconds
            execution_time_ms = (time.time() - start_time) * 1000
            
            # Always log the request/response (even if there was an error)
            log_data = {
                "method": request.method,
                "url": str(request.url),
                "headers": dict(request.headers),
                "request": req_json,
                "response": resp_json,
                "status_code": status_code,
                "execute_duration": int(round(execution_time_ms))  # Round to integer milliseconds
            }

            if error_message:
                log_data["error"] = error_message

            self.logger_service.log(
                f"HTTP request: {request.method} {request.url} - Status: {status_code}",
                extra=log_data
            )

        return response
