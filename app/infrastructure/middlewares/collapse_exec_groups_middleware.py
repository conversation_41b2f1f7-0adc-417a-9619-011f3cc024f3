from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGI<PERSON>pp
from starlette._utils import collapse_excgroups

from app.infrastructure.exception_interceptor import ExceptionInterceptor


class CollapseExecGroupsMiddleware(BaseHTTPMiddleware):
    exception_interceptor: ExceptionInterceptor

    def __init__(
        self, app: ASGIApp, exception_interceptor: ExceptionInterceptor
    ) -> None:
        super().__init__(app)
        self.exception_interceptor = exception_interceptor

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            with collapse_excgroups():
                return await call_next(request)
        except Exception as exc:
            return await self.exception_interceptor.unexpected_exception_handler(
                request, exc
            )
