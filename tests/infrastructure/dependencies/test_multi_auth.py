from typing import Any, Dict
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>, Depends
from unittest.mock import patch, MagicMock

from app.infrastructure.dependencies.multi_auth import multi_auth_dependency
from app.models.auth import Auth

app = FastAPI()

@app.get("/test-multi-auth")
def test_endpoint(auth: Auth = Depends(multi_auth_dependency)) -> None:
    return None

client = TestClient(app)


def test_returns_xapi_auth() -> None:
    mock_auth = Auth(user_id="test_user", token="test_token")
    with patch("app.infrastructure.dependencies.http_x_api_key_auth.HttpXApiKeyAuth.__call__", return_value=mock_auth), \
        patch("app.infrastructure.dependencies.gcp_id_auth.GcpIdAuth.__call__", return_value=None):

        response = client.get("/test-multi-auth")
        assert response.status_code == status.HTTP_200_OK

def test_returns_gcp_id_auth() -> None:
    mock_auth = Auth(user_id="test_user", token="test_token")
    with patch("app.infrastructure.dependencies.http_x_api_key_auth.HttpXApiKeyAuth.__call__", return_value=None), \
        patch("app.infrastructure.dependencies.gcp_id_auth.GcpIdAuth.__call__", return_value=mock_auth):

        response = client.get("/test-multi-auth")
        assert response.status_code == status.HTTP_200_OK

def test_unauthorized() -> None:
    with patch("app.infrastructure.dependencies.http_x_api_key_auth.HttpXApiKeyAuth.__call__", return_value=None), \
        patch("app.infrastructure.dependencies.gcp_id_auth.GcpIdAuth.__call__", return_value=None):
        
        response = client.get("/test-multi-auth")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.json()["detail"] == "Unauthorized"