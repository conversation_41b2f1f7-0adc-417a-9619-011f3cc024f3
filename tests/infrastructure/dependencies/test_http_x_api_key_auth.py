from typing import Any, Dict
from unittest.mock import patch
import pytest
import os
from fastapi import Depends, FastAPI, status
from fastapi.testclient import TestClient
from app.models.auth import Auth

from app.infrastructure.dependencies.http_x_api_key_auth import HttpXApi<PERSON><PERSON><PERSON>uth
from app.infrastructure.settings import settings

@pytest.fixture
def valid_token() -> str:
    return "valid.token.value"


@pytest.fixture
def valid_api_key(monkeypatch) -> str: # type: ignore
    monkeypatch.setattr(os, "getenv", lambda key, default=None: "valid-api-key" if key == "X_API_KEY" else default)
    return "valid-api-key"

app = FastAPI()

@app.get("/auto_error_on")
async def protected_auto_error_on(auth: Auth = Depends(HttpXApiKeyAuth(auto_error=True))) -> Dict[str, str]:
    return {"email": "<EMAIL>"}

@app.get("/auto_error_off")
async def protected_auto_error_off(auth: Auth  = Depends(HttpXApiKeyAuth(auto_error=False))) -> None:
    return None

client = TestClient(app)

# --- Tests for auto_error=True ---
def test_missing_authorization_header_error_on() -> None:
    response = client.get("/auto_error_on", headers={})
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_invalid_scheme_error_on() -> None:
    response = client.get("/auto_error_on", headers={"Authorization": "Basic sometoken"})
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_happy_path_error_on(monkeypatch) -> None: # type: ignore
    monkeypatch.setattr(settings, "x_api_key", "valid-api-key")
    response = client.get("/auto_error_on", headers={"X-API-Key": "valid-api-key"})
    assert response.status_code == status.HTTP_200_OK


# --- Tests for auto_error=False---
def test_missing_authorization_header_error_off() -> None:
    response = client.get("/auto_error_off", headers={})
    assert response.status_code == status.HTTP_200_OK

def test_invalid_scheme_error_off() -> None:
    response = client.get("/auto_error_off", headers={"Authorization": "Basic sometoken"})
    assert response.status_code == status.HTTP_200_OK


def test_happy_path_auto_error_off(valid_token: str) -> None:
    with patch("app.infrastructure.settings.settings.x_api_key", return_value=valid_token):
        response = client.get("/auto_error_off", headers={"X-API-Key": valid_token})
        assert response.status_code == status.HTTP_200_OK

