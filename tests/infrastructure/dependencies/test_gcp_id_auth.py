from typing import Any, Dict
from unittest.mock import patch
from fastapi.security import HTT<PERSON>uthorizationCredentials
import pytest
from fastapi import Depends, FastAPI, status
from fastapi.testclient import TestClient

from app.infrastructure.dependencies.gcp_id_auth import GcpIdAuth

def get_bearer_header(token: str) -> Dict[str, str]:
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def valid_token() -> str:
    return "valid.token.value"

@pytest.fixture
def identity_info() -> Dict[str, str]:
    return {
        "email": "<EMAIL>",
    }


app = FastAPI()

@app.get("/auto_error_on")
async def protected_auto_error_on(identity: HTTPAuthorizationCredentials = Depends(GcpIdAuth(auto_error=True))) -> Dict[str, str]:
    return {"email": "<EMAIL>"}

@app.get("/auto_error_off")
async def protected_auto_error_off(identity: HTTPAuthorizationCredentials = Depends(GcpIdAuth(auto_error=False))) -> None:
    return None

client = TestClient(app)

# --- Tests for auto_error=True ---
def test_missing_authorization_header_error_on() -> None:
    response = client.get("/auto_error_on", headers={})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json()["detail"] == "Not authenticated"

def test_invalid_scheme_error_on() -> None:
    response = client.get("/auto_error_on", headers={"Authorization": "Basic sometoken"})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json()["detail"] == "Invalid authentication credentials"

def test_happy_path_error_on(valid_token: str, identity_info: Dict[str,str]) -> None:
    with patch("app.infrastructure.dependencies.gcp_id_auth.id_token.verify_oauth2_token", return_value=identity_info):
        response = client.get("/auto_error_on", headers=get_bearer_header(valid_token))
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["email"] == identity_info["email"]

def test_wrong_audience_error_on(valid_token: str) -> None:
    with patch(
        "app.infrastructure.dependencies.gcp_id_auth.id_token.verify_oauth2_token",
        side_effect=ValueError("Token validation failed: Wrong audience")
    ):
        response = client.get("/auto_error_on", headers=get_bearer_header(valid_token))
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "Token validation failed" in response.json()["detail"]

# --- Tests for auto_error=False---
def test_missing_authorization_header_error_off() -> None:
    response = client.get("/auto_error_off", headers={})
    assert response.status_code == status.HTTP_200_OK

def test_invalid_scheme_error_off() -> None:
    response = client.get("/auto_error_off", headers={"Authorization": "Basic sometoken"})
    assert response.status_code == status.HTTP_200_OK


def test_happy_path_auto_error_off(valid_token: str, identity_info: Dict[str, Any]) -> None:
    with patch("app.infrastructure.dependencies.gcp_id_auth.id_token.verify_oauth2_token", return_value=identity_info):
        response = client.get("/auto_error_off", headers=get_bearer_header(valid_token))
        assert response.status_code == status.HTTP_200_OK

def test_wrong_audience_auto_error_off(valid_token: str) -> None:
    with patch(
        "app.infrastructure.dependencies.gcp_id_auth.id_token.verify_oauth2_token",
        side_effect=ValueError("Token validation failed: Wrong audience")
    ):
        response = client.get("/auto_error_off", headers=get_bearer_header(valid_token))
        assert response.status_code == status.HTTP_200_OK
