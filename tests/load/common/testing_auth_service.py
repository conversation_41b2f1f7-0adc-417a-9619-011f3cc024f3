import httpx

from app.infrastructure.settings import settings
from tests.load.common.auth_type import AuthType


class TestingAuthService:
    __otp_code = "111111"
    __auth_type_users: dict[AuthType, str] = {
        AuthType.DRIVER: "+***********",
        AuthType.RIDER: "+***********",
    }

    @staticmethod
    def get_id_token_for_role(auth_type: AuthType) -> str:
        """
        Exchange a refresh token for an id token.
        """
        with httpx.Client(timeout=httpx.Timeout(10, read=10)) as client:
            otp_response = client.post(
                f"https://identitytoolkit.googleapis.com/v1/accounts:sendVerificationCode?key={settings.google_identity_key}",
                data={"phoneNumber": TestingAuthService.__auth_type_users[auth_type]},
            )
            otp_response.raise_for_status()
            otp_response_dict = otp_response.json()
            session_info: str = otp_response_dict.get("sessionInfo")
            if session_info is None:
                raise ValueError(
                    f"Failed to get {auth_type} role identity session info."
                )

            verification_response = client.post(
                f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPhoneNumber?key={settings.google_identity_key}",
                data={
                    "sessionInfo": session_info,
                    "code": TestingAuthService.__otp_code,
                },
            )
            verification_response.raise_for_status()
            verification_response_dict = verification_response.json()
            id_token: str = verification_response_dict.get("idToken")
            if id_token is None:
                raise ValueError(f"Failed to get {auth_type} role identity id token")

            return id_token
