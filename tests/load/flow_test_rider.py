import logging
import random
import time
import traceback
from typing import Any, Callable
from locust import between, task

from app.contracts.hails.create_hail_request import Create<PERSON><PERSON>Request
from app.contracts.matrix.create_hail_option_matrix_request import (
    CreateHailOptionMatrixRequest,
)
from tests.load.common.auth_type import AuthType
from tests.load.users.socket_http_user import SocketHttpUser


class FlowTestRider(SocketHttpUser):
    """
    Test riders getting hails, creating hails, and starting socket connections
    """

    weight = 5
    wait_time: Callable[[Any], float] = between(1, 5)  # type: ignore
    initial_task_executed: bool = False

    @task(1)
    def initial_rider_flow_task(self) -> None:
        if self.initial_task_executed:
            return

        try:
            test_user_id = f"TESTUSER{random.randint(10000000, 99999999)}"
            test_user_query = f"?test_id={test_user_id}"

            headers = {
                "Authorization": f"Bearer {FlowTestRider.tokens[AuthType.RIDER]}",
                "Content-Type": "application/json",
            }

            # Get hails for riders
            self.client.get(
                url=f"/hailing/v1/hails/riders{test_user_query}",
                headers=headers,
                name="/hailing/v1/hails/riders",
            )

            time.sleep(6)

            # Get hail matrix
            self.client.post(
                url=f"/hailing/v1/pricing/matrix",
                data=CreateHailOptionMatrixRequest.create_example().model_dump_json(),
                headers=headers,
            )

            time.sleep(8)

            # Create new hail request
            create_hail_response = self.client.post(
                url=f"/hailing/v1/hails{test_user_query}",
                data=CreateHailRequest.create_example().model_dump_json(),
                headers=headers,
                name="/hailing/v1/hails",
            )
            hail_data = create_hail_response.json()

            hail_id = hail_data.get("id")
            if hail_id is None:
                raise AssertionError("Hail ID not found in response")

            # Connect to rider socket
            self.connect(
                url=f"{FlowTestRider.ws_host}/hailing/v1/hails/{hail_id}{test_user_query}",
                jwt_token=FlowTestRider.tokens[AuthType.RIDER],
                name="/hailing/v1/hails/<hail_id>",
            )

            logging.info(f"Hail socket {hail_id} connected")
        except AssertionError as e:
            logging.error(f"Assertion error in rider flow task: {e}")
        except Exception as e:
            print(traceback.format_exc())
            logging.error(f"Unexpected error in rider flow task: {e}")
        finally:
            self.initial_task_executed = True
