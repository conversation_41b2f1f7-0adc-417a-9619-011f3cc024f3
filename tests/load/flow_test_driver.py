import logging
import random
from typing import Any, Callable
from locust import task, constant
from websocket import WebSocket

from app.contracts.common.heart_beat import HeartBeat
from app.contracts.drivers.create_driver_request import CreateDriverSessionRequest
from app.sockets.messages.driver_heartbeat_message import DriverHeartbeatMessage
from app.sockets.messages.driver_session_start_message import DriverSessionStartMessage
from tests.load.common.auth_type import AuthType
from tests.load.users.socket_http_user import SocketHttpUser


class FlowTestDriver(SocketHttpUser):
    """
    Test groups of drivers connecting to the driver socket, starting a session and sending heartbeats
    """

    weight = 1
    heartbeat_interval_seconds = 5
    wait_time: Callable[[Any], float] = constant(heartbeat_interval_seconds)  # type: ignore

    def __connect_driver_socket(self) -> None:
        test_driver_id = f"+852{random.randint(10000000, 99999999)}TEST"
        self.connect(
            url=f"{FlowTestDriver.ws_host}/hailing/v1/drivers?test_id={test_driver_id}",
            jwt_token=FlowTestDriver.tokens[AuthType.DRIVER],
            name="/hailing/v1/drivers",
        )
        logging.info(f"Driver socket {test_driver_id} connected")

    def on_start(self) -> None:
        self.__connect_driver_socket()

    def on_socket_open(self, ws: WebSocket) -> None:
        super().on_socket_open(ws)
        self.send(
            DriverSessionStartMessage(
                payload=CreateDriverSessionRequest.create_example()
            )
        )

    @task(1)
    def driver_heart_beat_task(self) -> None:
        self.send(DriverHeartbeatMessage(payload=HeartBeat.create_example()))
