from locust import LoadTestShape


class SmokeLoad(LoadTestShape):
    total_duration = 30
    spawn_rate = 1
    max_users = 5

    def tick(self) -> tuple[int, int] | None:
        run_time: float = self.get_run_time()  # type: ignore

        if run_time < self.total_duration * 0.3:
            return (self.max_users, self.spawn_rate)
        elif run_time < self.total_duration * 0.6:
            return (self.max_users, self.spawn_rate)
        elif run_time < self.total_duration:
            return (0, self.spawn_rate)
        else:
            return None
