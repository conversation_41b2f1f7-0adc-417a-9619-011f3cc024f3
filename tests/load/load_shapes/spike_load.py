from locust import LoadTestShape


class SpikeLoad(LoadTestShape):
    total_duration = 1000
    spawn_rate = 5
    spike = 2000

    def tick(self) -> tuple[int, int] | None:
        run_time: float = self.get_run_time()  # type: ignore

        if run_time < self.total_duration * 0.15:
            return (100, self.spawn_rate)
        elif run_time < self.total_duration * 0.5:
            return (self.spike, self.spawn_rate)
        elif run_time < self.total_duration * 0.75:
            return (self.spike, self.spawn_rate)
        elif run_time < self.total_duration:
            return (0, self.spawn_rate)
        else:
            return None
