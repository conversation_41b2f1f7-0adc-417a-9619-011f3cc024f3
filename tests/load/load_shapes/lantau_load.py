import math
from locust import LoadTestShape


class LantauLoad(LoadTestShape):
    minutes = 5
    users_per_minute = 100
    total_duration = minutes * 60
    spawn_rate = math.ceil(users_per_minute / 60)

    def tick(self) -> tuple[int, int] | None:
        run_time: float = self.get_run_time()  # type: ignore

        if run_time < self.total_duration:
            current_minute = int(run_time // 60)
            users_to_spawn = int((run_time % 60) * self.spawn_rate)
            return (
                current_minute * self.users_per_minute + users_to_spawn,
                int(self.spawn_rate),
            )
        else:
            return None
