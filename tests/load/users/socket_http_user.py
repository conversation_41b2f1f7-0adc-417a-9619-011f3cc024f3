import json
import logging
import threading
import time
from typing import Any
from locust import HttpUser, events
from websocket import WebSocket

from app.sockets.messages.base_message import BaseMessage
from tests.load.common.auth_type import AuthType
from tests.load.common.testing_auth_service import TestingAuthService


class SocketHttpUser(HttpUser):
    """
    Locust user class for testing websocket connections.
    """

    # __proxies: list[str] = [
    #     "",
    #     "*************:443",
    #     "*************:4090",
    #     "**************:7777",
    # ]

    __ws: WebSocket | None = None

    ws_host: str | None = None
    """
    The websocket host to connect to.
    """

    abstract = True
    """
    This class is not meant to be instantiated directly by Locust.
    """

    tokens: dict[AuthType, str] = {}
    """
    Testing JWT tokens for the different auth types.
    """

    @events.init.add_listener
    def on_locust_init(environment: Any, **_kwargs) -> None:  # type: ignore
        SocketHttpUser.get_tokens()
        SocketHttpUser.ws_host = environment.parsed_options.ws_host

    @events.init_command_line_parser.add_listener
    def _(parser: Any) -> None:
        parser.add_argument(
            "--ws-host",
            type=str,
            default="",
            include_in_web_ui=True,
            help="The websocket host to connect to.",
        )

    @classmethod
    def get_tokens(cls) -> dict[AuthType, str]:
        """
        Get id token from refresh token and set it to the class variable
        """
        try:
            if len(cls.tokens) != len(AuthType):
                for auth_type in AuthType:
                    id_token = TestingAuthService.get_id_token_for_role(auth_type)
                    cls.tokens[auth_type] = id_token

            return cls.tokens

        except Exception as e:
            quit(f"Error getting tokens: {e}")

    def is_connected(self) -> bool:
        """
        Check if the websocket connection is established.
        """
        return self.__ws is not None and self.__ws.sock is not None

    def connect(
        self,
        url: str,
        jwt_token: str,
        max_retries: int = 3,
        initial_delay: float = 1.0,
        name: str | None = None,
    ) -> None:
        """
        Connect to the websocket. This method should typically be called in the
        on_start method if one long running connection should be used
        for the test.
        """
        import websocket

        retries = 0
        delay = initial_delay

        # proxy = random.choice(self.__proxies)
        # use_proxy = len(proxy) > 0

        # print(f"Proxy: {proxy}")

        while retries < max_retries:
            try:
                ws = websocket.WebSocketApp(
                    url,
                    header={
                        "Connection": "Upgrade",
                        "Authorization": f"Bearer {jwt_token}",
                    },
                    on_open=self.on_socket_open,
                    on_message=self.on_socket_message,
                    on_error=self.on_socket_error,
                    on_close=self.on_socket_close,
                )

                wst: threading.Thread
                # if use_proxy:
                #     wst = threading.Thread(
                #         target=lambda: ws.run_forever(
                #             http_proxy_host=proxy.split(":")[0] if use_proxy else "",
                #             http_proxy_port=(
                #                 int(proxy.split(":")[-1]) if use_proxy else ""
                #             ),
                #         )
                #     )
                # else:
                #     wst = threading.Thread(target=ws.run_forever)
                wst = threading.Thread(target=ws.run_forever)

                wst.daemon = True
                wst.start()

                self.environment.events.request.fire(
                    request_type="WSC",
                    name=name or url,
                    response_time=None,
                    response_length=0,
                    exception=None,
                    context=self.context(),
                )

                time.sleep(3)

                if self.is_connected():
                    return
                else:
                    raise Exception("WebSocket connection not established")

            except Exception as e:
                print(f"Connection attempt {retries + 1} failed: {e}")
                retries += 1
                time.sleep(delay)
                delay *= 2

    def disconnect(self) -> None:
        """
        Disconnect the websocket connection.
        """
        if self.__ws is not None:
            self.__ws.close()

    def send(self, message: BaseMessage) -> None:
        """
        Send a message over the websocket.
        """
        if self.__ws is None or not self.is_connected():
            logging.warning(
                "Unable to send message. Websocket connection not established."
            )
            return

        message_json = message.model_dump_json(by_alias=True)
        self.__ws.send(message_json)

        self.environment.events.request.fire(
            request_type="WSS",
            name=message.message,
            response_time=None,
            response_length=len(message_json),
            exception=None,
            context=self.context(),
        )

    def on_socket_message(self, _: WebSocket, message: Any) -> None:
        """
        Handle a message received from the websocket.
        """
        if message is None:
            raise Exception("Received empty message")

        message_dict: dict[Any, Any] = json.loads(message)
        name = message_dict.get("message")

        self.environment.events.request.fire(
            request_type="WSR",
            name=name,
            response_time=None,
            response_length=len(message),
            exception=None,
            context=self.context(),
        )

    def on_stop(self) -> None:
        """
        Close the websocket connection when the test stops.
        """
        if self.__ws is not None:
            self.__ws.close()

    def on_socket_error(self, _: WebSocket, error: Any) -> None:
        """
        Override to handle an error received from the websocket.
        """
        logging.error(f"WebSocket error: {error}")
        self.stop()

    def on_socket_open(self, ws: WebSocket) -> None:
        """
        Override to take actions on the opening of a websocket connection.
        """
        self.__ws = ws

    def on_socket_close(
        self, _: WebSocket, close_status_code: Any, close_msg: Any
    ) -> None:
        """
        Override to take actions on the closing of a websocket connection.
        """
        pass
