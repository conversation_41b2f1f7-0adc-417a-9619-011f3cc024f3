FROM python:3.12.5-slim-bullseye

# expose port 8080
EXPOSE 8080

# Set the working directory in the container
WORKDIR /app

# Install curl
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
    # deps for installing poetry
    curl \
    # deps for building python deps
    build-essential

ENV \
    PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    # Poetry's configuration:
    POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_CREATE=false \
    POETRY_CACHE_DIR='/var/cache/pypoetry' \
    POETRY_HOME='/usr/local' \
    POETRY_VERSION=1.7.1

ARG ACCESS_TOKEN

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Copy the pyproject.toml and poetry.lock files to the container
COPY poetry.lock pyproject.toml /app/

RUN poetry config http-basic.dash oauth2accesstoken $ACCESS_TOKEN

# Install project dependencies
RUN poetry install --no-root

# Copy the rest of the files to the container
COPY ./app /app/app

# Run the application
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]