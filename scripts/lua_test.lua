local function date_to_timestamp(date_str)
    local pattern = "(%d+)-(%d+)-(%d+)"
    local year, month, day = date_str:match(pattern)
    return os.time({ year = year, month = month, day = day })
end

local session_start_field_key = ARGV[1]
local start_date = date_to_timestamp(ARGV[2])
local end_date = date_to_timestamp(ARGV[3])
local date_sessions_by_driver = {}

for i = 1, #KEYS do
    local driver_session_id = KEYS[i]
    local driver_id = driver_session_id:match("([^:]+)$")
    local driver_session = redis.call("HGETALL", driver_session_id)

    if #driver_session > 0 then
        local driver_sessions_by_date = {}

        for j = 1, #driver_session, 2 do
            if driver_session[j] == session_start_field_key then
                do break end
            end

            local current_date = date_to_timestamp(driver_session[j])

            if current_date >= start_date and current_date <= end_date then
                driver_sessions_by_date[driver_session[j]] = driver_session[j + 1]
            end
        end

        if #driver_sessions_by_date > 0 then
            date_sessions_by_driver[driver_id] = driver_sessions_by_date
        end
    end
end

return cjson.encode(date_sessions_by_driver)
