[tool.poetry]
name = "hail-backend"
version = "0.1.0"
description = ""
authors = ["dash <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.112.0"
uvicorn = "^0.30.6"
pydantic-settings = "^2.4.0"
websockets = "^12.0"
shapely = "^2.0.5"
pydantic = "^2.8.2"
redis-om = "^0.3.2"
redis = "^5.0.8"
google-cloud-logging = "^3.11.1"
pyjwt = "^2.9.0"
panzi-json-logic = "^1.0.1"
apscheduler = "^3.10.4"
pyyaml = "^6.0.2"
httpx = "^0.27.0"
fastapi-controllers = "^0.3.0"
dependency-injector = "4.42.0b1"
scikit-learn = "^1.5.1"
geojson = "^3.1.0"
google-cloud-pubsub = "^2.23.0"
firebase-admin = "^6.5.0"
google-cloud-storage = "^2.18.2"
pytz = "^2024.2"
mypy = "^1.11.2"
types-pyyaml = "^6.0.12.20240917"
types-shapely = "^2.0.0.20240820"
locust = "^2.31.6"
websocket-client = "^1.8.0"
types-requests = "^2.32.0.20240914"
google-cloud-datastore = "^2.20.2"
sentry-sdk = {extras = ["fastapi"], version = "^2.18.0"}
types-pytz = "^2024.2.0.20241003"
aioredlock = "^0.7.3"
fastkml = "^1.1.0"
rtree = "^1.3.0"
polyline = "^2.0.2"
lxml = "^5.3.1"
dash-proto = "^1.0.22"
protobuf = "5.29.4"
types-protobuf = "^5.29.1.20250315"
pytest = "^8.4.1"


[tool.poetry.group.dev.dependencies]
keyring = "^25.6.0"
keyrings-google-artifactregistry-auth = "^1.1.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "dash"
url = "https://asia-east2-python.pkg.dev/dash-dev-81bb1/python-repository/simple"
priority = "supplemental"